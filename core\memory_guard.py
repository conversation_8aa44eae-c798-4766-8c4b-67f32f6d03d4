"""
Memory Guard Module
Monitors and manages system resources (CPU, RAM) to ensure bot runs efficiently
"""

import os
import psutil
import logging
import time
import threading
from datetime import datetime

# Set up logging
logger = logging.getLogger("MemoryGuard")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/system_resources.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class MemoryGuard:
    """
    Monitors and manages system resources (CPU, RAM)
    """

    def __init__(self, cpu_threshold=95, ram_threshold=95, check_interval=30, parent_bot=None):
        """
        Initialize the memory guard

        Args:
            cpu_threshold (int): CPU usage threshold percentage
            ram_threshold (int): RAM usage threshold percentage
            check_interval (int): Resource check interval in seconds
            parent_bot: Reference to the parent bot instance for emergency cleanup
        """
        self.cpu_threshold = cpu_threshold
        self.ram_threshold = ram_threshold
        self.check_interval = check_interval
        self.parent_bot = parent_bot

        # Initialize resource usage history
        self.usage_history = []
        self.max_history_size = 50  # Keep last 50 measurements to reduce memory usage

        # Initialize monitoring thread
        self.monitoring_active = False
        self.monitor_thread = None

        # Initialize alert callbacks
        self.alert_callbacks = []

        # Track consecutive high resource usage events
        self.consecutive_high_cpu = 0
        self.consecutive_high_ram = 0
        self.max_consecutive_threshold = 5  # After this many consecutive high usage events, take action

        # Initialize recovery state
        self.in_recovery_mode = False
        self.recovery_start_time = None
        self.recovery_cooldown = 300  # 5 minutes cooldown after recovery

        logger.info(f"MemoryGuard initialized with CPU threshold: {cpu_threshold}%, RAM threshold: {ram_threshold}%")

    def start_monitoring(self):
        """
        Start resource monitoring in a background thread
        """
        if self.monitoring_active:
            logger.warning("Resource monitoring is already active")
            return

        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitor_thread.start()

        logger.info("Resource monitoring started")

    def stop_monitoring(self):
        """
        Stop resource monitoring
        """
        if not self.monitoring_active:
            logger.warning("Resource monitoring is not active")
            return

        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        logger.info("Resource monitoring stopped")

    def _monitor_resources(self):
        """
        Monitor system resources in a loop
        """
        while self.monitoring_active:
            try:
                # Check if we're in recovery mode
                if self.in_recovery_mode:
                    current_time = datetime.now()
                    if (current_time - self.recovery_start_time).total_seconds() > self.recovery_cooldown:
                        logger.info("Exiting recovery mode after cooldown period")
                        self.in_recovery_mode = False
                        self.consecutive_high_cpu = 0
                        self.consecutive_high_ram = 0
                    else:
                        # In recovery mode, check less frequently
                        time.sleep(self.check_interval * 2)
                        continue

                # Get current resource usage
                cpu_usage = psutil.cpu_percent(interval=1)
                ram_usage = psutil.virtual_memory().percent

                # Record usage
                usage_data = {
                    'timestamp': datetime.now().isoformat(),
                    'cpu_usage': cpu_usage,
                    'ram_usage': ram_usage
                }

                self.usage_history.append(usage_data)

                # Trim history if needed
                if len(self.usage_history) > self.max_history_size:
                    self.usage_history = self.usage_history[-self.max_history_size:]

                # Check for threshold violations and track consecutive events
                cpu_threshold_exceeded = cpu_usage > self.cpu_threshold
                ram_threshold_exceeded = ram_usage > self.ram_threshold

                # Update consecutive counters
                if cpu_threshold_exceeded:
                    self.consecutive_high_cpu += 1
                    logger.warning(f"CPU usage ({cpu_usage}%) exceeds threshold ({self.cpu_threshold}%) - consecutive: {self.consecutive_high_cpu}")
                    self._trigger_alerts('cpu', cpu_usage)
                else:
                    self.consecutive_high_cpu = 0

                if ram_threshold_exceeded:
                    self.consecutive_high_ram += 1
                    logger.warning(f"RAM usage ({ram_usage}%) exceeds threshold ({self.ram_threshold}%) - consecutive: {self.consecutive_high_ram}")
                    self._trigger_alerts('ram', ram_usage)
                else:
                    self.consecutive_high_ram = 0

                # Take action if consecutive threshold violations exceed limit
                if self.consecutive_high_cpu >= self.max_consecutive_threshold:
                    logger.critical(f"CPU usage exceeded threshold for {self.consecutive_high_cpu} consecutive checks. Entering recovery mode.")
                    self._enter_recovery_mode()
                    self.optimize_resources()

                if self.consecutive_high_ram >= self.max_consecutive_threshold:
                    logger.critical(f"RAM usage exceeded threshold for {self.consecutive_high_ram} consecutive checks. Entering recovery mode.")
                    self._enter_recovery_mode()
                    self.force_memory_cleanup()

                # Check for critical resource usage (>98%)
                if cpu_usage > 98 or ram_usage > 98:
                    logger.critical(f"CRITICAL RESOURCE USAGE - CPU: {cpu_usage}%, RAM: {ram_usage}%. Forcing immediate cleanup.")
                    self._enter_recovery_mode()
                    self.force_memory_cleanup()

                # Log resource usage periodically
                if len(self.usage_history) % 10 == 0:
                    logger.info(f"Current resource usage - CPU: {cpu_usage}%, RAM: {ram_usage}%")

                # Sleep until next check
                time.sleep(self.check_interval)

            except Exception as e:
                logger.error(f"Error monitoring resources: {e}")
                time.sleep(self.check_interval)

    def _enter_recovery_mode(self):
        """
        Enter recovery mode to handle resource issues
        """
        self.in_recovery_mode = True
        self.recovery_start_time = datetime.now()
        logger.warning(f"Entering recovery mode at {self.recovery_start_time.isoformat()}")

        # Notify any registered callbacks
        for callback in self.alert_callbacks:
            try:
                callback('recovery_mode', True)
            except Exception as e:
                logger.error(f"Error in recovery mode callback: {e}")

    def _trigger_alerts(self, resource_type, usage):
        """
        Trigger alert callbacks

        Args:
            resource_type (str): Resource type ('cpu' or 'ram')
            usage (float): Current usage percentage
        """
        for callback in self.alert_callbacks:
            try:
                callback(resource_type, usage)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")

    def add_alert_callback(self, callback):
        """
        Add an alert callback function

        Args:
            callback (callable): Function to call when resource threshold is exceeded
        """
        self.alert_callbacks.append(callback)
        logger.info(f"Added alert callback: {callback.__name__}")

    def register_alert_callback(self, callback):
        """
        Register an alert callback function (alias for add_alert_callback)

        Args:
            callback (callable): Function to call when resource threshold is exceeded
        """
        self.add_alert_callback(callback)

    def get_current_usage(self):
        """
        Get current system resource usage

        Returns:
            dict: Current CPU and RAM usage
        """
        cpu_usage = psutil.cpu_percent(interval=0.5)
        ram_usage = psutil.virtual_memory().percent

        return {
            'cpu_usage': cpu_usage,
            'ram_usage': ram_usage,
            'timestamp': datetime.now().isoformat()
        }

    def get_current_cpu_usage(self):
        """
        Get current CPU usage

        Returns:
            float: Current CPU usage percentage
        """
        try:
            return psutil.cpu_percent(interval=0.5)
        except Exception as e:
            logger.error(f"Error getting CPU usage: {e}")
            return 0.0

    def get_current_ram_usage(self):
        """
        Get current RAM usage

        Returns:
            float: Current RAM usage percentage
        """
        try:
            return psutil.virtual_memory().percent
        except Exception as e:
            logger.error(f"Error getting RAM usage: {e}")
            return 0.0

    def get_usage_history(self):
        """
        Get resource usage history

        Returns:
            list: Resource usage history
        """
        return self.usage_history

    def get_average_usage(self, minutes=5):
        """
        Get average resource usage over a period

        Args:
            minutes (int): Time period in minutes

        Returns:
            dict: Average CPU and RAM usage
        """
        # Calculate timestamp threshold
        now = datetime.now()
        threshold = now.timestamp() - (minutes * 60)

        # Filter history by time
        recent_history = [
            entry for entry in self.usage_history
            if datetime.fromisoformat(entry['timestamp']).timestamp() > threshold
        ]

        if not recent_history:
            return {
                'cpu_average': 0,
                'ram_average': 0,
                'samples': 0
            }

        # Calculate averages
        cpu_sum = sum(entry['cpu_usage'] for entry in recent_history)
        ram_sum = sum(entry['ram_usage'] for entry in recent_history)
        count = len(recent_history)

        return {
            'cpu_average': cpu_sum / count,
            'ram_average': ram_sum / count,
            'samples': count
        }

    def optimize_resources(self):
        """
        Attempt to optimize resource usage

        Returns:
            bool: True if optimization was successful, False otherwise
        """
        try:
            # Get current process
            process = psutil.Process(os.getpid())

            # Lower process priority
            if hasattr(psutil, 'BELOW_NORMAL_PRIORITY_CLASS'):
                process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
            else:
                # Unix-like systems
                process.nice(10)

            # Aggressive garbage collection
            import gc
            gc.collect(generation=2)  # Force full collection

            # Clear memory cache if available
            if hasattr(self, 'usage_history') and len(self.usage_history) > 20:
                # Keep only the most recent entries
                self.usage_history = self.usage_history[-20:]

            # Close any unnecessary file handles
            for handler in logger.handlers:
                if isinstance(handler, logging.FileHandler):
                    handler.flush()

            logger.info("Resource optimization performed")
            return True

        except Exception as e:
            logger.error(f"Error optimizing resources: {e}")
            return False

    def force_memory_cleanup(self):
        """
        Force memory cleanup when RAM usage is critical

        Returns:
            bool: True if cleanup was successful, False otherwise
        """
        try:
            import gc
            import sys

            # Get current memory usage
            ram_usage = psutil.virtual_memory().percent
            logger.warning(f"Forcing memory cleanup. Current RAM usage: {ram_usage}%")

            # First, try to release file handles and close unnecessary resources
            self._release_file_handles()

            # Collect all generations with aggressive settings
            gc.collect(0)
            gc.collect(1)
            gc.collect(2)

            # Disable automatic garbage collection to manually control it
            gc.disable()

            # Clear any caches
            self.usage_history = self.usage_history[-5:] if len(self.usage_history) > 5 else []

            # Clear pandas cache if pandas is being used
            try:
                import pandas as pd
                if hasattr(pd, '_libs') and hasattr(pd._libs, 'hashtable'):
                    if hasattr(pd._libs.hashtable, '_clear_caches'):
                        pd._libs.hashtable._clear_caches()
                        logger.info("Cleared pandas caches")
            except (ImportError, AttributeError):
                pass

            # Clear numpy cache if numpy is being used
            try:
                import numpy as np
                np.clear_cache()
                logger.info("Cleared numpy caches")
            except (ImportError, AttributeError):
                pass

            # Clear any large objects in memory
            large_objects_cleared = 0
            for obj in gc.get_objects():
                try:
                    # Check for large collections (lists, dicts, sets)
                    if isinstance(obj, (list, dict, set)) and sys.getsizeof(obj) > 500000:  # Objects larger than ~500KB
                        if isinstance(obj, list):
                            # Keep only the last 10 items for large lists
                            if len(obj) > 10:
                                del obj[:-10]
                                large_objects_cleared += 1
                        elif isinstance(obj, dict):
                            # Keep only the last 10 items for large dicts
                            if len(obj) > 10:
                                keys_to_remove = list(obj.keys())[:-10]
                                for key in keys_to_remove:
                                    del obj[key]
                                large_objects_cleared += 1
                        elif isinstance(obj, set):
                            obj.clear()
                            large_objects_cleared += 1
                except Exception:
                    # Ignore errors when trying to clear objects
                    pass

            logger.info(f"Cleared {large_objects_cleared} large objects from memory")

            # Force another collection after clearing large objects
            gc.collect(2)

            # Re-enable garbage collection
            gc.enable()

            # Explicitly run Python's memory compaction
            try:
                import ctypes
                ctypes.pythonapi.PyGC_Collect()
                ctypes.pythonapi.PyObject_GC_Collect()
            except Exception:
                # Ignore if not available
                pass

            # Try to reduce memory fragmentation
            self._reduce_memory_fragmentation()

            # Get new memory usage
            new_ram_usage = psutil.virtual_memory().percent
            logger.info(f"Memory cleanup completed. RAM usage: {ram_usage}% → {new_ram_usage}%")

            # If memory usage is still critical, try more aggressive measures
            if new_ram_usage > 90:
                logger.warning("Memory usage still critical after cleanup. Taking more aggressive measures.")
                self._emergency_memory_cleanup()

                # Check if we need to restart any components
                if new_ram_usage > 95:
                    logger.critical("Memory usage extremely high. Attempting component restart.")
                    self._restart_components()

            return True

        except Exception as e:
            logger.error(f"Error during forced memory cleanup: {e}")
            return False

    def _release_file_handles(self):
        """
        Release file handles and close unnecessary resources
        """
        try:
            # Flush all file handlers in logging
            for handler in logging.root.handlers:
                if isinstance(handler, logging.FileHandler):
                    handler.flush()

            # Close and reopen log files to release file handles
            for handler in list(logging.root.handlers):
                if isinstance(handler, logging.FileHandler):
                    try:
                        filename = handler.baseFilename
                        handler.close()
                        new_handler = logging.FileHandler(filename)
                        new_handler.setFormatter(handler.formatter)
                        new_handler.setLevel(handler.level)
                        logging.root.removeHandler(handler)
                        logging.root.addHandler(new_handler)
                    except Exception as e:
                        logger.error(f"Error reopening log file: {e}")

            # Force Python to release memory back to the OS
            import gc
            gc.collect()

            logger.info("Released file handles and closed unnecessary resources")
        except Exception as e:
            logger.error(f"Error releasing file handles: {e}")

    def _reduce_memory_fragmentation(self):
        """
        Attempt to reduce memory fragmentation
        """
        try:
            # On Windows, try to use the Windows API to reduce working set
            if os.name == 'nt':
                try:
                    import ctypes
                    kernel32 = ctypes.windll.kernel32
                    kernel32.SetProcessWorkingSetSize(
                        ctypes.windll.kernel32.GetCurrentProcess(), -1, -1)
                    logger.info("Reduced working set size using Windows API")
                except Exception as e:
                    logger.error(f"Error reducing working set size: {e}")

            # Try to use malloc_trim if available (Linux)
            try:
                import ctypes
                try:
                    libc = ctypes.CDLL('libc.so.6')
                    if hasattr(libc, 'malloc_trim'):
                        libc.malloc_trim(0)
                        logger.info("Reduced memory fragmentation using malloc_trim")
                except Exception:
                    pass
            except Exception:
                pass

            logger.info("Attempted to reduce memory fragmentation")
        except Exception as e:
            logger.error(f"Error reducing memory fragmentation: {e}")

    def _restart_components(self):
        """
        Restart components to recover from extreme memory pressure
        """
        try:
            # Check if we have a parent bot reference
            if not hasattr(self, 'parent_bot') or self.parent_bot is None:
                logger.warning("No parent bot reference available for component restart")
                return

            # Restart components that might be causing memory leaks
            bot = self.parent_bot

            # Try to restart the margin monitor
            if hasattr(bot, 'margin_monitor'):
                try:
                    logger.warning("Restarting margin monitor")
                    bot.margin_monitor.stop_monitoring()
                    time.sleep(1)
                    bot.margin_monitor.start_monitoring()
                    logger.info("Margin monitor restarted successfully")
                except Exception as e:
                    logger.error(f"Error restarting margin monitor: {e}")

            # Try to restart any AI services
            if hasattr(bot, 'ai_connector'):
                try:
                    logger.warning("Resetting AI connector")
                    if hasattr(bot.ai_connector, 'reset'):
                        bot.ai_connector.reset()
                        logger.info("AI connector reset successfully")
                except Exception as e:
                    logger.error(f"Error resetting AI connector: {e}")

            # Force garbage collection after component restarts
            import gc
            gc.collect()

            logger.info("Component restart completed")
        except Exception as e:
            logger.error(f"Error during component restart: {e}")
            return False

    def _emergency_memory_cleanup(self):
        """
        Emergency memory cleanup when normal cleanup is not sufficient
        """
        try:
            import gc
            import sys

            # Get all objects tracked by the garbage collector
            gc.collect()
            objects = gc.get_objects()

            # Log memory usage of largest objects
            logger.warning("Top 10 largest objects in memory:")
            objects_with_size = [(obj, sys.getsizeof(obj)) for obj in objects if hasattr(obj, '__sizeof__')]
            objects_with_size.sort(key=lambda x: x[1], reverse=True)

            for i, (obj, size) in enumerate(objects_with_size[:10]):
                logger.warning(f"Object {i+1}: {type(obj)} - {size/1024/1024:.2f} MB")

            # Clear all module-level caches
            for _, module in sys.modules.items():
                # Clear function caches
                if hasattr(module, 'cache_clear'):
                    try:
                        module.cache_clear()
                    except Exception:
                        pass

                # Clear lru_cache for all functions in the module
                for attr_name in dir(module):
                    try:
                        attr = getattr(module, attr_name)
                        if hasattr(attr, 'cache_clear') and callable(attr.cache_clear):
                            attr.cache_clear()
                    except Exception:
                        pass

            # Clear pandas cache if pandas is being used
            try:
                import pandas as pd
                # Clear all pandas caches
                for cache_name in ['_indexers_cache', '_item_cache', '_cython_table', '_chunksize_cache']:
                    if hasattr(pd, cache_name):
                        getattr(pd, cache_name).clear()
            except (ImportError, AttributeError):
                pass

            # Clear matplotlib cache if matplotlib is being used
            try:
                import matplotlib
                matplotlib.pyplot.close('all')
                if hasattr(matplotlib, '_pylab_helpers'):
                    getattr(matplotlib, '_pylab_helpers').Gcf.figs.clear()
                logger.info("Cleared matplotlib figures")
            except (ImportError, AttributeError):
                pass

            # Clear any large lists or dictionaries in the bot's namespace
            if hasattr(self, 'parent_bot'):
                bot = self.parent_bot
                cleared_count = 0
                for attr_name in dir(bot):
                    try:
                        # Skip special attributes and methods
                        if attr_name.startswith('__'):
                            continue

                        attr = getattr(bot, attr_name)

                        # Handle large collections
                        if isinstance(attr, (list, dict, set)) and sys.getsizeof(attr) > 100000:  # Lower threshold for emergency cleanup
                            if isinstance(attr, list):
                                # Keep only the last 5 items for large lists in emergency mode
                                if len(attr) > 5:
                                    del attr[:-5]
                                    cleared_count += 1
                            elif isinstance(attr, dict):
                                # Keep only the last 5 items for large dicts in emergency mode
                                if len(attr) > 5:
                                    keys_to_remove = list(attr.keys())[:-5]
                                    for key in keys_to_remove:
                                        del attr[key]
                                    cleared_count += 1
                            elif isinstance(attr, set):
                                # Clear large sets completely
                                attr.clear()
                                cleared_count += 1

                        # Handle dataframes
                        elif 'pandas.core.frame.DataFrame' in str(type(attr)):
                            try:
                                # For DataFrames, try to reduce memory usage
                                if hasattr(attr, 'memory_usage') and attr.memory_usage().sum() > 1000000:  # >1MB
                                    # Try to convert to more memory-efficient types
                                    for col in attr.columns:
                                        if attr[col].dtype == 'float64':
                                            attr[col] = attr[col].astype('float32')
                                        elif attr[col].dtype == 'int64':
                                            attr[col] = attr[col].astype('int32')
                                    cleared_count += 1
                            except Exception:
                                pass
                    except Exception:
                        pass

                logger.info(f"Cleared or optimized {cleared_count} large objects in bot namespace")

                # Clear historical data caches
                if hasattr(bot, 'historical_data_loader') and hasattr(bot.historical_data_loader, 'clear_cache'):
                    try:
                        bot.historical_data_loader.clear_cache()
                        logger.info("Cleared historical data cache")
                    except Exception as e:
                        logger.error(f"Error clearing historical data cache: {e}")

            # Force another collection with aggressive settings
            gc.collect(2)

            # Try to compact memory
            self._compact_memory()

            # Get new memory usage
            new_ram_usage = psutil.virtual_memory().percent
            logger.info(f"Emergency memory cleanup completed. New RAM usage: {new_ram_usage}%")

            return True

        except Exception as e:
            logger.error(f"Error during emergency memory cleanup: {e}")
            return False

    def _compact_memory(self):
        """
        Try to compact memory and reduce fragmentation
        """
        try:
            # Windows-specific memory compaction
            if os.name == 'nt':
                try:
                    import ctypes
                    # Empty working set to release memory back to the OS
                    ctypes.windll.psapi.EmptyWorkingSet(ctypes.windll.kernel32.GetCurrentProcess())
                    logger.info("Compacted memory using Windows EmptyWorkingSet")
                except Exception as e:
                    logger.error(f"Error compacting memory with Windows API: {e}")

            # Force garbage collection on all platforms
            import gc
            gc.collect(0)  # Collect youngest generation
            gc.collect(1)  # Collect middle generation
            gc.collect(2)  # Collect oldest generation

            # Try to reduce memory fragmentation
            try:
                # On Windows, try to reduce working set size
                if os.name == 'nt':
                    import ctypes
                    kernel32 = ctypes.windll.kernel32
                    kernel32.SetProcessWorkingSetSize(
                        kernel32.GetCurrentProcess(), -1, -1)
            except Exception:
                pass

            logger.info("Memory compaction completed")
            return True
        except Exception as e:
            logger.error(f"Error during memory compaction: {e}")
            return False

    def is_system_healthy(self):
        """
        Check if system resources are within healthy limits

        Returns:
            bool: True if system is healthy, False otherwise
        """
        usage = self.get_current_usage()

        cpu_healthy = usage['cpu_usage'] < self.cpu_threshold
        ram_healthy = usage['ram_usage'] < self.ram_threshold

        if not cpu_healthy:
            logger.warning(f"System CPU usage is unhealthy: {usage['cpu_usage']}%")

        if not ram_healthy:
            logger.warning(f"System RAM usage is unhealthy: {usage['ram_usage']}%")

        return cpu_healthy and ram_healthy

    def check_system_health(self):
        """
        Check system health with circuit breaker pattern

        Returns:
            bool: True if system is healthy, False otherwise
        """
        # Get current usage
        usage = self.get_current_usage()

        # Check if system is healthy
        is_healthy = self.is_system_healthy()

        # If not healthy, try to optimize resources
        if not is_healthy:
            logger.warning(f"System health check failed - CPU: {usage['cpu_usage']}%, RAM: {usage['ram_usage']}%")

            # Check if RAM usage is critical (>90%)
            if usage['ram_usage'] > 90:
                logger.warning("RAM usage is critical, forcing memory cleanup")
                self.force_memory_cleanup()
            else:
                # Try regular optimization
                logger.info("Attempting resource optimization")
                self.optimize_resources()

            # Check again after optimization
            is_healthy = self.is_system_healthy()

            if is_healthy:
                logger.info("System health restored after optimization")
            else:
                # If still unhealthy, check if we can proceed anyway
                # Allow operation if CPU is healthy even if RAM is high
                cpu_healthy = usage['cpu_usage'] < self.cpu_threshold
                ram_critical = usage['ram_usage'] > 95  # Only block if RAM is extremely high

                if cpu_healthy and not ram_critical:
                    logger.warning("Proceeding despite high RAM usage (CPU is healthy)")
                    return True

        return is_healthy

    def check_api_health(self, api_client, max_retries=3, retry_delay=2):
        """
        Check API health with retry mechanism

        Args:
            api_client: API client to check
            max_retries (int): Maximum number of retry attempts
            retry_delay (int): Delay between retries in seconds

        Returns:
            bool: True if API is healthy, False otherwise
        """
        for attempt in range(1, max_retries + 1):
            try:
                # Basic API health check - just verify we can get a ticker
                if hasattr(api_client, 'get_ticker'):
                    ticker = api_client.get_ticker('BTC/USDT')
                    if ticker and 'last' in ticker:
                        if attempt > 1:
                            logger.info(f"API health check passed on attempt {attempt}/{max_retries}")
                        else:
                            logger.info("API health check passed")
                        return True

                # If we can't get a ticker, try alternative methods
                if hasattr(api_client, 'fetch_ticker'):
                    ticker = api_client.fetch_ticker('BTC/USDT')
                    if ticker and ('last' in ticker or 'close' in ticker):
                        logger.info("API health check passed using fetch_ticker")
                        return True

                # Try getting server time as a last resort
                if hasattr(api_client, 'get_server_time'):
                    server_time = api_client.get_server_time()
                    if server_time:
                        logger.info("API health check passed using server time")
                        return True

                # If client doesn't have any of these methods, assume it's healthy
                if not any(hasattr(api_client, method) for method in ['get_ticker', 'fetch_ticker', 'get_server_time']):
                    logger.info("API health check skipped (no suitable methods available)")
                    return True

                # If we get here, the API check failed but we might retry
                if attempt < max_retries:
                    logger.warning(f"API health check failed on attempt {attempt}/{max_retries}. Retrying in {retry_delay}s...")
                    time.sleep(retry_delay)

            except Exception as e:
                if attempt < max_retries:
                    logger.warning(f"API health check failed on attempt {attempt}/{max_retries}: {e}. Retrying in {retry_delay}s...")
                    time.sleep(retry_delay)
                else:
                    logger.warning(f"API health check failed after {max_retries} attempts: {e}")
                    return False

        # If we've exhausted all retries, check if the system is under high load
        # If so, we might want to proceed anyway to avoid false negatives
        usage = self.get_current_usage()
        if usage['cpu_usage'] > self.cpu_threshold * 0.9:  # CPU usage is close to threshold
            logger.warning("API health check failed, but system is under high load. Proceeding with caution.")
            return True

        return False
