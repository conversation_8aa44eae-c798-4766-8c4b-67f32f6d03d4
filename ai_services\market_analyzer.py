"""
Market analyzer using multiple AI services
"""

import logging
import datetime
from .ai_connector import MultiAIConnector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIMarketAnalyzer:
    """
    Market analyzer using multiple AI services
    """

    def __init__(self, key_manager=None):
        """
        Initialize the market analyzer

        Args:
            key_manager: KeyManager instance for secure API key management
        """
        self.key_manager = key_manager
        self.ai_connector = MultiAIConnector(key_manager=key_manager)

        # Configure confidence thresholds
        self.confidence_threshold = 0.7  # 70% confidence required for a signal
        self.min_sources = 2  # At least 2 sources must agree

        # Initialize model evaluator
        from ai_services.model_evaluator import AIModelEvaluator
        self.model_evaluator = AIModelEvaluator(base_weights={
            "openai": 0.30,
            "deepseek": 0.35,
            "qwen": 0.35
        })

        # Get weights from model evaluator
        self.weights = self.model_evaluator.get_current_weights()

        logger.info("AIMarketAnalyzer initialized with secure key management")

    def analyze_market(self, market_data):
        """
        Analyze market data using multiple AI services

        Args:
            market_data (dict): Market data to analyze

        Returns:
            dict: Aggregated signal
        """
        logger.info("Starting market analysis with multiple AI services")

        # Get signals from all AI services
        signals = self.ai_connector.analyze_market(market_data)

        # Generate discussion between AI models
        discussion = self.ai_connector.generate_discussion(signals)
        logger.info(f"AI Discussion:\n{discussion}")

        # Aggregate signals
        aggregated_signal = self._aggregate_signals(signals, market_data)

        # Add discussion to aggregated signal
        aggregated_signal["discussion"] = discussion

        return aggregated_signal

    def _aggregate_signals(self, signals, market_data):
        """
        Aggregate signals from multiple sources

        Args:
            signals (dict): Signals from multiple sources
            market_data (dict): Market data

        Returns:
            dict: Aggregated signal
        """
        # Check if models disagree
        recommendations_set = set()
        for source in signals:
            if "analysis" in signals[source]:
                recommendation = signals[source]["analysis"].get("recommendation", "hold").lower()
                recommendations_set.add(recommendation)

        # If models disagree, use debate mode weights
        if len(recommendations_set) > 1 and len(signals) > 1:
            logger.info("Models disagree. Using debate mode weights.")
            debate_weights = self.model_evaluator.get_debate_weights(signals)
            self.weights = debate_weights
        else:
            # Use current weights from model evaluator
            self.weights = self.model_evaluator.get_current_weights()

        # Count recommendations
        recommendations = {"buy": 0, "sell": 0, "hold": 0}
        confidence_sum = 0
        source_count = 0

        # Process AI signals
        for source in ["openai", "deepseek", "qwen"]:
            if source in signals and "analysis" in signals[source]:
                analysis = signals[source]["analysis"]
                recommendation = analysis.get("recommendation", "hold").lower()
                confidence = analysis.get("confidence", 50) / 100.0  # Convert to 0-1 scale

                # Add weighted recommendation
                recommendations[recommendation] += self.weights[source] * confidence
                confidence_sum += self.weights[source] * confidence
                source_count += 1

                # Record prediction for evaluation
                self.model_evaluator.record_prediction(
                    source,
                    recommendation,
                    confidence
                )

        # Normalize recommendations
        if confidence_sum > 0:
            for rec in recommendations:
                recommendations[rec] /= confidence_sum

        # Determine final recommendation
        final_recommendation = max(recommendations, key=recommendations.get)
        final_confidence = recommendations[final_recommendation]

        # Check if confidence threshold is met
        if final_confidence < self.confidence_threshold or source_count < self.min_sources:
            final_recommendation = "hold"
            reason = f"Insufficient confidence ({final_confidence:.2f}) or sources ({source_count})"
        else:
            reason = f"Aggregated from {source_count} sources with {final_confidence:.2f} confidence"

        # Verify predictions when we have a final recommendation
        if final_recommendation != "hold":
            self.model_evaluator.verify_latest_predictions(final_recommendation)

        # Create final signal
        symbol = market_data.get("symbol", "BTC/USDT")
        current_price = market_data.get("price", 0)

        aggregated_signal = {
            "symbol": symbol,
            "recommendation": final_recommendation,
            "confidence": final_confidence,
            "price": current_price,
            "timestamp": datetime.datetime.now().isoformat(),
            "reason": reason,
            "source_count": source_count,
            "recommendations": recommendations,
            "signals": signals
        }

        logger.info(f"Aggregated signal: {final_recommendation} with {final_confidence:.2f} confidence")
        return aggregated_signal
