"""
Market Analyzer Module
Analyzes market data and generates trading signals
"""

import os
import logging
import datetime
import numpy as np
from analysis.indicators import (
    calculate_ema, calculate_ema_slope, is_ema_flat, calculate_rsi, calculate_macd,
    calculate_atr, is_bullish_macd_crossover, is_bearish_macd_crossover,
    is_rsi_breaking_up, is_rsi_breaking_down, is_volume_above_average,
    is_atr_high, is_false_candle, calculate_trade_quality_score, is_low_liquidity_time
)

# Set up logging
logger = logging.getLogger("market_analyzer")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/market_analyzer.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class MarketAnalyzer:
    """
    Analyzes market data and generates trading signals
    """
    
    def __init__(self, min_score=70):
        """
        Initialize the market analyzer
        
        Args:
            min_score (float): Minimum trade quality score (0-100)
        """
        self.min_score = min_score
        logger.info(f"MarketAnalyzer initialized with minimum score: {min_score}")
    
    def analyze_market(self, symbol, ohlcv_data):
        """
        Analyze market data and generate trading signal
        
        Args:
            symbol (str): Trading pair symbol
            ohlcv_data (list): List of OHLCV data
            
        Returns:
            dict: Analysis result
        """
        try:
            # Extract OHLCV data
            timestamps = [candle.get('timestamp') for candle in ohlcv_data]
            open_prices = [candle.get('open') for candle in ohlcv_data]
            high_prices = [candle.get('high') for candle in ohlcv_data]
            low_prices = [candle.get('low') for candle in ohlcv_data]
            close_prices = [candle.get('close') for candle in ohlcv_data]
            volumes = [candle.get('volume') for candle in ohlcv_data]
            
            # Calculate indicators
            ema50 = calculate_ema(close_prices, 50)
            ema200 = calculate_ema(close_prices, 200)
            ema50_slope = calculate_ema_slope(ema50)
            is_ema50_flat_result = is_ema_flat(ema50)
            
            rsi_values = calculate_rsi(close_prices)
            macd_line, signal_line, histogram = calculate_macd(close_prices)
            atr_values = calculate_atr(high_prices, low_prices, close_prices)
            
            # Check trend conditions
            ema50_above_ema200 = len(ema50) > 0 and len(ema200) > 0 and ema50[-1] > ema200[-1]
            ema50_below_ema200 = len(ema50) > 0 and len(ema200) > 0 and ema50[-1] < ema200[-1]
            ema50_sloping_up = ema50_slope > 0
            ema50_sloping_down = ema50_slope < 0
            
            # Check entry conditions
            rsi_breaking_up = is_rsi_breaking_up(rsi_values)
            rsi_breaking_down = is_rsi_breaking_down(rsi_values)
            bullish_macd_crossover = is_bullish_macd_crossover(macd_line, signal_line, histogram)
            bearish_macd_crossover = is_bearish_macd_crossover(macd_line, signal_line, histogram)
            volume_above_average = is_volume_above_average(volumes)
            atr_high_result = is_atr_high(atr_values)
            false_candle_result = is_false_candle(open_prices, high_prices, low_prices, close_prices)
            low_liquidity_time_result = is_low_liquidity_time()
            
            # Check buy conditions
            buy_conditions = {
                'ema50_above_ema200': ema50_above_ema200,
                'ema50_sloping_up': ema50_sloping_up,
                'rsi_breaking_up': rsi_breaking_up,
                'bullish_macd_crossover': bullish_macd_crossover,
                'volume_above_average': volume_above_average,
                'atr_not_high': not atr_high_result,
                'not_false_candle': not false_candle_result,
                'not_low_liquidity_time': not low_liquidity_time_result
            }
            
            # Check sell conditions
            sell_conditions = {
                'ema50_below_ema200': ema50_below_ema200,
                'ema50_sloping_down': ema50_sloping_down,
                'rsi_breaking_down': rsi_breaking_down,
                'bearish_macd_crossover': bearish_macd_crossover,
                'volume_above_average': volume_above_average,
                'atr_not_high': not atr_high_result,
                'not_false_candle': not false_candle_result,
                'not_low_liquidity_time': not low_liquidity_time_result
            }
            
            # Calculate scores
            buy_score = calculate_trade_quality_score(buy_conditions)
            sell_score = calculate_trade_quality_score(sell_conditions)
            
            # Determine signal
            signal = 'hold'
            score = 0
            
            if not is_ema50_flat_result:  # Ignore trading if EMA50 is flat
                if buy_score >= self.min_score and buy_score > sell_score:
                    signal = 'buy'
                    score = buy_score
                elif sell_score >= self.min_score and sell_score > buy_score:
                    signal = 'sell'
                    score = sell_score
            
            # Create result
            result = {
                'symbol': symbol,
                'timestamp': datetime.datetime.now().isoformat(),
                'signal': signal,
                'score': score,
                'current_price': close_prices[-1] if close_prices else 0,
                'indicators': {
                    'ema50': ema50[-1] if ema50 else None,
                    'ema200': ema200[-1] if ema200 else None,
                    'ema50_slope': ema50_slope,
                    'is_ema50_flat': is_ema50_flat_result,
                    'rsi': rsi_values[-1] if rsi_values else None,
                    'macd': macd_line[-1] if macd_line else None,
                    'signal': signal_line[-1] if signal_line else None,
                    'histogram': histogram[-1] if histogram else None,
                    'atr': atr_values[-1] if atr_values else None,
                    'is_atr_high': atr_high_result,
                    'is_false_candle': false_candle_result,
                    'is_low_liquidity_time': low_liquidity_time_result
                },
                'conditions': {
                    'buy': buy_conditions,
                    'sell': sell_conditions
                },
                'scores': {
                    'buy': buy_score,
                    'sell': sell_score
                },
                'market_data': {
                    'open_prices': open_prices,
                    'high_prices': high_prices,
                    'low_prices': low_prices,
                    'close_prices': close_prices,
                    'volumes': volumes,
                    'timestamps': timestamps,
                    'rsi_values': rsi_values,
                    'macd_line': macd_line,
                    'signal_line': signal_line,
                    'histogram': histogram,
                    'atr_values': atr_values
                }
            }
            
            logger.info(f"Analysis for {symbol}: signal={signal}, score={score:.2f}, buy_score={buy_score:.2f}, sell_score={sell_score:.2f}")
            
            return result
        except Exception as e:
            logger.error(f"Error analyzing market: {e}")
            return {
                'symbol': symbol,
                'timestamp': datetime.datetime.now().isoformat(),
                'signal': 'hold',
                'score': 0,
                'error': str(e)
            }
