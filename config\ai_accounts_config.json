{"ai_accounts": {"openai": {"service_name": "openai", "base_url": "https://api.openai.com/v1", "model": "gpt-4", "accounts": [{"account_id": "openai_account_1", "api_key": "OPENAI_API_KEY_1", "daily_limit": 10000, "rate_limit": 60, "priority": 1, "active": true}, {"account_id": "openai_account_2", "api_key": "OPENAI_API_KEY_2", "daily_limit": 10000, "rate_limit": 60, "priority": 2, "active": true}, {"account_id": "openai_account_3", "api_key": "OPENAI_API_KEY_3", "daily_limit": 10000, "rate_limit": 60, "priority": 3, "active": true}]}, "deepseek": {"service_name": "deepseek", "base_url": "https://api.deepseek.com/v1", "model": "deepseek-chat", "accounts": [{"account_id": "deepseek_account_1", "api_key": "DEEPSEEK_API_KEY_1", "daily_limit": 15000, "rate_limit": 100, "priority": 1, "active": true}, {"account_id": "deepseek_account_2", "api_key": "DEEPSEEK_API_KEY_2", "daily_limit": 15000, "rate_limit": 100, "priority": 2, "active": true}, {"account_id": "deepseek_account_3", "api_key": "DEEPSEEK_API_KEY_3", "daily_limit": 15000, "rate_limit": 100, "priority": 3, "active": true}]}, "qwen": {"service_name": "qwen", "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1", "model": "qwen-turbo", "accounts": [{"account_id": "qwen_account_1", "api_key": "QWEN_API_KEY_1", "daily_limit": 20000, "rate_limit": 120, "priority": 1, "active": true}, {"account_id": "qwen_account_2", "api_key": "QWEN_API_KEY_2", "daily_limit": 20000, "rate_limit": 120, "priority": 2, "active": true}, {"account_id": "qwen_account_3", "api_key": "QWEN_API_KEY_3", "daily_limit": 20000, "rate_limit": 120, "priority": 3, "active": true}]}}, "rotation_settings": {"enable_rotation": true, "rotation_strategy": "round_robin", "fallback_enabled": true, "health_check_interval": 300, "error_threshold": 3, "cooldown_period": 1800}, "weight_settings": {"base_weights": {"openai": 0.3, "deepseek": 0.35, "qwen": 0.35}, "dynamic_adjustment": {"enabled": true, "max_adjustment": 0.1, "performance_window": 20, "update_frequency": "daily"}}, "security_settings": {"encrypt_keys": true, "key_rotation_enabled": true, "rotation_interval_days": 30, "audit_logging": true, "rate_limit_enforcement": true}}