info = {
    "name": "rm",
    "date_order": "DM<PERSON>",
    "january": [
        "schan",
        "schaner"
    ],
    "february": [
        "favr",
        "favrer"
    ],
    "march": [
        "mars"
    ],
    "april": [
        "avr",
        "avrigl"
    ],
    "may": [
        "matg"
    ],
    "june": [
        "zercl",
        "zercladur"
    ],
    "july": [
        "fan",
        "fanadur"
    ],
    "august": [
        "avust"
    ],
    "september": [
        "sett",
        "settember"
    ],
    "october": [
        "oct",
        "october"
    ],
    "november": [
        "nov",
        "november"
    ],
    "december": [
        "dec",
        "december"
    ],
    "monday": [
        "gli",
        "glindesdi"
    ],
    "tuesday": [
        "ma",
        "mardi"
    ],
    "wednesday": [
        "me",
        "mesemna"
    ],
    "thursday": [
        "gie",
        "gievgia"
    ],
    "friday": [
        "ve",
        "venderdi"
    ],
    "saturday": [
        "so",
        "sonda"
    ],
    "sunday": [
        "du",
        "dumengia"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "onn"
    ],
    "month": [
        "mais"
    ],
    "week": [
        "emna"
    ],
    "day": [
        "tag"
    ],
    "hour": [
        "ura"
    ],
    "minute": [
        "minuta"
    ],
    "second": [
        "secunda"
    ],
    "relative-type": {
        "0 day ago": [
            "oz"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week"
        ],
        "0 year ago": [
            "this year"
        ],
        "1 day ago": [
            "ier"
        ],
        "1 month ago": [
            "last month"
        ],
        "1 week ago": [
            "last week"
        ],
        "1 year ago": [
            "last year"
        ],
        "in 1 day": [
            "damaun"
        ],
        "in 1 month": [
            "next month"
        ],
        "in 1 week": [
            "next week"
        ],
        "in 1 year": [
            "next year"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
