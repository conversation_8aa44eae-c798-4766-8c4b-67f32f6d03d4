"""
AI Connector for multiple AI services with account rotation and fallback logic
"""

import os
import json
import logging
import datetime
import requests
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIServiceAccount:
    """
    Represents an AI service account with API key and usage tracking
    """

    def __init__(self, service_name, account_id, api_key, api_base=None, model=None):
        """
        Initialize an AI service account

        Args:
            service_name (str): Name of the AI service (openai, deepseek, qwen)
            account_id (str): Identifier for this account (e.g., "account1")
            api_key (str): API key for this account
            api_base (str, optional): Base URL for API requests
            model (str, optional): Model to use for this account
        """
        self.service_name = service_name
        self.account_id = account_id
        self.api_key = api_key
        self.api_base = api_base
        self.model = model

        # Usage tracking
        self.is_active = True
        self.last_used = None
        self.error_count = 0
        self.total_requests = 0
        self.total_tokens = 0

    def mark_used(self, tokens_used=0):
        """
        Mark this account as used

        Args:
            tokens_used (int): Number of tokens used in this request
        """
        self.last_used = datetime.datetime.now()
        self.total_requests += 1
        self.total_tokens += tokens_used

    def mark_error(self):
        """
        Mark this account as having an error
        """
        self.error_count += 1

    def disable(self):
        """
        Disable this account (e.g., when API key expires)
        """
        self.is_active = False
        logger.warning(f"{self.service_name} account {self.account_id} has been disabled")

    def __str__(self):
        return f"{self.service_name}-{self.account_id} (active: {self.is_active}, errors: {self.error_count})"

class AIServiceManager:
    """
    Manages multiple AI service accounts with rotation and fallback logic
    """

    def __init__(self, key_manager=None):
        """
        Initialize the AI service manager

        Args:
            key_manager: KeyManager instance for secure API key management
        """
        # Load environment variables
        load_dotenv()

        # Store key manager
        self.key_manager = key_manager

        # Initialize account lists for each service
        self.openai_accounts = []
        self.deepseek_accounts = []
        self.qwen_accounts = []

        # Load accounts from environment variables or key manager
        self._load_accounts()

        # Current account indices
        self.current_openai_idx = 0
        self.current_deepseek_idx = 0
        self.current_qwen_idx = 0

        logger.info(f"AIServiceManager initialized with {len(self.openai_accounts)} OpenAI accounts, "
                   f"{len(self.deepseek_accounts)} DeepSeek accounts, and {len(self.qwen_accounts)} Qwen accounts")

    def _load_accounts(self):
        """
        Load AI service accounts from environment variables
        """
        # Load OpenAI accounts
        for i in range(1, 4):  # Accounts 1-3
            api_key = os.getenv(f"OPENAI_API_KEY_{i}")
            if api_key:
                model = os.getenv(f"OPENAI_MODEL_{i}", "gpt-4")
                account = AIServiceAccount("openai", f"account{i}", api_key, model=model)
                self.openai_accounts.append(account)

        # Load DeepSeek accounts
        for i in range(1, 4):  # Accounts 1-3
            api_key = os.getenv(f"DEEPSEEK_API_KEY_{i}")
            if api_key:
                api_base = os.getenv(f"DEEPSEEK_API_BASE_{i}", "https://api.deepseek.com/v1")
                model = os.getenv(f"DEEPSEEK_MODEL_{i}", "deepseek-chat")
                account = AIServiceAccount("deepseek", f"account{i}", api_key, api_base=api_base, model=model)
                self.deepseek_accounts.append(account)

        # Load Qwen accounts
        for i in range(1, 4):  # Accounts 1-3
            api_key = os.getenv(f"QWEN_API_KEY_{i}")
            if api_key:
                api_base = os.getenv(f"QWEN_API_BASE_{i}", "https://api.qwen.ai/v1")
                model = os.getenv(f"QWEN_MODEL_{i}", "qwen-max")
                account = AIServiceAccount("qwen", f"account{i}", api_key, api_base=api_base, model=model)
                self.qwen_accounts.append(account)

    def get_next_openai_account(self):
        """
        Get the next available OpenAI account with fallback to Qwen

        Returns:
            AIServiceAccount: Next available account
        """
        # Try all OpenAI accounts
        for _ in range(len(self.openai_accounts)):
            account = self.openai_accounts[self.current_openai_idx]
            self.current_openai_idx = (self.current_openai_idx + 1) % max(1, len(self.openai_accounts))

            if account.is_active:
                return account

        # Fallback to first Qwen account
        if self.qwen_accounts and self.qwen_accounts[0].is_active:
            logger.warning("All OpenAI accounts are disabled. Falling back to Qwen account.")
            return self.qwen_accounts[0]

        # No available accounts
        logger.error("No available OpenAI or fallback Qwen accounts")
        return None

    def get_next_deepseek_account(self):
        """
        Get the next available DeepSeek account with fallback to Qwen

        Returns:
            AIServiceAccount: Next available account
        """
        # Try all DeepSeek accounts
        for _ in range(len(self.deepseek_accounts)):
            account = self.deepseek_accounts[self.current_deepseek_idx]
            self.current_deepseek_idx = (self.current_deepseek_idx + 1) % max(1, len(self.deepseek_accounts))

            if account.is_active:
                return account

        # Fallback to second Qwen account
        if len(self.qwen_accounts) >= 2 and self.qwen_accounts[1].is_active:
            logger.warning("All DeepSeek accounts are disabled. Falling back to Qwen account.")
            return self.qwen_accounts[1]

        # No available accounts
        logger.error("No available DeepSeek or fallback Qwen accounts")
        return None

    def get_next_qwen_account(self):
        """
        Get the next available Qwen account

        Returns:
            AIServiceAccount: Next available account
        """
        # Try all Qwen accounts
        for _ in range(len(self.qwen_accounts)):
            account = self.qwen_accounts[self.current_qwen_idx]
            self.current_qwen_idx = (self.current_qwen_idx + 1) % max(1, len(self.qwen_accounts))

            if account.is_active:
                return account

        # No available accounts
        logger.error("No available Qwen accounts")
        return None

    def get_account_status(self):
        """
        Get status of all accounts

        Returns:
            dict: Account status information
        """
        status = {
            "openai": [{"id": acc.account_id, "active": acc.is_active, "errors": acc.error_count}
                      for acc in self.openai_accounts],
            "deepseek": [{"id": acc.account_id, "active": acc.is_active, "errors": acc.error_count}
                        for acc in self.deepseek_accounts],
            "qwen": [{"id": acc.account_id, "active": acc.is_active, "errors": acc.error_count}
                    for acc in self.qwen_accounts]
        }
        return status

class MultiAIConnector:
    """
    AI connector that manages multiple AI services with account rotation and fallback
    """

    def __init__(self, key_manager=None, model_evaluator=None):
        """
        Initialize the multi-AI connector

        Args:
            key_manager: KeyManager instance for secure API key management
            model_evaluator: AIModelEvaluator instance for model evaluation
        """
        self.service_manager = AIServiceManager(key_manager=key_manager)

        # Initialize model evaluator if not provided
        if model_evaluator is None:
            from ai_services.model_evaluator import AIModelEvaluator
            self.model_evaluator = AIModelEvaluator(base_weights={
                "openai": 0.30,
                "deepseek": 0.35,
                "qwen": 0.35
            })
        else:
            self.model_evaluator = model_evaluator

        # Configure system prompts for each service
        self.system_prompts = {
            "openai": "You are a financial market analyst. Analyze the provided market data and indicators to provide a trading recommendation. Be concise and focus on actionable insights.",
            "deepseek": "You are a financial market analyst. Analyze the provided market data and indicators to provide a trading recommendation. Be concise and focus on actionable insights.",
            "qwen": "You are a financial market analyst. Analyze the provided market data and indicators to provide a trading recommendation. Be concise and focus on actionable insights."
        }

        # Configure max tokens for each service
        self.max_tokens = {
            "openai": 500,
            "deepseek": 500,
            "qwen": 500
        }

        # Get current weights from model evaluator
        self.weights = self.model_evaluator.get_current_weights()

        logger.info(f"MultiAIConnector initialized with weights: {self.weights}")

    def _format_market_data(self, market_data):
        """
        Format market data for AI analysis

        Args:
            market_data (dict): Market data

        Returns:
            str: Formatted market data
        """
        symbol = market_data.get("symbol", "BTC/USDT")
        price = market_data.get("price", 0)

        # Extract indicators
        indicators = market_data.get("indicators", {})
        ema50 = indicators.get("ema50", None)
        ema200 = indicators.get("ema200", None)
        is_uptrend = indicators.get("is_uptrend", False)
        is_downtrend = indicators.get("is_downtrend", False)
        is_flat = indicators.get("is_flat", True)

        # Format data
        formatted_data = f"""
Market Data for {symbol}:
Current Price: ${price:.2f}

Technical Indicators:
- EMA50: {f"{ema50:.2f}" if ema50 is not None else 'N/A'}
- EMA200: {f"{ema200:.2f}" if ema200 is not None else 'N/A'}
- Trend: {"Uptrend" if is_uptrend else "Downtrend" if is_downtrend else "Flat/Neutral"}

Based on this data, provide a trading recommendation (buy, sell, or hold) with a confidence level (0-100%) and a brief explanation.
"""
        return formatted_data

    def _call_openai_api(self, account, formatted_data, system_prompt=None, user_prompt=None):
        """
        Call OpenAI API for market analysis

        Args:
            account (AIServiceAccount): OpenAI account to use
            formatted_data (str): Formatted market data
            system_prompt (str, optional): Custom system prompt to use
            user_prompt (str, optional): Additional user prompt to append

        Returns:
            dict: Analysis result
        """
        try:
            import openai
            import json

            # Get API key from account
            api_key = account.get_api_key()
            if not api_key:
                logger.error("No OpenAI API key available")
                return self._get_fallback_analysis("openai", formatted_data)

            # Initialize OpenAI client
            client = openai.OpenAI(api_key=api_key)

            # Default prompts if not provided
            if not system_prompt:
                system_prompt = self.system_prompts.get("openai", "You are an expert cryptocurrency trading analyst.")

            if not user_prompt:
                user_prompt = f"""
                Analyze the following cryptocurrency market data and provide a trading recommendation:

                {formatted_data}

                Consider the following factors in your analysis:
                1. Technical indicators (EMA, RSI, MACD, ATR, Bollinger Bands, Ichimoku Cloud)
                2. Volume analysis and market liquidity
                3. Market regime (stable, volatile, unclear)
                4. Risk-reward ratio and position sizing
                5. Current market sentiment and momentum
                6. Support and resistance levels
                7. Trend strength and direction

                Please provide your analysis in the following JSON format:
                {{
                    "recommendation": "buy|sell|hold",
                    "confidence": 0-100,
                    "sentiment": "bullish|bearish|neutral",
                    "explanation": "detailed explanation of your reasoning",
                    "key_factors": ["factor1", "factor2", "factor3"],
                    "risk_level": "low|medium|high",
                    "time_horizon": "short|medium|long",
                    "entry_price": "suggested entry price or null",
                    "stop_loss": "suggested stop loss percentage or null",
                    "take_profit": "suggested take profit percentage or null",
                    "market_regime": "stable|volatile|unclear"
                }}
                """

            # Make API call with enhanced parameters
            response = client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=800,
                temperature=0.2,  # Lower temperature for more consistent responses
                top_p=0.9,
                frequency_penalty=0.1,
                presence_penalty=0.1
            )

            # Parse response
            content = response.choices[0].message.content.strip()

            # Extract JSON from response
            try:
                # Try to parse as JSON directly
                analysis_data = json.loads(content)
            except json.JSONDecodeError:
                # If not valid JSON, try to extract JSON from text
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    analysis_data = json.loads(json_match.group())
                else:
                    # Fallback: parse manually
                    analysis_data = self._parse_ai_response_fallback(content)

            # Validate and normalize response
            analysis = self._validate_ai_response(analysis_data)

            # Mark account as used
            tokens_used = response.usage.total_tokens if response.usage else 400
            account.mark_used(tokens_used=tokens_used)

            logger.info(f"OpenAI analysis: {analysis['recommendation']} with {analysis['confidence']}% confidence")

            return {
                "service": "openai",
                "account_id": account.account_id,
                "analysis": analysis,
                "timestamp": datetime.datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            account.mark_error()

            # If too many errors, disable the account
            if account.error_count >= 3:
                account.disable()

            return self._get_fallback_analysis("openai", formatted_data)

    def _validate_ai_response(self, analysis_data):
        """
        Validate and normalize AI response data with enhanced fields

        Args:
            analysis_data (dict): Raw analysis data from AI

        Returns:
            dict: Validated and normalized analysis
        """
        try:
            # Ensure required fields exist
            recommendation = analysis_data.get("recommendation", "hold").lower()
            if recommendation not in ["buy", "sell", "hold"]:
                recommendation = "hold"

            confidence = analysis_data.get("confidence", 50)
            if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 100:
                confidence = 50

            sentiment = analysis_data.get("sentiment", "neutral").lower()
            if sentiment not in ["bullish", "bearish", "neutral"]:
                sentiment = "neutral"

            explanation = analysis_data.get("explanation", "No explanation provided.")
            if not isinstance(explanation, str):
                explanation = "No explanation provided."

            key_factors = analysis_data.get("key_factors", [])
            if not isinstance(key_factors, list):
                key_factors = []

            # Enhanced fields
            risk_level = analysis_data.get("risk_level", "medium").lower()
            if risk_level not in ["low", "medium", "high"]:
                risk_level = "medium"

            time_horizon = analysis_data.get("time_horizon", "short").lower()
            if time_horizon not in ["short", "medium", "long"]:
                time_horizon = "short"

            market_regime = analysis_data.get("market_regime", "unclear").lower()
            if market_regime not in ["stable", "volatile", "unclear"]:
                market_regime = "unclear"

            # Optional price fields
            entry_price = analysis_data.get("entry_price")
            if entry_price and not isinstance(entry_price, (int, float, str)):
                entry_price = None

            stop_loss = analysis_data.get("stop_loss")
            if stop_loss and not isinstance(stop_loss, (int, float, str)):
                stop_loss = None

            take_profit = analysis_data.get("take_profit")
            if take_profit and not isinstance(take_profit, (int, float, str)):
                take_profit = None

            return {
                "recommendation": recommendation,
                "confidence": int(confidence),
                "sentiment": sentiment,
                "explanation": explanation,
                "key_factors": key_factors,
                "risk_level": risk_level,
                "time_horizon": time_horizon,
                "market_regime": market_regime,
                "entry_price": entry_price,
                "stop_loss": stop_loss,
                "take_profit": take_profit
            }
        except Exception as e:
            logger.error(f"Error validating AI response: {e}")
            return {
                "recommendation": "hold",
                "confidence": 50,
                "sentiment": "neutral",
                "explanation": "Error processing AI response.",
                "key_factors": [],
                "risk_level": "medium",
                "time_horizon": "short",
                "market_regime": "unclear",
                "entry_price": None,
                "stop_loss": None,
                "take_profit": None
            }

    def _parse_ai_response_fallback(self, content):
        """
        Fallback parser for AI responses that aren't valid JSON

        Args:
            content (str): AI response content

        Returns:
            dict: Parsed analysis data
        """
        try:
            # Extract recommendation
            recommendation = "hold"
            if "buy" in content.lower():
                recommendation = "buy"
            elif "sell" in content.lower():
                recommendation = "sell"

            # Extract confidence (look for percentage)
            import re
            confidence_match = re.search(r'(\d+)%', content)
            confidence = int(confidence_match.group(1)) if confidence_match else 50

            # Extract sentiment
            sentiment = "neutral"
            if "bullish" in content.lower():
                sentiment = "bullish"
            elif "bearish" in content.lower():
                sentiment = "bearish"

            return {
                "recommendation": recommendation,
                "confidence": confidence,
                "sentiment": sentiment,
                "explanation": content[:200] + "..." if len(content) > 200 else content,
                "key_factors": []
            }
        except Exception as e:
            logger.error(f"Error in fallback parser: {e}")
            return {
                "recommendation": "hold",
                "confidence": 50,
                "sentiment": "neutral",
                "explanation": "Error parsing AI response.",
                "key_factors": []
            }

    def _get_fallback_analysis(self, service, formatted_data=None):
        """
        Get fallback analysis when API calls fail

        Args:
            service (str): Service name
            formatted_data (str, optional): Market data (unused but kept for compatibility)

        Returns:
            dict: Fallback analysis
        """
        # formatted_data is not used but kept for API compatibility
        _ = formatted_data

        return {
            "service": service,
            "account_id": "fallback",
            "analysis": {
                "recommendation": "hold",
                "confidence": 30,
                "sentiment": "neutral",
                "explanation": f"{service.upper()} API unavailable. Using conservative hold recommendation.",
                "key_factors": ["API_UNAVAILABLE"]
            },
            "timestamp": datetime.datetime.now().isoformat()
        }

    def _call_deepseek_api(self, account, formatted_data, system_prompt=None, user_prompt=None):
        """
        Call DeepSeek API for market analysis

        Args:
            account (AIServiceAccount): DeepSeek account to use
            formatted_data (str): Formatted market data
            system_prompt (str, optional): Custom system prompt to use
            user_prompt (str, optional): Additional user prompt to append

        Returns:
            dict: Analysis result
        """
        try:
            import requests
            import json

            # Get API key from account
            api_key = account.get_api_key()
            if not api_key:
                logger.error("No DeepSeek API key available")
                return self._get_fallback_analysis("deepseek", formatted_data)

            # DeepSeek API endpoint
            url = "https://api.deepseek.com/v1/chat/completions"

            # Default prompts if not provided
            if not system_prompt:
                system_prompt = self.system_prompts.get("deepseek", "You are an expert cryptocurrency trading analyst.")

            if not user_prompt:
                user_prompt = f"""
                Analyze the following cryptocurrency market data and provide a trading recommendation:

                {formatted_data}

                Consider the following factors in your analysis:
                1. Technical indicators (EMA, RSI, MACD, ATR, Bollinger Bands, Ichimoku Cloud)
                2. Volume analysis and market liquidity
                3. Market regime (stable, volatile, unclear)
                4. Risk-reward ratio and position sizing
                5. Current market sentiment and momentum
                6. Support and resistance levels
                7. Trend strength and direction

                Please provide your analysis in the following JSON format:
                {{
                    "recommendation": "buy|sell|hold",
                    "confidence": 0-100,
                    "sentiment": "bullish|bearish|neutral",
                    "explanation": "detailed explanation of your reasoning",
                    "key_factors": ["factor1", "factor2", "factor3"],
                    "risk_level": "low|medium|high",
                    "time_horizon": "short|medium|long",
                    "entry_price": "suggested entry price or null",
                    "stop_loss": "suggested stop loss percentage or null",
                    "take_profit": "suggested take profit percentage or null",
                    "market_regime": "stable|volatile|unclear"
                }}
                """

            # Prepare request with enhanced headers
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
                "User-Agent": "SP-Bot-Enhanced/2.0.0"
            }

            data = {
                "model": "deepseek-chat",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "max_tokens": 800,
                "temperature": 0.2,
                "top_p": 0.9,
                "frequency_penalty": 0.1,
                "presence_penalty": 0.1
            }

            # Make API call
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            # Parse response
            response_data = response.json()
            content = response_data["choices"][0]["message"]["content"].strip()

            # Extract JSON from response
            try:
                # Try to parse as JSON directly
                analysis_data = json.loads(content)
            except json.JSONDecodeError:
                # If not valid JSON, try to extract JSON from text
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    analysis_data = json.loads(json_match.group())
                else:
                    # Fallback: parse manually
                    analysis_data = self._parse_ai_response_fallback(content)

            # Validate and normalize response
            analysis = self._validate_ai_response(analysis_data)

            # Mark account as used
            tokens_used = response_data.get("usage", {}).get("total_tokens", 400)
            account.mark_used(tokens_used=tokens_used)

            logger.info(f"DeepSeek analysis: {analysis['recommendation']} with {analysis['confidence']}% confidence")

            return {
                "service": "deepseek",
                "account_id": account.account_id,
                "analysis": analysis,
                "timestamp": datetime.datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calling DeepSeek API: {e}")
            account.mark_error()

            # If too many errors, disable the account
            if account.error_count >= 3:
                account.disable()

            return self._get_fallback_analysis("deepseek", formatted_data)

    def _call_qwen_api(self, account, formatted_data, system_prompt=None, user_prompt=None):
        """
        Call Qwen API for market analysis

        Args:
            account (AIServiceAccount): Qwen account to use
            formatted_data (str): Formatted market data
            system_prompt (str, optional): Custom system prompt to use
            user_prompt (str, optional): Additional user prompt to append

        Returns:
            dict: Analysis result
        """
        try:
            import requests
            import json

            # Get API key from account
            api_key = account.get_api_key()
            if not api_key:
                logger.error("No Qwen API key available")
                return self._get_fallback_analysis("qwen", formatted_data)

            # Qwen API endpoint (using Alibaba Cloud DashScope)
            url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

            # Default prompts if not provided
            if not system_prompt:
                system_prompt = self.system_prompts.get("qwen", "You are an expert cryptocurrency trading analyst.")

            if not user_prompt:
                user_prompt = f"""
                Analyze the following cryptocurrency market data and provide a trading recommendation:

                {formatted_data}

                Consider the following factors in your analysis:
                1. Technical indicators (EMA, RSI, MACD, ATR, Bollinger Bands, Ichimoku Cloud)
                2. Volume analysis and market liquidity
                3. Market regime (stable, volatile, unclear)
                4. Risk-reward ratio and position sizing
                5. Current market sentiment and momentum
                6. Support and resistance levels
                7. Trend strength and direction

                Please provide your analysis in the following JSON format:
                {{
                    "recommendation": "buy|sell|hold",
                    "confidence": 0-100,
                    "sentiment": "bullish|bearish|neutral",
                    "explanation": "detailed explanation of your reasoning",
                    "key_factors": ["factor1", "factor2", "factor3"],
                    "risk_level": "low|medium|high",
                    "time_horizon": "short|medium|long",
                    "entry_price": "suggested entry price or null",
                    "stop_loss": "suggested stop loss percentage or null",
                    "take_profit": "suggested take profit percentage or null",
                    "market_regime": "stable|volatile|unclear"
                }}
                """

            # Prepare request with enhanced headers
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
                "User-Agent": "SP-Bot-Enhanced/2.0.0"
            }

            # Combine system and user prompts for Qwen
            combined_prompt = f"{system_prompt}\n\n{user_prompt}"

            data = {
                "model": "qwen-turbo",
                "input": {
                    "messages": [
                        {"role": "user", "content": combined_prompt}
                    ]
                },
                "parameters": {
                    "max_tokens": 800,
                    "temperature": 0.2,
                    "top_p": 0.9
                }
            }

            # Make API call
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            # Parse response
            response_data = response.json()
            content = response_data["output"]["choices"][0]["message"]["content"].strip()

            # Extract JSON from response
            try:
                # Try to parse as JSON directly
                analysis_data = json.loads(content)
            except json.JSONDecodeError:
                # If not valid JSON, try to extract JSON from text
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    analysis_data = json.loads(json_match.group())
                else:
                    # Fallback: parse manually
                    analysis_data = self._parse_ai_response_fallback(content)

            # Validate and normalize response
            analysis = self._validate_ai_response(analysis_data)

            # Mark account as used
            tokens_used = response_data.get("usage", {}).get("total_tokens", 400)
            account.mark_used(tokens_used=tokens_used)

            logger.info(f"Qwen analysis: {analysis['recommendation']} with {analysis['confidence']}% confidence")

            return {
                "service": "qwen",
                "account_id": account.account_id,
                "analysis": analysis,
                "timestamp": datetime.datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calling Qwen API: {e}")
            account.mark_error()

            # If too many errors, disable the account
            if account.error_count >= 3:
                account.disable()

            return self._get_fallback_analysis("qwen", formatted_data)

    def analyze_market(self, market_data):
        """
        Analyze market data using multiple AI services

        Args:
            market_data (dict): Market data to analyze

        Returns:
            dict: Analysis results from multiple services
        """
        # Format market data for AI analysis
        formatted_data = self._format_market_data(market_data)

        # Collect signals from all sources
        signals = {}

        # Get OpenAI analysis
        openai_account = self.service_manager.get_next_openai_account()
        if openai_account:
            openai_result = self._call_openai_api(openai_account, formatted_data)
            if openai_result:
                signals["openai"] = openai_result
                # Record prediction for evaluation
                self.model_evaluator.record_prediction(
                    "openai",
                    openai_result['analysis']['recommendation'],
                    openai_result['analysis']['confidence'] / 100.0
                )
                logger.info(f"OpenAI analysis ({openai_account.account_id}): {openai_result['analysis']['recommendation']} with {openai_result['analysis']['confidence']}% confidence")

        # Get DeepSeek analysis
        deepseek_account = self.service_manager.get_next_deepseek_account()
        if deepseek_account:
            deepseek_result = self._call_deepseek_api(deepseek_account, formatted_data)
            if deepseek_result:
                signals["deepseek"] = deepseek_result
                # Record prediction for evaluation
                self.model_evaluator.record_prediction(
                    "deepseek",
                    deepseek_result['analysis']['recommendation'],
                    deepseek_result['analysis']['confidence'] / 100.0
                )
                logger.info(f"DeepSeek analysis ({deepseek_account.account_id}): {deepseek_result['analysis']['recommendation']} with {deepseek_result['analysis']['confidence']}% confidence")

        # Get Qwen analysis
        qwen_account = self.service_manager.get_next_qwen_account()
        if qwen_account:
            qwen_result = self._call_qwen_api(qwen_account, formatted_data)
            if qwen_result:
                signals["qwen"] = qwen_result
                # Record prediction for evaluation
                self.model_evaluator.record_prediction(
                    "qwen",
                    qwen_result['analysis']['recommendation'],
                    qwen_result['analysis']['confidence'] / 100.0
                )
                logger.info(f"Qwen analysis ({qwen_account.account_id}): {qwen_result['analysis']['recommendation']} with {qwen_result['analysis']['confidence']}% confidence")

        # Check if we need to enter debate mode (models disagree)
        recommendations = set()
        for source in signals:
            if "analysis" in signals[source]:
                recommendation = signals[source]["analysis"].get("recommendation", "hold").lower()
                recommendations.add(recommendation)

        # If models disagree, enter enhanced debate mode
        if len(recommendations) > 1 and len(signals) > 1:
            logger.info("Models disagree. Entering Enhanced AI Debate Mode.")

            # Request detailed analysis from each model for debate
            detailed_signals = {}

            for source in signals:
                if "analysis" in signals[source]:
                    # Store original signal
                    detailed_signals[source] = signals[source].copy()

                    # Request detailed analysis if available
                    account = None
                    if source == "openai":
                        account = self.service_manager.get_next_openai_account()
                        if account:
                            # Add debate prompt to request more detailed analysis
                            debate_prompt = f"""
                            You previously recommended {signals[source]['analysis']['recommendation'].upper()} with {signals[source]['analysis']['confidence']}% confidence.
                            Other AI models have different opinions. Please provide a more detailed analysis with:
                            1. Key technical indicators supporting your view
                            2. Potential counterarguments to your position
                            3. Market conditions that would invalidate your recommendation
                            4. Confidence level (0-100%) in your recommendation
                            """

                            # Get detailed analysis
                            detailed_result = self._call_openai_api(account, formatted_data, system_prompt=self.system_prompts["openai"], user_prompt=debate_prompt)
                            if detailed_result:
                                detailed_signals[source]["detailed_analysis"] = detailed_result["analysis"]

                    elif source == "deepseek":
                        account = self.service_manager.get_next_deepseek_account()
                        if account:
                            # Add debate prompt to request more detailed analysis
                            debate_prompt = f"""
                            You previously recommended {signals[source]['analysis']['recommendation'].upper()} with {signals[source]['analysis']['confidence']}% confidence.
                            Other AI models have different opinions. Please provide a more detailed analysis with:
                            1. Key technical indicators supporting your view
                            2. Potential counterarguments to your position
                            3. Market conditions that would invalidate your recommendation
                            4. Confidence level (0-100%) in your recommendation
                            """

                            # Get detailed analysis
                            detailed_result = self._call_deepseek_api(account, formatted_data, system_prompt=self.system_prompts["deepseek"], user_prompt=debate_prompt)
                            if detailed_result:
                                detailed_signals[source]["detailed_analysis"] = detailed_result["analysis"]

                    elif source == "qwen":
                        account = self.service_manager.get_next_qwen_account()
                        if account:
                            # Add debate prompt to request more detailed analysis
                            debate_prompt = f"""
                            You previously recommended {signals[source]['analysis']['recommendation'].upper()} with {signals[source]['analysis']['confidence']}% confidence.
                            Other AI models have different opinions. Please provide a more detailed analysis with:
                            1. Key technical indicators supporting your view
                            2. Potential counterarguments to your position
                            3. Market conditions that would invalidate your recommendation
                            4. Confidence level (0-100%) in your recommendation
                            """

                            # Get detailed analysis
                            detailed_result = self._call_qwen_api(account, formatted_data, system_prompt=self.system_prompts["qwen"], user_prompt=debate_prompt)
                            if detailed_result:
                                detailed_signals[source]["detailed_analysis"] = detailed_result["analysis"]

            # Get debate weights based on historical accuracy
            debate_weights = self.model_evaluator.get_debate_weights(detailed_signals)
            logger.info(f"Debate weights: {debate_weights}")

            # Store debate weights for use in signal aggregation
            self.weights = debate_weights

            # Replace signals with detailed signals
            signals = detailed_signals
        else:
            # Use current weights from model evaluator
            self.weights = self.model_evaluator.get_current_weights()

        return signals

    def generate_discussion(self, signals):
        """
        Generate a discussion between AI models about the market

        Args:
            signals (dict): Signals from multiple sources

        Returns:
            str: Discussion text
        """
        # Generate discussion
        discussion = "AI Models Discussion:\n\n"

        # Add introduction
        discussion += "The following is a discussion between AI models about the current market conditions:\n\n"

        # Check if we need to enter debate mode (models disagree)
        recommendations = set()
        for source in signals:
            if "analysis" in signals[source]:
                recommendation = signals[source]["analysis"].get("recommendation", "hold").lower()
                recommendations.add(recommendation)

        # Get model weights for display
        weights = self.weights

        # Add each AI's analysis
        for source in ["openai", "deepseek", "qwen"]:
            if source in signals and "analysis" in signals[source]:
                analysis = signals[source]["analysis"]
                account_id = signals[source].get("account_id", "unknown")
                weight_pct = int(weights.get(source, 0) * 100)
                model_name = f"{source.upper()} ({account_id}, Weight: {weight_pct}%)"
                recommendation = analysis.get("recommendation", "hold").upper()
                confidence = analysis.get("confidence", 50)
                explanation = analysis.get("explanation", "No explanation provided.")

                discussion += f"{model_name}: I recommend {recommendation} with {confidence}% confidence. {explanation}\n\n"

        # Enhanced debate mode if models disagree
        if len(recommendations) > 1 and len(signals) > 1:
            discussion += "\n=== AI DEBATE MODE ACTIVATED ===\n"
            discussion += "Models disagree on the recommendation. Initiating detailed analysis debate.\n\n"

            # Get historical accuracy for each model
            performance_history = self.model_evaluator.get_performance_history()
            recent_days = sorted(performance_history.keys())[-5:] if performance_history else []

            if recent_days:
                discussion += "Recent accuracy of each model:\n"
                for source in ["openai", "deepseek", "qwen"]:
                    accuracies = []
                    for day in recent_days:
                        if day in performance_history and "accuracy" in performance_history[day]:
                            if source in performance_history[day]["accuracy"]:
                                accuracies.append(performance_history[day]["accuracy"][source])

                    if accuracies:
                        avg_accuracy = sum(accuracies) / len(accuracies)
                        discussion += f"- {source.upper()}: {avg_accuracy:.2%} accuracy over last {len(accuracies)} days\n"

                discussion += "\n"

            # Add detailed arguments from each model
            discussion += "Detailed arguments:\n\n"

            for source in ["openai", "deepseek", "qwen"]:
                if source in signals and "analysis" in signals[source]:
                    analysis = signals[source]["analysis"]
                    recommendation = analysis.get("recommendation", "hold").upper()
                    explanation = analysis.get("explanation", "No explanation provided.")

                    discussion += f"{source.upper()} argues for {recommendation}:\n"
                    discussion += f"- {explanation}\n"
                    discussion += f"- Based on {source.upper()}'s analysis of market conditions and technical indicators.\n\n"

            # Add debate conclusion with weighted decision
            discussion += "Debate conclusion:\n"

            # Calculate weighted recommendation
            weighted_rec = {"buy": 0, "sell": 0, "hold": 0}
            for source in ["openai", "deepseek", "qwen"]:
                if source in signals and "analysis" in signals[source]:
                    rec = signals[source]["analysis"].get("recommendation", "hold").lower()
                    conf = signals[source]["analysis"].get("confidence", 50) / 100.0
                    weight = weights.get(source, 0)
                    weighted_rec[rec] += weight * conf

            # Normalize and find highest
            total_weight = sum(weighted_rec.values())
            if total_weight > 0:
                for rec in weighted_rec:
                    weighted_rec[rec] /= total_weight

                final_rec = max(weighted_rec, key=weighted_rec.get)
                final_conf = weighted_rec[final_rec] * 100

                discussion += f"After weighing all arguments, the final recommendation is {final_rec.upper()} with {final_conf:.1f}% confidence.\n"
                discussion += f"This decision gives more weight to models with better historical performance.\n"
            else:
                discussion += "Unable to reach a conclusion due to insufficient data.\n"

            discussion += "\n=== END OF DEBATE ===\n"

        # Add conclusion
        discussion += "\nFinal Conclusion:\n"

        # Calculate weighted recommendation
        weighted_rec = {"buy": 0, "sell": 0, "hold": 0}
        for source in ["openai", "deepseek", "qwen"]:
            if source in signals and "analysis" in signals[source]:
                rec = signals[source]["analysis"].get("recommendation", "hold").lower()
                conf = signals[source]["analysis"].get("confidence", 50) / 100.0
                weight = weights.get(source, 0)
                weighted_rec[rec] += weight * conf

        # Normalize and find highest
        total_weight = sum(weighted_rec.values())
        if total_weight > 0:
            for rec in weighted_rec:
                weighted_rec[rec] /= total_weight

            final_rec = max(weighted_rec, key=weighted_rec.get)
            final_conf = weighted_rec[final_rec] * 100

            discussion += f"Based on weighted analysis, the final recommendation is {final_rec.upper()} with {final_conf:.1f}% confidence.\n"
        else:
            discussion += "No recommendations available.\n"

        return discussion
