info = {
    "name": "br",
    "date_order": "<PERSON><PERSON>",
    "january": [
        "gen",
        "genver"
    ],
    "february": [
        "c'hwe",
        "c'hwevrer"
    ],
    "march": [
        "meur",
        "meurzh"
    ],
    "april": [
        "ebr",
        "ebrel"
    ],
    "may": [
        "mae"
    ],
    "june": [
        "mezh",
        "mezheven"
    ],
    "july": [
        "goue",
        "gouere"
    ],
    "august": [
        "eost"
    ],
    "september": [
        "gwen",
        "gwengolo"
    ],
    "october": [
        "here"
    ],
    "november": [
        "du"
    ],
    "december": [
        "ker",
        "kerzu",
        "kzu"
    ],
    "monday": [
        "lun"
    ],
    "tuesday": [
        "meu",
        "meurzh"
    ],
    "wednesday": [
        "mer",
        "merc'her"
    ],
    "thursday": [
        "yaou"
    ],
    "friday": [
        "gwe",
        "gwener"
    ],
    "saturday": [
        "sad",
        "sadorn"
    ],
    "sunday": [
        "sul"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "gm"
    ],
    "year": [
        "bl",
        "bloaz"
    ],
    "month": [
        "miz"
    ],
    "week": [
        "sizhun"
    ],
    "day": [
        "d",
        "deiz"
    ],
    "hour": [
        "e",
        "eur"
    ],
    "minute": [
        "min",
        "munut"
    ],
    "second": [
        "eilenn",
        "s"
    ],
    "relative-type": {
        "0 day ago": [
            "hiziv"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "ar miz-mañ"
        ],
        "0 second ago": [
            "brem",
            "bremañ"
        ],
        "0 week ago": [
            "ar sizhun-mañ"
        ],
        "0 year ago": [
            "hevlene"
        ],
        "1 day ago": [
            "dec'h"
        ],
        "1 month ago": [
            "ar miz diaraok"
        ],
        "1 week ago": [
            "ar sizhun diaraok"
        ],
        "1 year ago": [
            "warlene"
        ],
        "in 1 day": [
            "warc'hoazh"
        ],
        "in 1 month": [
            "ar miz a zeu"
        ],
        "in 1 week": [
            "ar sizhun a zeu"
        ],
        "in 1 year": [
            "ar bl a zeu",
            "ar bloaz a zeu"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) d zo",
            "(\\d+[.,]?\\d*) deiz zo"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) e zo",
            "(\\d+[.,]?\\d*) eur zo"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) min zo",
            "(\\d+[.,]?\\d*) munut zo"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) miz zo"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) eilenn zo",
            "(\\d+[.,]?\\d*) s zo"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) sizhun zo"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) bl zo",
            "(\\d+[.,]?\\d*) bloaz zo",
            "(\\d+[.,]?\\d*) vloaz zo"
        ],
        "in \\1 day": [
            "a-benn (\\d+[.,]?\\d*) d",
            "a-benn (\\d+[.,]?\\d*) deiz"
        ],
        "in \\1 hour": [
            "a-benn (\\d+[.,]?\\d*) e",
            "a-benn (\\d+[.,]?\\d*) eur"
        ],
        "in \\1 minute": [
            "a-benn (\\d+[.,]?\\d*) min",
            "a-benn (\\d+[.,]?\\d*) munut"
        ],
        "in \\1 month": [
            "a-benn (\\d+[.,]?\\d*) miz"
        ],
        "in \\1 second": [
            "a-benn (\\d+[.,]?\\d*) eilenn",
            "a-benn (\\d+[.,]?\\d*) s"
        ],
        "in \\1 week": [
            "a-benn (\\d+[.,]?\\d*) sizhun"
        ],
        "in \\1 year": [
            "a-benn (\\d+[.,]?\\d*) bl",
            "a-benn (\\d+[.,]?\\d*) bloaz",
            "a-benn (\\d+[.,]?\\d*) vloaz"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
