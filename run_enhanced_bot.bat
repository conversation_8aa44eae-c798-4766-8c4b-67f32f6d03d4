@echo off
echo ========================================
echo ENHANCED TRADING BOT
echo ========================================
echo.
echo This script will run the enhanced trading bot with multiple data sources.
echo.
echo Options:
echo 1. Run in TEST mode (no real trades)
echo 2. Run in LIVE mode (real money trading)
echo 3. Initialize API keys
echo 4. Exit
echo.

set /p option=Enter option (1-4): 

if "%option%"=="1" (
    echo.
    echo Running in TEST mode...
    python enhanced_main.py --test
) else if "%option%"=="2" (
    echo.
    echo WARNING: This will use REAL MONEY for trading!
    set /p confirm=Are you sure you want to start LIVE trading? (yes/no): 
    
    if /i "%confirm%"=="yes" (
        echo.
        echo Running in LIVE mode...
        python enhanced_main.py
    ) else (
        echo.
        echo Live trading cancelled.
    )
) else if "%option%"=="3" (
    echo.
    echo Initializing API keys...
    python initialize_keys.py
) else if "%option%"=="4" (
    echo.
    echo Exiting...
) else (
    echo.
    echo Invalid option. Please try again.
)

echo.
pause
