info = {
    "name": "lb",
    "date_order": "DMY",
    "january": [
        "jan",
        "januar"
    ],
    "february": [
        "feb",
        "februar"
    ],
    "march": [
        "mäe",
        "mäerz"
    ],
    "april": [
        "abr",
        "abrëll"
    ],
    "may": [
        "mee"
    ],
    "june": [
        "jun",
        "juni"
    ],
    "july": [
        "jul",
        "juli"
    ],
    "august": [
        "aug",
        "august"
    ],
    "september": [
        "sep",
        "september"
    ],
    "october": [
        "okt",
        "oktober"
    ],
    "november": [
        "nov",
        "november"
    ],
    "december": [
        "dez",
        "dezember"
    ],
    "monday": [
        "méi",
        "méindeg"
    ],
    "tuesday": [
        "dën",
        "dënschdeg"
    ],
    "wednesday": [
        "mët",
        "mëttwoch"
    ],
    "thursday": [
        "don",
        "donneschdeg"
    ],
    "friday": [
        "fre",
        "freideg"
    ],
    "saturday": [
        "sam",
        "samschdeg"
    ],
    "sunday": [
        "son",
        "sonndeg"
    ],
    "am": [
        "moies"
    ],
    "pm": [
        "nomëttes"
    ],
    "year": [
        "j",
        "joer"
    ],
    "month": [
        "m",
        "mount"
    ],
    "week": [
        "w",
        "woch"
    ],
    "day": [
        "d",
        "dag"
    ],
    "hour": [
        "st",
        "stonn"
    ],
    "minute": [
        "min",
        "minutt"
    ],
    "second": [
        "sek",
        "sekonn"
    ],
    "relative-type": {
        "0 day ago": [
            "haut"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "dëse mount"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "dës woch"
        ],
        "0 year ago": [
            "dëst joer"
        ],
        "1 day ago": [
            "gëschter"
        ],
        "1 month ago": [
            "leschte mount"
        ],
        "1 week ago": [
            "lescht woch"
        ],
        "1 year ago": [
            "lescht joer"
        ],
        "in 1 day": [
            "muer"
        ],
        "in 1 month": [
            "nächste mount"
        ],
        "in 1 week": [
            "nächst woch"
        ],
        "in 1 year": [
            "nächst joer"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "virun (\\d+[.,]?\\d*) d",
            "virun (\\d+[.,]?\\d*) dag"
        ],
        "\\1 hour ago": [
            "virun (\\d+[.,]?\\d*) st",
            "virun (\\d+[.,]?\\d*) stonn"
        ],
        "\\1 minute ago": [
            "virun (\\d+[.,]?\\d*) min",
            "virun (\\d+[.,]?\\d*) minutt"
        ],
        "\\1 month ago": [
            "virun (\\d+[.,]?\\d*) m",
            "virun (\\d+[.,]?\\d*) mount"
        ],
        "\\1 second ago": [
            "virun (\\d+[.,]?\\d*) sek",
            "virun (\\d+[.,]?\\d*) sekonn"
        ],
        "\\1 week ago": [
            "virun (\\d+[.,]?\\d*) w",
            "virun (\\d+[.,]?\\d*) woch"
        ],
        "\\1 year ago": [
            "virun (\\d+[.,]?\\d*) j",
            "virun (\\d+[.,]?\\d*) joer"
        ],
        "in \\1 day": [
            "an (\\d+[.,]?\\d*) d",
            "an (\\d+[.,]?\\d*) dag"
        ],
        "in \\1 hour": [
            "an (\\d+[.,]?\\d*) st",
            "an (\\d+[.,]?\\d*) stonn"
        ],
        "in \\1 minute": [
            "an (\\d+[.,]?\\d*) min",
            "an (\\d+[.,]?\\d*) minutt"
        ],
        "in \\1 month": [
            "an (\\d+[.,]?\\d*) m",
            "an (\\d+[.,]?\\d*) mount"
        ],
        "in \\1 second": [
            "an (\\d+[.,]?\\d*) sek",
            "an (\\d+[.,]?\\d*) sekonn"
        ],
        "in \\1 week": [
            "an (\\d+[.,]?\\d*) w",
            "an (\\d+[.,]?\\d*) woch"
        ],
        "in \\1 year": [
            "an (\\d+[.,]?\\d*) j",
            "an (\\d+[.,]?\\d*) joer"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
