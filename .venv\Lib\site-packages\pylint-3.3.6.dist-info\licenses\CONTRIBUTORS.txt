# This file is autocompleted by 'contributors-txt',
# using the configuration in 'script/.contributors_aliases.json'.
# Do not add new persons manually and only add information without
# using '-' as the line first character.
# Please verify that your change are stable if you modify manually.

Ex-maintainers
--------------
- <PERSON><PERSON><PERSON><PERSON> <pc<PERSON><PERSON><EMAIL>>
- <PERSON><PERSON><PERSON><PERSON> <<EMAIL>> : main author / maintainer
- <PERSON><PERSON> <<EMAIL>>


Maintainers
-----------
- <PERSON> <<EMAIL>>
- <PERSON><PERSON> <13665637+<PERSON><PERSON><EMAIL>>
- <PERSON> <<EMAIL>>
- <PERSON> <<EMAIL>>
- Hippo91 <<EMAIL>>
- <PERSON> <<EMAIL>>
- <PERSON> <<EMAIL>>
- <PERSON><PERSON> <<EMAIL>>
- <PERSON> <112832187+clav<PERSON><PERSON><PERSON>@users.noreply.github.com>
- Łukasz Rogalski <<EMAIL>>
- <PERSON> Whetter <<EMAIL>>
- Nick Drozd <<EMAIL>>: performance improvements to astroid
- Bryce Guinta <<EMAIL>>
- Yu Shao, Pang <<EMAIL>>
- Dimitri Prybysh <<EMAIL>>
  * multiple-imports, not-iterable, not-a-mapping, various patches.
- Roy Williams <<EMAIL>> (Lyft)
  * added check for implementing __eq__ without implementing __hash__,
  * Added Python 3 check for accessing Exception.message.
  * Added Python 3 check for calling encode/decode with invalid codecs.
  * Added Python 3 check for accessing sys.maxint.
  * Added Python 3 check for bad import statements.
  * Added Python 3 check for accessing deprecated methods on the 'string' module,
    various patches.
- Florian Bruhin <<EMAIL>>
- Arianna Yang <<EMAIL>>


Contributors
------------

We would not be here without folks that contributed patches, pull requests,
issues and their time to pylint. We're incredibly grateful to all of these
contributors:

- Emile Anclin <<EMAIL>> (Logilab): python 3 support
- Michal Nowikowski <<EMAIL>>:
  * wrong-spelling-in-comment
  * wrong-spelling-in-docstring
  * parallel execution on multiple CPUs
- Julthep Nandakwang <<EMAIL>>
- Bruno Daniel <<EMAIL>>: check_docs extension.
- Sushobhit <<EMAIL>> (sushobhit27)
  * Added new check 'comparison-with-itself'.
  * Added new check 'useless-import-alias'.
  * Added support of annotations in missing-type-doc and missing-return-type-doc.
  * Added new check 'comparison-with-callable'.
  * Removed six package dependency.
  * Added new check 'chained-comparison'.
  * Added new check 'useless-object-inheritance'.
- Brett Cannon <<EMAIL>>:
  * Port source code to be Python 2/3 compatible
  * Python 3 checker
- Laura Médioni <<EMAIL>> (Logilab, on behalf of the CNES):
  * misplaced-comparison-constant
  * no-classmethod-decorator
  * no-staticmethod-decorator
  * too-many-nested-blocks,
  * too-many-boolean-expressions
  * unneeded-not
  * wrong-import-order
  * ungrouped-imports,
  * wrong-import-position
  * redefined-variable-type
- Harutaka Kawamura <<EMAIL>>
- Alexandre Fayolle <<EMAIL>> (Logilab): TkInter gui, documentation, debian support
- Ville Skyttä <<EMAIL>>
- Julien Cristau <<EMAIL>> (Logilab): python 3 support
- Adrien Di Mascio <<EMAIL>>
- Moisés López <<EMAIL>> (Vauxoo):
  * Support for deprecated-modules in modules not installed,
  * Refactor wrong-import-order to integrate it with `isort` library
  * Add check too-complex with mccabe for cyclomatic complexity
  * Refactor wrong-import-position to skip try-import and nested cases
  * Add consider-merging-isinstance, superfluous-else-return
  * Fix consider-using-ternary for 'True and True and True or True' case
  * Add bad-docstring-quotes and docstring-first-line-empty
  * Add missing-timeout
- Frank Harrison <<EMAIL>> (doublethefish)
- Pierre-Yves David <<EMAIL>>
- David Shea <<EMAIL>>: invalid sequence and slice index
- Gunung P. Wibisono <<EMAIL>>
- Derek Gustafson <<EMAIL>>
- Cezar Elnazli <<EMAIL>>: deprecated-method
- Joseph Young <<EMAIL>> (jpy-git)
- Tim Martin <<EMAIL>>
- Ollie <<EMAIL>>
- Zen Lee <<EMAIL>>
- Tushar Sadhwani <<EMAIL>> (tusharsadhwani)
- Nicolas Chauvat <<EMAIL>>
- orSolocate <<EMAIL>>
- Radu Ciorba <<EMAIL>>: not-context-manager and confusing-with-statement warnings.
- Holger Peters <<EMAIL>>
- Cosmin Poieană <<EMAIL>>: unichr-builtin and improvements to bad-open-mode.
- Yilei "Dolee" Yang <<EMAIL>>
- Steven Myint <<EMAIL>>: duplicate-except.
- Peter Kolbus <<EMAIL>> (Garmin)
- Luigi Bertaco Cristofolini <<EMAIL>> (luigibertaco)
- Glenn Matthews <<EMAIL>>:
  * autogenerated documentation for optional extensions,
  * bug fixes and enhancements for docparams (née check_docs) extension
- crazybolillo <<EMAIL>>
- Vlad Temian <<EMAIL>>: redundant-unittest-assert and the JSON reporter.
- Julien Jehannet <<EMAIL>>
- Boris Feld <<EMAIL>>
- Anthony Sottile <<EMAIL>>
- Robert Hofer <<EMAIL>>
- Pedro Algarvio <<EMAIL>> (s0undt3ch)
- Julien Palard <<EMAIL>>
- David Liu <<EMAIL>> (david-yz-liu)
- Dan Goldsmith <<EMAIL>>: support for msg-template in HTML reporter.
- Buck Evan <<EMAIL>>
- Mariatta Wijaya <<EMAIL>>
  * Added new check `logging-fstring-interpolation`
  * Documentation typo fixes
- Jakub Wilk <<EMAIL>>
- Hugo van Kemenade <<EMAIL>>
- Eli Fine <<EMAIL>> (eli88fine): Fixed false positive duplicate code warning for lines with symbols only
- Andrew Haigh <<EMAIL>> (nelfin)
- Émile Crater <<EMAIL>>
- Pavel Roskin <<EMAIL>>
- David Gilman <<EMAIL>>
- へーさん <<EMAIL>>
- Thomas Hisch <<EMAIL>>
- Marianna Polatoglou <<EMAIL>>: minor contribution for wildcard import check
- Manuel Vázquez Acosta <<EMAIL>>
- Luis Escobar <<EMAIL>> (Vauxoo): Add bad-docstring-quotes and docstring-first-line-empty
- Lucas Cimon <<EMAIL>>
- Konstantina Saketou <<EMAIL>>
- Konstantin <<EMAIL>>
- Jim Robertson <<EMAIL>>
- Ethan Leba <<EMAIL>>
- Enji Cooper <<EMAIL>>
- Drum Ogilvie <<EMAIL>>
- David Lindquist <<EMAIL>>: logging-format-interpolation warning.
- Daniel Harding <<EMAIL>>
- Anthony Truchet <<EMAIL>>
- Alexander Todorov <<EMAIL>>:
  * added new error conditions to 'bad-super-call',
  * Added new check for incorrect len(SEQUENCE) usage,
  * Added new extension for comparison against empty string constants,
  * Added new extension which detects comparing integers to zero,
  * Added new useless-return checker,
  * Added new try-except-raise checker
- theirix <<EMAIL>>
- correctmost <<EMAIL>>
- Téo Bouvard <<EMAIL>>
- Stavros Ntentos <<EMAIL>>
- Nicolas Boulenguez <<EMAIL>>
- Mihai Balint <<EMAIL>>
- Mark Bell <<EMAIL>>
- Levi Gruspe <<EMAIL>>
- Jakub Kuczys <<EMAIL>>
- Hornwitser <<EMAIL>>: fix import graph
- Fureigh <<EMAIL>>
- David Douard <<EMAIL>>
- Daniel Balparda <<EMAIL>> (Google): GPyLint maintainer (Google's pylint variant)
- Bastien Vallet <<EMAIL>> (Djailla)
- Aru Sahni <<EMAIL>>: Git ignoring, regex-based ignores
- Andreas Freimuth <<EMAIL>>: fix indentation checking with tabs
- Alexandru Coman <<EMAIL>>
- jpkotta <<EMAIL>>
- Thomas Grainger <<EMAIL>>
- Takahide Nojima <<EMAIL>>
- Taewon D. Kim <<EMAIL>>
- Sneaky Pete <<EMAIL>>
- Sergey B Kirpichev <<EMAIL>>
- Sandro Tosi <<EMAIL>>: Debian packaging
- Rogdham <<EMAIL>>
- Rene Zhang <<EMAIL>>
- Paul Lichtenberger <<EMAIL>>
- Or Bahari <<EMAIL>>
- Mr. Senko <<EMAIL>>
- Mike Frysinger <<EMAIL>>
- Martin von Gagern <<EMAIL>> (Google): Added 'raising-format-tuple' warning.
- Martin Vielsmaier <<EMAIL>>
- Martin Pool <<EMAIL>> (Google):
  * warnings for anomalous backslashes
  * symbolic names for messages (like 'unused')
  * etc.
- Martin Bašti <<EMAIL>>
  * Added new check for shallow copy of os.environ
  * Added new check for useless `with threading.Lock():` statement
- Marcus Näslund <<EMAIL>> (naslundx)
- Marco Pernigotti <<EMAIL>>
- Marco Forte <<EMAIL>>
- James Addison <<EMAIL>>
- Ionel Maries Cristian <<EMAIL>>
- Gergely Kalmár <<EMAIL>>
- Damien Baty <<EMAIL>>
- Benjamin Drung <<EMAIL>>: contributing Debian Developer
- Anubhav <<EMAIL>>
- Antonio Quarta <<EMAIL>>
- Andrew J. Simmons <<EMAIL>>
- Alexey Pelykh <<EMAIL>>
- wtracy <<EMAIL>>
- jessebrennan <<EMAIL>>
- chohner <<EMAIL>>
- aatle <<EMAIL>>
- Tiago Honorato <<EMAIL>>
- Steven M. Vascellaro <<EMAIL>>
- Robin Tweedie <<EMAIL>>
- Roberto Leinardi <<EMAIL>>: PyCharm plugin maintainer
- Ricardo Gemignani <<EMAIL>>
- Pieter Engelbrecht <<EMAIL>>
- Philipp Albrecht <<EMAIL>> (pylbrecht)
- Nicolas Dickreuter <<EMAIL>>
- Nick Bastin <<EMAIL>>
- Nathaniel Manista <<EMAIL>>: suspicious lambda checking
- Maksym Humetskyi <<EMAIL>> (mhumetskyi)
  * Fixed ignored empty functions by similarities checker with "ignore-signatures" option enabled
  * Ignore function decorators signatures as well by similarities checker with "ignore-signatures" option enabled
  * Ignore class methods and nested functions signatures as well by similarities checker with "ignore-signatures" option enabled
- Kylian <<EMAIL>>
- Konstantin Manna <<EMAIL>>
- Kai Mueller <<EMAIL>>
- Joshua Cannon <<EMAIL>>
- John Leach <<EMAIL>>
- James Morgensen <<EMAIL>>: ignored-modules option applies to import errors.
- Jaehoon Hwang <<EMAIL>> (jaehoonhwang)
- Huw Jones <<EMAIL>>
- Gideon <<EMAIL>>
- Ganden Schaffner <<EMAIL>>
- Frost Ming <<EMAIL>>
- Federico Bond <<EMAIL>>
- Erik Wright <<EMAIL>>
- Erik Eriksson <<EMAIL>>: Added overlapping-except error check.
- Daniel Mouritzen <<EMAIL>>
- Dan Hemberger <<EMAIL>>
- Chris Rebert <<EMAIL>>: unidiomatic-typecheck.
- Aurelien Campeas <<EMAIL>>
- Alvaro Frias <<EMAIL>>
- Alexander Pervakov <<EMAIL>>
- Alain Leufroy <<EMAIL>>
- Adam Williamson <<EMAIL>>
- xmo-odoo <<EMAIL>>
- tbennett0 <<EMAIL>>
- omarandlorraine <<EMAIL>>
- craig-sh <<EMAIL>>
- bernie gray <<EMAIL>>
- azinneck0485 <<EMAIL>>
- Wes Turner <<EMAIL>> (Google): added new check 'inconsistent-quotes'
- Tyler Thieding <<EMAIL>>
- Tobias Hernstig <<EMAIL>>
- Sviatoslav Sydorenko <<EMAIL>>
- Smixi <<EMAIL>>
- Simu Toni <<EMAIL>>
- Sergei Lebedev <<EMAIL>>
- Scott Worley <<EMAIL>>
- Saugat Pachhai <<EMAIL>>
- Samuel FORESTIER <<EMAIL>>
- Rémi Cardona <<EMAIL>>
- Ryan Ozawa <<EMAIL>>
- Roger Sheu <<EMAIL>>
- Raphael Gaschignard <<EMAIL>>
- Ram Rachum <<EMAIL>> (cool-RR)
- Radostin Stoyanov <<EMAIL>>
- Peter Bittner <<EMAIL>>
- Paul Renvoisé <<EMAIL>>
- PHeanEX <<EMAIL>>
- Omega Weapon <<EMAIL>>
- Nikolai Kristiansen <<EMAIL>>
- Nick Pesce <<EMAIL>>
- Nathan Marrow <<EMAIL>>
- Mikhail Fesenko <<EMAIL>>
- Matthew Suozzo <<EMAIL>>
- Matthew Beckers <<EMAIL>> (mattlbeck)
- Mark Roman Miller <<EMAIL>>: fix inline defs in too-many-statements
- MalanB <<EMAIL>>
- Mads Kiilerich <<EMAIL>>
- Maarten ter Huurne <<EMAIL>>
- Lefteris Karapetsas <<EMAIL>>
- LCD 47 <<EMAIL>>
- Jérome Perrin <<EMAIL>>
- Justin Li <<EMAIL>>
- John Kirkham <<EMAIL>>
- Jens H. Nielsen <<EMAIL>>
- Jake Lishman <<EMAIL>>
- Ioana Tagirta <<EMAIL>>: fix bad thread instantiation check
- Ikraduya Edian <<EMAIL>>: Added new checks 'consider-using-generator' and 'use-a-generator'.
- Hugues Bruant <<EMAIL>>
- Hashem Nasarat <<EMAIL>>
- Harut <<EMAIL>>
- Grygorii Iermolenko <<EMAIL>>
- Grizzly Nyo <<EMAIL>>
- Gabriel R. Sezefredo <<EMAIL>>: Fixed "exception-escape" false positive with generators
- Filipe Brandenburger <<EMAIL>>
- Fantix King <<EMAIL>> (UChicago)
- Eric McDonald <<EMAIL>>
- Elias Dorneles <<EMAIL>>: minor adjust to config defaults and docs
- Elazrod56 <<EMAIL>>
- Derek Harland <<EMAIL>>
- David Pursehouse <<EMAIL>>
- Dave Bunten <<EMAIL>>
- Daniel Miller <<EMAIL>>
- Christoph Blessing <<EMAIL>>
- Chris Murray <<EMAIL>>
- Chris Lamb <<EMAIL>>
- Charles Hebert <<EMAIL>>
- Carli Freudenberg <<EMAIL>> (CarliJoy)
  * Fixed issue 5281, added Unicode checker
  * Improve non-ascii-name checker
- Bruce Dawson <<EMAIL>>
- Brian Shaginaw <<EMAIL>>: prevent error on exception check for functions
- Benny Mueller <<EMAIL>>
- Ben James <<EMAIL>>
- Ben Green <<EMAIL>>
- Batuhan Taskaya <<EMAIL>>
- Alexander Kapshuna <<EMAIL>>
- Akhil Kamat <<EMAIL>>
- Adam Parkin <<EMAIL>>
- 谭九鼎 <<EMAIL>>
- Łukasz Sznuk <<EMAIL>>
- zasca <<EMAIL>>
- y2kbugger <<EMAIL>>
- vinnyrose <<EMAIL>>
- ttenhoeve-aa <<EMAIL>>
- thinwybk <<EMAIL>>
- syutbai <<EMAIL>>
- sur.la.route <<EMAIL>>
- sdet_liang <<EMAIL>>
- purajit <<EMAIL>>
- paschich <<EMAIL>>
- oittaa <<EMAIL>>
- nyabkun <<EMAIL>>
- moxian <<EMAIL>>
- mar-chi-pan <<EMAIL>>
- lrjball <<EMAIL>>
- laike9m <<EMAIL>>
- kyoto7250 <<EMAIL>>
- kriek <<EMAIL>>
- kdestin <<EMAIL>>
- jaydesl <<EMAIL>>
- jab <<EMAIL>>
- gracejiang16 <<EMAIL>>
- glmdgrielson <<EMAIL>>
- glegoux <<EMAIL>>
- gaurikholkar <<EMAIL>>
- flyingbot91 <<EMAIL>>
- fly <<EMAIL>>
- fahhem <fahhem>
- fadedDexofan <<EMAIL>>
- epenet <<EMAIL>>
- danields <<EMAIL>>
- cosven <<EMAIL>>
- cordis-dev <<EMAIL>>
- cherryblossom <<EMAIL>>
- bluesheeptoken <<EMAIL>>
- anatoly techtonik <<EMAIL>>
- akirchhoff-modular <<EMAIL>>
- agutole <<EMAIL>>
- Zeckie <<EMAIL>>
- Zeb Nicholls <<EMAIL>>
  * Made W9011 compatible with 'of' syntax in return types
- Yuval Langer <<EMAIL>>
- Yury Gribov <<EMAIL>>
- Yuri Bochkarev <<EMAIL>>: Added epytext support to docparams extension.
- Youngsoo Sung <<EMAIL>>
- Yory <<EMAIL>>
- Yoichi Nakayama <<EMAIL>>
- Yeting Li <<EMAIL>> (yetingli)
- Yannack <<EMAIL>>
- Yann Dirson <<EMAIL>>
- Yang Yang <<EMAIL>>
- Xi Shen <<EMAIL>>
- Winston H <<EMAIL>>
- Wing Lian <<EMAIL>>
- Will Shanks <<EMAIL>>
- Viorel Știrbu <<EMAIL>>: intern-builtin warning.
- VictorT <<EMAIL>>
- Victor Jiajunsu <<EMAIL>>
- ViRuSTriNiTy <<EMAIL>>
- Val Lorentz <<EMAIL>>
- Ulrich Eckhardt <<EMAIL>>
- Udi Fuchs <<EMAIL>>
- Trevor Bekolay <<EMAIL>>
  * Added --list-msgs-enabled command
- Tomer Chachamu <<EMAIL>>: simplifiable-if-expression
- Tomasz Michalski <<EMAIL>>
- Tomasz Magulski <<EMAIL>>
- Tom <<EMAIL>>
- Tim Hatch <<EMAIL>>
- Tim Gates <<EMAIL>>
- Tianyu Chen <<EMAIL>>
- Théo Battrel <<EMAIL>>
- Thomas Benhamou <<EMAIL>>
- Theodore Ni <<EMAIL>>
- Tanvi Moharir <<EMAIL>>: Fix for invalid toml config
- T.Rzepka <<EMAIL>>
- Svetoslav Neykov <<EMAIL>>
- SubaruArai <<EMAIL>>
- Stéphane Wirtel <<EMAIL>>: nonlocal-without-binding
- Stephen Longofono <<EMAIL>>
- Stephane Odul <<EMAIL>>
- Stanislav Levin <<EMAIL>>
- Sorin Sbarnea <<EMAIL>>
- Slavfox <<EMAIL>>
- Skip Montanaro <<EMAIL>>
- Sigurd Spieckermann <<EMAIL>>
- Shiv Venkatasubrahmanyam <<EMAIL>>
- Sebastian Müller <<EMAIL>>
- Sayyed Faisal Ali <<EMAIL>>
- Sasha Bagan <<EMAIL>>
- Sardorbek Imomaliev <<EMAIL>>
- Santiago Castro <<EMAIL>>
- Samuel Freilich <<EMAIL>> (sfreilich)
- Sam Vermeiren <<EMAIL>>
- Ryan McGuire <<EMAIL>>
- Ry4an Brase <<EMAIL>>
- Ruro <<EMAIL>>
- Roshan Shetty <<EMAIL>>
- Roman Ivanov <<EMAIL>>
- Robert Schweizer <<EMAIL>>
- Reverb Chu <<EMAIL>>
- Renat Galimov <<EMAIL>>
- Rebecca Turner <<EMAIL>> (9999years)
- Randall Leeds <<EMAIL>>
- Ramon Saraiva <<EMAIL>>
- Ramiro Leal-Cavazos <<EMAIL>> (ramiro050): Fixed bug preventing pylint from working with Emacs tramp
- RSTdefg <<EMAIL>>
- R. N. West <<EMAIL>>
- Qwiddle13 <<EMAIL>>
- Quentin Young <<EMAIL>>
- Prajwal Borkar <<EMAIL>>
- Petr Pulc <<EMAIL>>: require whitespace around annotations
- Peter Dawyndt <<EMAIL>>
- Peter Dave Hello <<EMAIL>>
- Peter Aronoff <<EMAIL>>
- Paul Cochrane <<EMAIL>>
- Patrik <<EMAIL>>
- Pascal Corpet <<EMAIL>>
- Pablo Galindo Salgado <<EMAIL>>
  * Fix false positive 'Non-iterable value' with async comprehensions.
- Osher De Paz <<EMAIL>>
- Oisín Moran <<EMAIL>>
- Obscuron <<EMAIL>>
- Noam Yorav-Raphael <<EMAIL>>
- Noah-Agnel <<EMAIL>>
- Nir Soffer <<EMAIL>>
- Niko Wenselowski <<EMAIL>>
- Nikita Sobolev <<EMAIL>>
- Nick Smith <<EMAIL>>
- Neowizard <<EMAIL>>
- Ned Batchelder <<EMAIL>>
- Natalie Serebryakova <<EMAIL>>
- Naglis Jonaitis <<EMAIL>>
- Moody <<EMAIL>>
- Mitchell Young <<EMAIL>>: minor adjustment to docparams
- Mitar <<EMAIL>>
- Ming Lyu <<EMAIL>>
- Mikhail f. Shiryaev <<EMAIL>>
- Mike Fiedler <<EMAIL>> (miketheman)
- Mike Bryant <<EMAIL>>
- Mike Bernard <<EMAIL>>
- Michka Popoff <<EMAIL>>
- Michal Vasilek <<EMAIL>>
- Michael Scott Cuthbert <<EMAIL>>
- Michael Kefeder <<EMAIL>>
- Michael K <<EMAIL>>
- Michael Hudson-Doyle <<EMAIL>>
- Michael Giuffrida <<EMAIL>>
- Melvin Hazeleger <<EMAIL>>
- Meltem Kenis <<EMAIL>>
- Mehdi Drissi <<EMAIL>>
- Matěj Grabovský <<EMAIL>>
- Matthijs Blom <<EMAIL>>
- Matej Spiller Muys <<EMAIL>>
- Matej Marušák <<EMAIL>>
- Markus Siebenhaar <<EMAIL>>
- Marco Edward Gorelli <<EMAIL>>: Documented Jupyter integration
- Marcin Kurczewski <<EMAIL>> (rr-)
- Maik Röder <<EMAIL>>
- Lumír 'Frenzy' Balhar <<EMAIL>>
- Ludovic Aubry <<EMAIL>>
- Louis Sautier <<EMAIL>>
- Lorena Buciu <<EMAIL>>
- Logan Miller <<EMAIL>>
- Kári Tristan Helgason <<EMAIL>>
- Kurian Benoy <<EMAIL>>
- Krzysztof Czapla <<EMAIL>>
- Kraig Brockschmidt <<EMAIL>>
- Kound <<EMAIL>>
- KotlinIsland <<EMAIL>>
- Kosarchuk Sergey <<EMAIL>>
- Konrad Weihmann <<EMAIL>>
- Kian Meng, Ang <<EMAIL>>
- Kevin Phillips <<EMAIL>>
- Kevin Jing Qiu <<EMAIL>>
- Kenneth Schackart <<EMAIL>>
- Kayran Schmidt <<EMAIL>>
- Karthik Nadig <<EMAIL>>
- Jürgen Hermann <<EMAIL>>
- Josselin Feist <<EMAIL>>
- Jonathan Kotta <<EMAIL>>
- John Paraskevopoulos <<EMAIL>>: add 'differing-param-doc' and 'differing-type-doc'
- John McGehee <<EMAIL>>
- John Gabriele <<EMAIL>>
- John Belmonte <<EMAIL>>
- Joffrey Mander <<EMAIL>>
- Jochen Preusche <<EMAIL>>
- Jeroen Seegers <<EMAIL>>:
  * Fixed `toml` dependency issue
- Jeremy Fleischman <<EMAIL>>
- Jason Owen <<EMAIL>>
- Jason Lau <<EMAIL>>
- Jared Garst <<EMAIL>>
- Jared Deckard <<EMAIL>>
- Janne Rönkkö <<EMAIL>>
- Jamie Scott <<EMAIL>>
- James Sinclair <<EMAIL>>
- James M. Allen <<EMAIL>>
- James Lingard <<EMAIL>>
- James Broadhead <<EMAIL>>
- Jakub Kulík <<EMAIL>>
- Jakob Normark <<EMAIL>>
- Jacques Kvam <<EMAIL>>
- Jace Browning <<EMAIL>>: updated default report format with clickable paths
- JT Olds <<EMAIL>>
- Iggy Eom <<EMAIL>>
- Hayden Richards <<EMAIL>>
  * Fixed "no-self-use" for async methods
  * Fixed "docparams" extension for async functions and methods
- Harshil <<EMAIL>>
- Harry <<EMAIL>>
- Grégoire <<EMAIL>>
- Grant Welch <<EMAIL>>
- Giuseppe Valente <<EMAIL>>
- Gary Tyler McLeod <<EMAIL>>
- Felix von Drigalski <<EMAIL>>
- Fabrice Douchant <<EMAIL>>
- Fabio Natali <<EMAIL>>
- Fabian Damken <<EMAIL>>
- Eric Froemling <<EMAIL>>
- Emmanuel Chaudron <<EMAIL>>
- Elizabeth Bott <<EMAIL>>
- Ekin Dursun <<EMAIL>>
- Eisuke Kawashima <<EMAIL>>
- Edward K. Ream <<EMAIL>>
- Edgemaster <<EMAIL>>
- Eddie Darling <<EMAIL>>
- Drew Risinger <<EMAIL>>
- Dr. Nick <<EMAIL>>
- Don Jayamanne <<EMAIL>>
- Dmytro Kyrychuk <<EMAIL>>
- Dionisio E Alonso <<EMAIL>>
- DetachHead <<EMAIL>>
- Dennis Keck <<EMAIL>>
- Denis Laxalde <<EMAIL>>
- David Lawson <<EMAIL>>
- David Cain <<EMAIL>>
- Danny Hermes <<EMAIL>>
- Daniele Procida <<EMAIL>>
- Daniela Plascencia <<EMAIL>>
- Daniel Werner <<EMAIL>>
- Daniel Wang <<EMAIL>>
- Daniel R. Neal <<EMAIL>> (danrneal)
- Daniel Draper <<EMAIL>>
- Daniel Dorani <<EMAIL>> (doranid)
- Daniel Brookman <<EMAIL>>
- Dan Garrette <<EMAIL>>
- Damien Nozay <<EMAIL>>
- Cubicpath <<EMAIL>>
- Craig Citro <<EMAIL>>
- Cosmo <<EMAIL>>
- Clément Schreiner <<EMAIL>>
- Clément Pit-Claudel <<EMAIL>>
- Christopher Zurcher <<EMAIL>>
- Christian Clauss <<EMAIL>>
- Carl Crowder <<EMAIL>>: don't evaluate the value of arguments for 'dangerous-default-value'
- Carey Metcalfe <<EMAIL>>: demoted `try-except-raise` from error to warning
- Cameron Olechowski <<EMAIL>>
- Calin Don <<EMAIL>>
- Caio Carrara <<EMAIL>>
- C.A.M. Gerlach <<EMAIL>>
- Bruno P. Kinoshita <<EMAIL>>
- Brice Chardin <<EMAIL>>
- Brian C. Lane <<EMAIL>>
- Brandon W Maister <<EMAIL>>
- BioGeek <<EMAIL>>
- Benjamin Graham <<EMAIL>>
- Benedikt Morbach <<EMAIL>>
- Ben Greiner <<EMAIL>>
- Barak Shoshany <<EMAIL>>
- Banjamin Freeman <<EMAIL>>
- Avram Lubkin <<EMAIL>>
- Athos Ribeiro <<EMAIL>>: Fixed dict-keys-not-iterating false positive for inverse containment checks
- Arun Persaud <<EMAIL>>
- Arthur Lutz <<EMAIL>>
- Antonio Ossa <<EMAIL>>
- Antonio Gámiz Delgado <<EMAIL>>
- Anthony VEREZ <<EMAIL>>
- Anthony Tan <<EMAIL>>
- Anthony Foglia <<EMAIL>> (Google): Added simple string slots check.
- Anentropic <<EMAIL>>
- Andy Young <<EMAIL>>
- Andy Palmer <<EMAIL>>
- Andrzej Klajnert <<EMAIL>>
- Andrew Howe <<EMAIL>>
- Andres Perez Hortal <<EMAIL>>
- Andre Hora <<EMAIL>>
- Aman Salwan <<EMAIL>>
- Alok Singh <<EMAIL>>
- Allan Chandler <<EMAIL>> (allanc65)
  * Fixed issue 5452, false positive missing-param-doc for multi-line Google-style params
- Alex Waygood <<EMAIL>>
- Alex Mor <<EMAIL>>
- Alex Jurkiewicz <<EMAIL>>
- Alex Hearn <<EMAIL>>
- Alex Fortin <<EMAIL>>
- Aleksander Mamla <<EMAIL>>
- Alan Evangelista <<EMAIL>>
- Alan Chan <<EMAIL>>
- Aivar Annamaa <<EMAIL>>
- Aidan Haase <<EMAIL>>
- Ahirnish Pareek <<EMAIL>>: 'keyword-arg-before-var-arg' check
- Agustin Marquez <<EMAIL>>
- Adrian Chirieac <<EMAIL>>
- Aditya Gupta <<EMAIL>> (adityagupta1089)
  * Added ignore_signatures to duplicate checker
- Adam Tuft <<EMAIL>>
- Adam Dangoor <<EMAIL>>
- 243f6a88 85a308d3 <<EMAIL>>


Co-Author
---------
The following persons were credited manually but did not commit themselves
under this name, or we did not manage to find their commits in the history.

- Agustin Toledo
- Amaury Forgeot d'Arc: check names imported from a module exists in the module
- Anthony Tan
- Axel Muller
- Benjamin Niemann: allow block level enabling/disabling of messages
- Bernard Nauwelaerts
- Bill Wendling
- Brian van den Broek: windows installation documentation
- Craig Henriques
- D. Alphus (Alphadelta14)
- Daniil Kharkov
- Eero Vuojolahti
- Fabio Zadrozny
- Gauthier Sebaux
- James DesLauriers
- manderj
- Mirko Friedenhagen
- Nicholas Smith
- Nuzula H. Yudaka (Nuzhuka)
- Pek Chhan
- Peter Hammond
- Pierre Rouleau
- Richard Goodman: simplifiable-if-expression (with Tomer Chachamu)
- Sebastian Ulrich
- Takashi Hirashima
- Thomas Snowden: fix missing-docstring for inner functions
- Wolfgang Grafen
- Yannick Brehon
