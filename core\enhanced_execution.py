"""
Enhanced Execution Engine Module
Handles order execution with verification across multiple sources
"""

import os
import json
import time
import logging
import datetime
from functools import wraps
from analysis.signal_aggregator import SignalAggregator
from core.resource_monitor import ResourceMonitor
from security.key_manager import KeyManager

# Set up logging
logger = logging.getLogger("enhanced_execution")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/live_trades.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# Retry logic with exponential backoff
def retry_with_backoff(max_retries=3, backoff_factor=2, start_sleep_time=0.5):
    """Retry decorator with exponential backoff.

    Args:
        max_retries (int): Maximum number of retry attempts
        backoff_factor (int): Multiplication factor for sleep time after each retry
        start_sleep_time (float): Initial sleep time between retries in seconds

    Returns:
        function: Decorated function with retry logic
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    wait_time = start_sleep_time * (backoff_factor ** retries)
                    logger.warning(f"Error: {str(e)}. Retrying in {wait_time:.2f} seconds...")
                    time.sleep(wait_time)
            raise RuntimeError(f"Failed after {max_retries} retries.")
        return wrapper
    return decorator

class EnhancedExecutionEngine:
    """
    Enhanced execution engine with verification across multiple sources
    """
    
    def __init__(self, exchange_client=None, dry_run=False, key_manager=None):
        """
        Initialize the enhanced execution engine
        
        Args:
            exchange_client: The exchange API client instance. If None, runs in simulation mode.
            dry_run (bool): If True, runs in test mode with no real trades.
            key_manager (KeyManager, optional): Key manager instance for API keys
        """
        self.exchange = exchange_client
        self.dry_run = dry_run
        self.key_manager = key_manager or KeyManager()
        
        self.signal_aggregator = SignalAggregator(self.key_manager)
        self.resource_monitor = ResourceMonitor()
        
        self.circuit_breaker_triggered = False
        self.last_trade_time = None
        self.min_trade_interval = 3600  # 1 hour in seconds
        
        self.balance = None
        self.min_balance = 20  # Minimum USDT balance required
        
        # Initialize trade history
        self.trade_history = self._load_trade_history()
    
    def _load_trade_history(self):
        """
        Load trade history from file
        
        Returns:
            list: Trade history
        """
        try:
            history_file = "logs/trade_history.json"
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"Error loading trade history: {e}")
            return []
    
    def _save_trade_history(self):
        """
        Save trade history to file
        """
        try:
            history_file = "logs/trade_history.json"
            with open(history_file, 'w') as f:
                json.dump(self.trade_history, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving trade history: {e}")
    
    def _log_trade(self, trade_data):
        """
        Log trade to file
        
        Args:
            trade_data (dict): Trade data
        """
        try:
            # Add trade to history
            self.trade_history.append(trade_data)
            self._save_trade_history()
            
            # Log to live_trades.txt
            with open("logs/live_trades.txt", 'a') as f:
                f.write(f"{trade_data['timestamp']} - {trade_data['side']} {trade_data['quantity']} {trade_data['symbol']} at ${trade_data['price']:.2f}\n")
            
            logger.info(f"Trade logged: {trade_data['side']} {trade_data['quantity']} {trade_data['symbol']} at ${trade_data['price']:.2f}")
        except Exception as e:
            logger.error(f"Error logging trade: {e}")
    
    def trigger_circuit_breaker(self):
        """
        Activate circuit breaker to pause trading
        """
        self.circuit_breaker_triggered = True
        logger.warning("Circuit breaker activated. Trading is paused.")
    
    def reset_circuit_breaker(self):
        """
        Reset circuit breaker to resume trading
        """
        self.circuit_breaker_triggered = False
        logger.info("Circuit breaker reset. Trading is resumed.")
    
    @retry_with_backoff()
    def get_balance(self):
        """
        Fetch account balance from exchange or simulate if no exchange
        
        Returns:
            float: Account balance in USDT
        """
        try:
            if self.exchange is not None and not self.dry_run:
                # Get actual balance from exchange
                balance_info = self.exchange.fetch_balance()
                # Most exchanges return USDT balance in the free property
                usdt_balance = balance_info.get('USDT', {}).get('free', 0)
                self.balance = usdt_balance
                return usdt_balance
            
            # Simulation mode
            self.balance = 1000.0
            return self.balance
        except Exception as e:
            logger.error(f"Error fetching balance: {e}")
            # Return last known balance or default
            return self.balance if self.balance is not None else 1000.0
    
    def check_system_health(self):
        """
        Check system health before executing trades
        
        Returns:
            bool: True if system is healthy, False otherwise
        """
        try:
            # Check resource usage
            if not self.resource_monitor.check_system_health():
                logger.warning("System health check failed. High resource usage detected.")
                return False
            
            # Check API health if not in dry run mode
            if self.exchange is not None and not self.dry_run:
                if not self.resource_monitor.check_api_health(self.exchange):
                    logger.warning("API health check failed.")
                    return False
            
            # Check if circuit breaker is triggered
            if self.circuit_breaker_triggered:
                logger.warning("Circuit breaker is active. System health check failed.")
                return False
            
            # Check if minimum trade interval has passed
            if self.last_trade_time is not None:
                elapsed = (datetime.datetime.now() - self.last_trade_time).total_seconds()
                if elapsed < self.min_trade_interval:
                    logger.info(f"Minimum trade interval not reached. {self.min_trade_interval - elapsed:.0f} seconds remaining.")
                    return False
            
            # Check if balance is sufficient
            balance = self.get_balance()
            if balance < self.min_balance:
                logger.warning(f"Insufficient balance: {balance} USDT (minimum: {self.min_balance} USDT)")
                return False
            
            logger.info("System health check passed.")
            return True
        
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
            return False
    
    def analyze_market_data(self, market_data):
        """
        Analyze market data using multiple sources
        
        Args:
            market_data: Market data from the exchange API
            
        Returns:
            dict or None: Trading signal if generated, None otherwise
        """
        try:
            logger.info("Analyzing market data with multiple sources...")
            
            # Use signal aggregator to analyze market data
            aggregated_signal = self.signal_aggregator.analyze_market(market_data)
            
            # Check if signal is valid
            if aggregated_signal["recommendation"] == "hold":
                logger.info("No trading signal generated. Holding position.")
                return None
            
            # Check confidence level
            if aggregated_signal["confidence"] < 0.7:
                logger.info(f"Signal confidence too low: {aggregated_signal['confidence']:.2f}. Minimum: 0.7")
                return None
            
            # Create trading signal
            signal = {
                "symbol": aggregated_signal["symbol"],
                "side": aggregated_signal["recommendation"],
                "price": aggregated_signal["price"],
                "quantity": self._calculate_position_size(aggregated_signal),
                "confidence": aggregated_signal["confidence"],
                "reason": aggregated_signal["reason"],
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            logger.info(f"Generated {signal['side']} signal for {signal['symbol']} at ${signal['price']:.2f}")
            return signal
        
        except Exception as e:
            logger.error(f"Error analyzing market data: {e}")
            return None
    
    def _calculate_position_size(self, signal):
        """
        Calculate position size based on balance and signal confidence
        
        Args:
            signal (dict): Trading signal
            
        Returns:
            float: Position size
        """
        try:
            # Get balance
            balance = self.get_balance()
            
            # Base position size on balance and confidence
            base_percent = 0.05  # 5% of balance
            confidence_factor = signal["confidence"]  # 0-1 scale
            
            # Adjust position size based on confidence
            position_percent = base_percent * confidence_factor
            
            # Calculate position size
            position_size = balance * position_percent
            
            # Ensure minimum position size
            min_position = 10  # Minimum 10 USDT
            if position_size < min_position:
                position_size = min_position
            
            # Ensure maximum position size
            max_position = balance * 0.2  # Maximum 20% of balance
            if position_size > max_position:
                position_size = max_position
            
            logger.info(f"Calculated position size: {position_size:.2f} USDT ({position_percent*100:.1f}% of balance)")
            return position_size
        
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 10  # Default to minimum position size
    
    async def execute_order(self, signal):
        """
        Execute a trading signal
        
        Args:
            signal (dict): Trading signal with symbol, side, quantity, etc.
            
        Returns:
            dict: Order result or None if execution failed
        """
        try:
            # Check system health
            if not self.check_system_health():
                logger.warning("System health check failed. Skipping order execution.")
                return None
            
            # Validate signal
            if not signal or not isinstance(signal, dict):
                logger.warning("Invalid signal format. Skipping order execution.")
                return None
            
            # Extract signal parameters
            symbol = signal.get("symbol")
            side = signal.get("side")
            quantity = signal.get("quantity")
            price = signal.get("price")
            
            if not all([symbol, side, quantity, price]):
                logger.error(f"Missing required signal parameters: {signal}")
                return None
            
            # Log the order
            logger.info(f"Executing {side} order for {quantity:.6f} USDT of {symbol} at ${price:.2f}")
            
            # Execute the order
            if self.dry_run:
                # Simulate order execution
                logger.info(f"[DRY RUN] Would execute {side} order for {quantity:.6f} USDT of {symbol} at ${price:.2f}")
                
                # Calculate quantity in crypto
                crypto_quantity = quantity / price
                
                # Create simulated order result
                order_result = {
                    "id": f"dry_run_{int(time.time())}",
                    "symbol": symbol,
                    "side": side,
                    "price": price,
                    "quantity": crypto_quantity,
                    "status": "filled",
                    "timestamp": datetime.datetime.now().isoformat()
                }
            else:
                # Execute real order
                if self.exchange is None:
                    logger.warning("No exchange client available. Cannot execute order.")
                    return None
                
                # Calculate quantity in crypto
                crypto_quantity = quantity / price
                
                # Execute order on exchange
                if side.lower() == "buy":
                    order_result = self.exchange.create_market_buy_order(symbol, crypto_quantity)
                else:  # sell
                    order_result = self.exchange.create_market_sell_order(symbol, crypto_quantity)
                
                logger.info(f"Order executed: {order_result}")
            
            # Update last trade time
            self.last_trade_time = datetime.datetime.now()
            
            # Log the trade
            trade_data = {
                "timestamp": datetime.datetime.now().isoformat(),
                "symbol": symbol,
                "side": side.upper(),
                "price": price,
                "quantity": crypto_quantity,
                "value_usdt": quantity,
                "order_id": order_result.get("id", "unknown"),
                "confidence": signal.get("confidence", 0),
                "reason": signal.get("reason", ""),
                "dry_run": self.dry_run
            }
            
            self._log_trade(trade_data)
            
            return order_result
        
        except Exception as e:
            logger.error(f"Error executing order: {e}")
            return None
