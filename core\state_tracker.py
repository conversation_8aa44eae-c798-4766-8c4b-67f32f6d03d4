"""
State Tracker Module
Tracks and persists bot state for crash recovery
"""

import os
import json
import time
import logging
import datetime
import threading
from pathlib import Path

# Set up logging
logger = logging.getLogger("state_tracker")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/state_tracker.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class StateTracker:
    """
    Tracks and persists bot state for crash recovery
    """
    
    def __init__(self, state_file="logs/bot_state.json", backup_dir="logs/state_backups", 
                 save_interval=60, max_backups=5):
        """
        Initialize the state tracker
        
        Args:
            state_file (str): Path to the state file
            backup_dir (str): Directory for state backups
            save_interval (int): Interval between automatic state saves in seconds
            max_backups (int): Maximum number of backup files to keep
        """
        self.state_file = state_file
        self.backup_dir = backup_dir
        self.save_interval = save_interval
        self.max_backups = max_backups
        
        # Create backup directory if it doesn't exist
        os.makedirs(backup_dir, exist_ok=True)
        
        # Initialize state
        self.state = {
            "version": 1,
            "last_updated": datetime.datetime.now().isoformat(),
            "start_time": datetime.datetime.now().isoformat(),
            "heartbeat": datetime.datetime.now().isoformat(),
            "status": "initializing",
            "positions": [],
            "trades": [],
            "errors": [],
            "performance": {
                "cpu_usage": 0,
                "memory_usage": 0
            },
            "settings": {}
        }
        
        # Load existing state if available
        self._load_state()
        
        # Update start time if this is a new session
        self.state["start_time"] = datetime.datetime.now().isoformat()
        self.state["status"] = "initializing"
        
        # Initialize auto-save thread
        self.auto_save_active = False
        self.auto_save_thread = None
        
        logger.info(f"StateTracker initialized with state file: {state_file}")
    
    def _load_state(self):
        """
        Load state from file
        """
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    loaded_state = json.load(f)
                
                # Update state with loaded values
                for key, value in loaded_state.items():
                    if key in self.state:
                        self.state[key] = value
                
                logger.info(f"State loaded from {self.state_file}")
                
                # Check if the loaded state is recent (within the last hour)
                try:
                    last_updated = datetime.datetime.fromisoformat(self.state.get("last_updated", ""))
                    now = datetime.datetime.now()
                    
                    if (now - last_updated).total_seconds() > 3600:  # 1 hour
                        logger.warning(f"Loaded state is old ({last_updated.isoformat()}), creating backup")
                        self._create_backup("old_state")
                except Exception:
                    pass
            else:
                logger.info(f"No existing state file found at {self.state_file}")
        except Exception as e:
            logger.error(f"Error loading state: {e}")
            
            # If the state file exists but is corrupted, create a backup
            if os.path.exists(self.state_file):
                logger.warning("State file exists but is corrupted, creating backup")
                self._create_backup("corrupted")
    
    def _create_backup(self, reason="manual"):
        """
        Create a backup of the current state file
        
        Args:
            reason (str): Reason for creating the backup
        """
        try:
            if os.path.exists(self.state_file):
                # Create backup filename with timestamp
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = os.path.join(self.backup_dir, f"state_{timestamp}_{reason}.json")
                
                # Copy state file to backup
                with open(self.state_file, 'r') as src:
                    with open(backup_file, 'w') as dst:
                        dst.write(src.read())
                
                logger.info(f"State backup created: {backup_file}")
                
                # Clean up old backups
                self._cleanup_old_backups()
                
                return backup_file
            else:
                logger.warning(f"Cannot create backup: State file {self.state_file} does not exist")
                return None
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return None
    
    def _cleanup_old_backups(self):
        """
        Clean up old backup files, keeping only the most recent ones
        """
        try:
            # Get all backup files
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.startswith("state_") and file.endswith(".json"):
                    file_path = os.path.join(self.backup_dir, file)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # Remove old backups
            if len(backup_files) > self.max_backups:
                for file_path, _ in backup_files[self.max_backups:]:
                    os.remove(file_path)
                    logger.info(f"Removed old backup: {file_path}")
        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")
    
    def save_state(self):
        """
        Save current state to file
        """
        try:
            # Update last updated timestamp
            self.state["last_updated"] = datetime.datetime.now().isoformat()
            
            # Save state to file
            with open(self.state_file, 'w') as f:
                json.dump(self.state, f, indent=2)
            
            logger.debug(f"State saved to {self.state_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving state: {e}")
            return False
    
    def update_status(self, status):
        """
        Update bot status
        
        Args:
            status (str): New status
        """
        self.state["status"] = status
        self.state["heartbeat"] = datetime.datetime.now().isoformat()
        self.save_state()
        logger.info(f"Status updated: {status}")
    
    def update_heartbeat(self):
        """
        Update heartbeat timestamp
        """
        self.state["heartbeat"] = datetime.datetime.now().isoformat()
        self.save_state()
    
    def add_position(self, position):
        """
        Add a new position
        
        Args:
            position (dict): Position information
        """
        # Add timestamp if not present
        if "timestamp" not in position:
            position["timestamp"] = datetime.datetime.now().isoformat()
        
        # Add position to list
        self.state["positions"].append(position)
        
        # Update state
        self.update_status("position_opened")
        self.save_state()
        logger.info(f"Position added: {position}")
    
    def update_position(self, position_id, updates):
        """
        Update an existing position
        
        Args:
            position_id (str): Position ID
            updates (dict): Updates to apply
        """
        for i, position in enumerate(self.state["positions"]):
            if position.get("id") == position_id:
                # Update position
                self.state["positions"][i].update(updates)
                
                # Update state
                self.save_state()
                logger.info(f"Position {position_id} updated: {updates}")
                return True
        
        logger.warning(f"Position {position_id} not found")
        return False
    
    def close_position(self, position_id, close_data):
        """
        Mark a position as closed
        
        Args:
            position_id (str): Position ID
            close_data (dict): Closing information
        """
        for i, position in enumerate(self.state["positions"]):
            if position.get("id") == position_id:
                # Update position
                self.state["positions"][i]["status"] = "closed"
                self.state["positions"][i]["close_time"] = datetime.datetime.now().isoformat()
                self.state["positions"][i].update(close_data)
                
                # Add to trades
                trade = self.state["positions"][i].copy()
                self.state["trades"].append(trade)
                
                # Update state
                self.update_status("position_closed")
                self.save_state()
                logger.info(f"Position {position_id} closed: {close_data}")
                return True
        
        logger.warning(f"Position {position_id} not found")
        return False
    
    def add_error(self, error_type, error_message, error_data=None):
        """
        Add an error to the error log
        
        Args:
            error_type (str): Type of error
            error_message (str): Error message
            error_data (dict, optional): Additional error data
        """
        error = {
            "type": error_type,
            "message": error_message,
            "timestamp": datetime.datetime.now().isoformat(),
            "data": error_data or {}
        }
        
        # Add error to list
        self.state["errors"].append(error)
        
        # Trim error list if it gets too long
        if len(self.state["errors"]) > 100:
            self.state["errors"] = self.state["errors"][-100:]
        
        # Update state
        self.save_state()
        logger.error(f"Error added: {error_type} - {error_message}")
    
    def update_performance(self, cpu_usage, memory_usage):
        """
        Update performance metrics
        
        Args:
            cpu_usage (float): CPU usage percentage
            memory_usage (float): Memory usage percentage
        """
        self.state["performance"]["cpu_usage"] = cpu_usage
        self.state["performance"]["memory_usage"] = memory_usage
        
        # Don't save state here to avoid too frequent writes
        # Just update the in-memory state
    
    def update_settings(self, settings):
        """
        Update bot settings
        
        Args:
            settings (dict): Bot settings
        """
        self.state["settings"].update(settings)
        self.save_state()
        logger.info(f"Settings updated: {settings}")
    
    def get_state(self):
        """
        Get current state
        
        Returns:
            dict: Current state
        """
        return self.state
    
    def get_open_positions(self):
        """
        Get all open positions
        
        Returns:
            list: Open positions
        """
        return [p for p in self.state["positions"] if p.get("status") != "closed"]
    
    def get_trades(self):
        """
        Get all completed trades
        
        Returns:
            list: Completed trades
        """
        return self.state["trades"]
    
    def get_errors(self):
        """
        Get error log
        
        Returns:
            list: Error log
        """
        return self.state["errors"]
    
    def clear_errors(self):
        """
        Clear error log
        """
        self.state["errors"] = []
        self.save_state()
        logger.info("Error log cleared")
    
    def start_auto_save(self):
        """
        Start automatic state saving
        """
        if self.auto_save_active:
            logger.warning("Auto-save is already active")
            return
        
        self.auto_save_active = True
        self.auto_save_thread = threading.Thread(target=self._auto_save_loop, daemon=True)
        self.auto_save_thread.start()
        
        logger.info(f"Auto-save started with interval: {self.save_interval}s")
    
    def stop_auto_save(self):
        """
        Stop automatic state saving
        """
        self.auto_save_active = False
        
        if self.auto_save_thread:
            self.auto_save_thread.join(timeout=2)
            self.auto_save_thread = None
        
        logger.info("Auto-save stopped")
    
    def _auto_save_loop(self):
        """
        Automatic state saving loop
        """
        while self.auto_save_active:
            try:
                # Update heartbeat
                self.update_heartbeat()
                
                # Sleep until next save
                time.sleep(self.save_interval)
            except Exception as e:
                logger.error(f"Error in auto-save loop: {e}")
                time.sleep(5)  # Sleep a bit before retrying
    
    def create_backup(self, reason="manual"):
        """
        Create a backup of the current state
        
        Args:
            reason (str): Reason for creating the backup
            
        Returns:
            str: Path to the backup file
        """
        return self._create_backup(reason)
    
    def restore_from_backup(self, backup_file=None):
        """
        Restore state from a backup file
        
        Args:
            backup_file (str, optional): Path to the backup file. If None, uses the most recent backup.
            
        Returns:
            bool: True if restore was successful, False otherwise
        """
        try:
            # If no backup file specified, find the most recent one
            if backup_file is None:
                backup_files = []
                for file in os.listdir(self.backup_dir):
                    if file.startswith("state_") and file.endswith(".json"):
                        file_path = os.path.join(self.backup_dir, file)
                        backup_files.append((file_path, os.path.getmtime(file_path)))
                
                if not backup_files:
                    logger.warning("No backup files found")
                    return False
                
                # Sort by modification time (newest first)
                backup_files.sort(key=lambda x: x[1], reverse=True)
                backup_file = backup_files[0][0]
            
            # Check if backup file exists
            if not os.path.exists(backup_file):
                logger.warning(f"Backup file {backup_file} does not exist")
                return False
            
            # Load backup
            with open(backup_file, 'r') as f:
                backup_state = json.load(f)
            
            # Update state with backup values
            for key, value in backup_state.items():
                if key in self.state:
                    self.state[key] = value
            
            # Update timestamps
            self.state["last_updated"] = datetime.datetime.now().isoformat()
            self.state["status"] = "restored_from_backup"
            
            # Save state
            self.save_state()
            
            logger.info(f"State restored from backup: {backup_file}")
            return True
        except Exception as e:
            logger.error(f"Error restoring from backup: {e}")
            return False
