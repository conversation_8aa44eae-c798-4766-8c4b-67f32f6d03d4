"""
AI Trading Bot
A trading bot that uses multiple AI services for market analysis and decision making
"""

import os
import sys
import time
import json
import logging
import datetime
import argparse
from dotenv import load_dotenv

# Import custom modules
from security.key_manager import KeyManager
from exchange.binance_api import BinanceAPI
from analysis.signal_aggregator import SignalAggregator
from core.memory_guard import MemoryGuard
from core.resource_monitor import ResourceMonitor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/ai_trading_bot.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("ai_trading_bot")

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

class AITradingBot:
    """
    Trading bot that uses multiple AI services for market analysis and decision making
    """
    
    def __init__(self, symbol="BTC/USDT", test_mode=True, config_file="config/bot_config.json"):
        """
        Initialize the trading bot
        
        Args:
            symbol (str): Trading pair symbol
            test_mode (bool): Whether to run in test mode (no real trades)
            config_file (str): Path to configuration file
        """
        self.symbol = symbol
        self.test_mode = test_mode
        self.config_file = config_file
        
        # Load configuration
        self.config = self._load_config()
        
        # Initialize key manager
        self.key_manager = KeyManager()
        
        # Initialize API keys from environment variables
        self._initialize_api_keys()
        
        # Initialize Binance API
        api_key = os.getenv("BINANCE_API_KEY")
        api_secret = os.getenv("BINANCE_API_SECRET")
        self.exchange = BinanceAPI(api_key, api_secret, use_testnet=test_mode)
        
        # Initialize signal aggregator
        self.signal_aggregator = SignalAggregator(self.key_manager)
        
        # Initialize resource monitor
        self.resource_monitor = ResourceMonitor()
        
        # Initialize memory guard
        self.memory_guard = MemoryGuard(
            cpu_threshold=self.config.get("cpu_threshold", 85),
            ram_threshold=self.config.get("ram_threshold", 75),
            check_interval=self.config.get("resource_check_interval", 30)
        )
        
        # Start resource monitoring
        self.memory_guard.start_monitoring()
        
        # Initialize trading parameters
        self.check_interval = self.config.get("check_interval", 60)  # seconds
        self.trade_amount = self.config.get("trade_amount", 100)  # USDT
        self.stop_loss = self.config.get("stop_loss", 0.02)  # 2%
        self.take_profit = self.config.get("take_profit", 0.03)  # 3%
        self.max_trades_per_day = self.config.get("max_trades_per_day", 5)
        
        # Initialize state variables
        self.current_position = None
        self.trade_history = []
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        logger.info(f"AITradingBot initialized for {symbol} in {'TEST' if test_mode else 'LIVE'} mode")
    
    def _load_config(self):
        """
        Load configuration from file
        
        Returns:
            dict: Configuration parameters
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                logger.info(f"Configuration loaded from {self.config_file}")
                return config
            else:
                logger.warning(f"Configuration file {self.config_file} not found. Using defaults.")
                return self._create_default_config()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return self._create_default_config()
    
    def _create_default_config(self):
        """
        Create default configuration
        
        Returns:
            dict: Default configuration parameters
        """
        default_config = {
            "check_interval": 60,  # seconds
            "trade_amount": 100,  # USDT
            "stop_loss": 0.02,  # 2%
            "take_profit": 0.03,  # 3%
            "max_trades_per_day": 5,
            "cpu_threshold": 85,
            "ram_threshold": 75,
            "resource_check_interval": 30,
            "confidence_threshold": 0.7,
            "min_sources": 3
        }
        
        # Save default configuration
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        with open(self.config_file, 'w') as f:
            json.dump(default_config, f, indent=4)
        
        logger.info(f"Default configuration created at {self.config_file}")
        return default_config
    
    def _initialize_api_keys(self):
        """
        Initialize API keys from environment variables
        """
        try:
            # OpenAI API key
            openai_api_key = os.getenv("OPENAI_API_KEY")
            if openai_api_key:
                self.key_manager.store_key("openai", "default", openai_api_key)
                logger.info("OpenAI API key initialized")
            else:
                logger.warning("OpenAI API key not found in environment variables")
            
            # DeepSeek API key
            deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")
            if deepseek_api_key:
                self.key_manager.store_key("deepseek", "default", deepseek_api_key)
                logger.info("DeepSeek API key initialized")
            else:
                logger.warning("DeepSeek API key not found in environment variables")
            
            # Qwen API key
            qwen_api_key = os.getenv("QWEN_API_KEY")
            if qwen_api_key:
                self.key_manager.store_key("qwen", "default", qwen_api_key)
                logger.info("Qwen API key initialized")
            else:
                logger.warning("Qwen API key not found in environment variables")
            
            # Google News API key
            google_news_api_key = os.getenv("GOOGLE_NEWS_API_KEY")
            if google_news_api_key:
                self.key_manager.store_key("google_news", "default", google_news_api_key)
                logger.info("Google News API key initialized")
            else:
                logger.warning("Google News API key not found in environment variables")
        
        except Exception as e:
            logger.error(f"Error initializing API keys: {e}")
    
    def check_price(self):
        """
        Check current price
        
        Returns:
            float: Current price
        """
        ticker = self.exchange.get_ticker(self.symbol)
        if ticker:
            price = ticker['last']
            logger.info(f"Current price for {self.symbol}: ${price:.2f}")
            return price
        else:
            logger.error(f"Failed to get ticker for {self.symbol}")
            return None
    
    def get_market_data(self):
        """
        Get market data for analysis
        
        Returns:
            dict: Market data
        """
        try:
            # Get current price
            ticker = self.exchange.get_ticker(self.symbol)
            if not ticker:
                logger.error(f"Failed to get ticker for {self.symbol}")
                return None
            
            # Get historical data
            ohlcv = self.exchange.get_ohlcv(self.symbol, timeframe='1h', limit=24)
            if not ohlcv:
                logger.error(f"Failed to get OHLCV data for {self.symbol}")
                return None
            
            # Calculate basic indicators
            closes = [candle['close'] for candle in ohlcv]
            
            # Simple Moving Averages
            sma_5 = sum(closes[-5:]) / 5 if len(closes) >= 5 else None
            sma_10 = sum(closes[-10:]) / 10 if len(closes) >= 10 else None
            sma_20 = sum(closes[-20:]) / 20 if len(closes) >= 20 else None
            
            # Prepare market data
            market_data = {
                "symbol": self.symbol,
                "price": ticker['last'],
                "timestamp": datetime.datetime.now().isoformat(),
                "historical": ohlcv,
                "indicators": {
                    "sma_5": sma_5,
                    "sma_10": sma_10,
                    "sma_20": sma_20
                }
            }
            
            return market_data
        
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return None
    
    def analyze_market(self):
        """
        Analyze market using multiple AI services
        
        Returns:
            dict: Aggregated signal
        """
        # Get market data
        market_data = self.get_market_data()
        if not market_data:
            logger.error("Failed to get market data for analysis")
            return None
        
        # Use signal aggregator to analyze market
        aggregated_signal = self.signal_aggregator.analyze_market(market_data)
        
        # Generate AI discussion
        discussion = self.signal_aggregator.generate_discussion(aggregated_signal.get("signals", {}))
        logger.info(f"AI Discussion:\n{discussion}")
        
        return aggregated_signal
    
    def execute_trade(self, signal):
        """
        Execute a trade based on the signal
        
        Args:
            signal (dict): Trading signal
            
        Returns:
            dict: Order information
        """
        if not signal:
            logger.warning("No signal provided for trade execution")
            return None
        
        recommendation = signal.get("recommendation", "hold")
        confidence = signal.get("confidence", 0)
        price = signal.get("price", 0)
        
        # Check if we should execute a trade
        if recommendation == "hold":
            logger.info("Signal recommends HOLD. No trade executed.")
            return None
        
        # Check if we've reached the daily trade limit
        current_date = datetime.datetime.now().date()
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        if self.daily_trade_count >= self.max_trades_per_day:
            logger.warning(f"Daily trade limit reached ({self.max_trades_per_day}). No trade executed.")
            return None
        
        # Check if we already have a position
        if self.current_position is not None:
            logger.warning("Already have an open position. No trade executed.")
            return None
        
        # Execute the trade
        if recommendation == "buy":
            if self.test_mode:
                logger.info(f"TEST MODE: Would execute BUY for {self.symbol} at ${price:.2f}")
                order = {
                    "id": f"test_{int(time.time())}",
                    "symbol": self.symbol,
                    "side": "buy",
                    "type": "market",
                    "price": price,
                    "amount": self.trade_amount / price,
                    "cost": self.trade_amount,
                    "timestamp": datetime.datetime.now().timestamp() * 1000,
                    "status": "closed"
                }
            else:
                logger.info(f"Executing BUY for {self.symbol} at market price")
                order = self.exchange.create_market_buy_order(self.symbol, self.trade_amount)
            
            if order:
                # Record position
                self.current_position = {
                    "side": "buy",
                    "entry_price": price,
                    "quantity": order.get("amount", self.trade_amount / price),
                    "timestamp": datetime.datetime.now().isoformat(),
                    "order_id": order.get("id", "test"),
                    "signal": signal
                }
                
                self.daily_trade_count += 1
                logger.info(f"BUY position opened: {self.current_position['quantity']:.8f} {self.symbol} at ${price:.2f}")
                return order
        
        elif recommendation == "sell":
            # For simplicity, we'll use the same trade amount for sell orders
            # In a real implementation, you would check the available balance
            if self.test_mode:
                logger.info(f"TEST MODE: Would execute SELL for {self.symbol} at ${price:.2f}")
                order = {
                    "id": f"test_{int(time.time())}",
                    "symbol": self.symbol,
                    "side": "sell",
                    "type": "market",
                    "price": price,
                    "amount": self.trade_amount / price,
                    "cost": self.trade_amount,
                    "timestamp": datetime.datetime.now().timestamp() * 1000,
                    "status": "closed"
                }
            else:
                logger.info(f"Executing SELL for {self.symbol} at market price")
                # Calculate quantity based on available balance
                balance = self.exchange.get_balance()
                base_currency = self.symbol.split('/')[0]
                available_amount = balance.get(base_currency, {}).get("free", 0)
                
                if available_amount > 0:
                    order = self.exchange.create_market_sell_order(self.symbol, available_amount)
                else:
                    logger.warning(f"Insufficient {base_currency} balance for SELL order")
                    return None
            
            if order:
                # Record position
                self.current_position = {
                    "side": "sell",
                    "entry_price": price,
                    "quantity": order.get("amount", self.trade_amount / price),
                    "timestamp": datetime.datetime.now().isoformat(),
                    "order_id": order.get("id", "test"),
                    "signal": signal
                }
                
                self.daily_trade_count += 1
                logger.info(f"SELL position opened: {self.current_position['quantity']:.8f} {self.symbol} at ${price:.2f}")
                return order
        
        return None
    
    def manage_positions(self, current_price):
        """
        Manage open positions (check stop loss and take profit)
        
        Args:
            current_price (float): Current price
            
        Returns:
            dict: Close order information if position was closed, None otherwise
        """
        if self.current_position is None:
            return None
        
        entry_price = self.current_position["entry_price"]
        side = self.current_position["side"]
        quantity = self.current_position["quantity"]
        
        if side == "buy":
            # Calculate profit/loss percentage
            pnl_percent = (current_price - entry_price) / entry_price * 100
            
            logger.info(f"Current position: BUY at ${entry_price:.2f}, current P&L: {pnl_percent:.2f}%")
            
            if pnl_percent <= -self.stop_loss * 100:
                # Stop loss triggered
                logger.info(f"Stop loss triggered: {pnl_percent:.2f}% loss")
                
                if self.test_mode:
                    logger.info(f"TEST MODE: Would close BUY position at ${current_price:.2f}")
                    close_order = {
                        "id": f"test_close_{int(time.time())}",
                        "symbol": self.symbol,
                        "side": "sell",
                        "type": "market",
                        "price": current_price,
                        "amount": quantity,
                        "cost": quantity * current_price,
                        "timestamp": datetime.datetime.now().timestamp() * 1000,
                        "status": "closed"
                    }
                else:
                    logger.info(f"Closing BUY position at market price")
                    close_order = self.exchange.create_market_sell_order(self.symbol, quantity)
                
                if close_order:
                    # Record trade in history
                    self.trade_history.append({
                        "entry": self.current_position,
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "stop_loss",
                            "pnl_percent": pnl_percent,
                            "order_id": close_order.get("id", "test")
                        }
                    })
                    
                    # Clear current position
                    self.current_position = None
                    
                    logger.info(f"Position closed with {pnl_percent:.2f}% loss")
                    return close_order
            
            elif pnl_percent >= self.take_profit * 100:
                # Take profit triggered
                logger.info(f"Take profit triggered: {pnl_percent:.2f}% profit")
                
                if self.test_mode:
                    logger.info(f"TEST MODE: Would close BUY position at ${current_price:.2f}")
                    close_order = {
                        "id": f"test_close_{int(time.time())}",
                        "symbol": self.symbol,
                        "side": "sell",
                        "type": "market",
                        "price": current_price,
                        "amount": quantity,
                        "cost": quantity * current_price,
                        "timestamp": datetime.datetime.now().timestamp() * 1000,
                        "status": "closed"
                    }
                else:
                    logger.info(f"Closing BUY position at market price")
                    close_order = self.exchange.create_market_sell_order(self.symbol, quantity)
                
                if close_order:
                    # Record trade in history
                    self.trade_history.append({
                        "entry": self.current_position,
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "take_profit",
                            "pnl_percent": pnl_percent,
                            "order_id": close_order.get("id", "test")
                        }
                    })
                    
                    # Clear current position
                    self.current_position = None
                    
                    logger.info(f"Position closed with {pnl_percent:.2f}% profit")
                    return close_order
        
        elif side == "sell":
            # For short positions, profit/loss is reversed
            pnl_percent = (entry_price - current_price) / entry_price * 100
            
            logger.info(f"Current position: SELL at ${entry_price:.2f}, current P&L: {pnl_percent:.2f}%")
            
            if pnl_percent <= -self.stop_loss * 100:
                # Stop loss triggered
                logger.info(f"Stop loss triggered: {pnl_percent:.2f}% loss")
                
                if self.test_mode:
                    logger.info(f"TEST MODE: Would close SELL position at ${current_price:.2f}")
                    close_order = {
                        "id": f"test_close_{int(time.time())}",
                        "symbol": self.symbol,
                        "side": "buy",
                        "type": "market",
                        "price": current_price,
                        "amount": quantity,
                        "cost": quantity * current_price,
                        "timestamp": datetime.datetime.now().timestamp() * 1000,
                        "status": "closed"
                    }
                else:
                    logger.info(f"Closing SELL position at market price")
                    close_order = self.exchange.create_market_buy_order(self.symbol, quantity * current_price)
                
                if close_order:
                    # Record trade in history
                    self.trade_history.append({
                        "entry": self.current_position,
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "stop_loss",
                            "pnl_percent": pnl_percent,
                            "order_id": close_order.get("id", "test")
                        }
                    })
                    
                    # Clear current position
                    self.current_position = None
                    
                    logger.info(f"Position closed with {pnl_percent:.2f}% loss")
                    return close_order
            
            elif pnl_percent >= self.take_profit * 100:
                # Take profit triggered
                logger.info(f"Take profit triggered: {pnl_percent:.2f}% profit")
                
                if self.test_mode:
                    logger.info(f"TEST MODE: Would close SELL position at ${current_price:.2f}")
                    close_order = {
                        "id": f"test_close_{int(time.time())}",
                        "symbol": self.symbol,
                        "side": "buy",
                        "type": "market",
                        "price": current_price,
                        "amount": quantity,
                        "cost": quantity * current_price,
                        "timestamp": datetime.datetime.now().timestamp() * 1000,
                        "status": "closed"
                    }
                else:
                    logger.info(f"Closing SELL position at market price")
                    close_order = self.exchange.create_market_buy_order(self.symbol, quantity * current_price)
                
                if close_order:
                    # Record trade in history
                    self.trade_history.append({
                        "entry": self.current_position,
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "take_profit",
                            "pnl_percent": pnl_percent,
                            "order_id": close_order.get("id", "test")
                        }
                    })
                    
                    # Clear current position
                    self.current_position = None
                    
                    logger.info(f"Position closed with {pnl_percent:.2f}% profit")
                    return close_order
        
        return None
    
    def save_trade_history(self):
        """
        Save trade history to file
        """
        try:
            history_file = f"logs/trade_history_{datetime.datetime.now().strftime('%Y%m%d')}.json"
            
            with open(history_file, 'w') as f:
                json.dump(self.trade_history, f, indent=4)
            
            logger.info(f"Trade history saved to {history_file}")
        except Exception as e:
            logger.error(f"Error saving trade history: {e}")
    
    def run(self):
        """
        Run the trading bot
        """
        logger.info(f"Starting AITradingBot in {'TEST' if self.test_mode else 'LIVE'} mode")
        logger.info(f"Trading {self.symbol} with {self.trade_amount} USDT per trade")
        logger.info(f"Stop Loss: {self.stop_loss*100}%, Take Profit: {self.take_profit*100}%")
        
        try:
            iteration = 1
            
            while True:
                logger.info(f"Iteration {iteration}")
                
                # Check system health
                if not self.resource_monitor.check_system_health():
                    logger.warning("System health check failed. Waiting before next iteration.")
                    time.sleep(self.check_interval)
                    continue
                
                # Check API health
                if not self.resource_monitor.check_api_health(self.exchange):
                    logger.warning("API health check failed. Waiting before next iteration.")
                    time.sleep(self.check_interval)
                    continue
                
                # Check current price
                current_price = self.check_price()
                if current_price is None:
                    logger.error("Failed to get current price. Waiting before next iteration.")
                    time.sleep(self.check_interval)
                    continue
                
                # Manage open positions
                if self.current_position is not None:
                    self.manage_positions(current_price)
                
                # If no open position, analyze market for new signals
                if self.current_position is None:
                    # Analyze market
                    signal = self.analyze_market()
                    
                    if signal:
                        # Execute trade if signal is generated
                        self.execute_trade(signal)
                    else:
                        logger.warning("No valid signal generated")
                
                # Save trade history periodically
                if iteration % 10 == 0:
                    self.save_trade_history()
                
                # Wait for next iteration
                logger.info(f"Waiting {self.check_interval} seconds until next check...")
                time.sleep(self.check_interval)
                iteration += 1
        
        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        except Exception as e:
            logger.error(f"Error: {e}")
        finally:
            # Stop resource monitoring
            self.memory_guard.stop_monitoring()
            
            # Save trade history
            self.save_trade_history()
            
            logger.info("Bot shutting down")

def main():
    """
    Main function
    """
    # Load environment variables
    load_dotenv()
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="AI Trading Bot")
    parser.add_argument("--symbol", type=str, default="BTC/USDT", help="Trading pair symbol")
    parser.add_argument("--test", action="store_true", help="Run in test mode (no real trades)")
    parser.add_argument("--config", type=str, default="config/bot_config.json", help="Path to configuration file")
    args = parser.parse_args()
    
    # Initialize and run bot
    bot = AITradingBot(symbol=args.symbol, test_mode=args.test, config_file=args.config)
    bot.run()

if __name__ == "__main__":
    main()
