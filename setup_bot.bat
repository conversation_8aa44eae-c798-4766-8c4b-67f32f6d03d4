@echo off
echo ========================================
echo TRADING BOT SETUP
echo ========================================
echo.
echo This script will set up the trading bot environment.
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3.8 or higher and try again.
    goto end
)

REM Create required directories
echo Creating required directories...
mkdir logs 2>nul
mkdir security 2>nul
mkdir analysis 2>nul
mkdir config 2>nul
echo Directories created.
echo.

REM Install required packages
echo Installing required packages...
pip install -r requirements.txt
echo.

REM Initialize API keys
echo Initializing API keys...
python initialize_keys.py
echo.

echo Setup complete!
echo.
echo You can now run the trading bot using:
echo - run_enhanced_bot.bat
echo.

:end
pause
