@echo off
echo ========================================
echo AI TRADING BOT - LIVE MODE
echo ========================================
echo.
echo WARNING: This will use REAL MONEY for trading!
echo Please make sure your API keys are correctly set in the .env file.
echo.
echo Trading Parameters:
echo - Symbol: BTC/USDT
echo - Trade Amount: Dynamic (10-30%% of available balance)
echo - Stop Loss: 2.0%%
echo - Take Profit: 3.0%%
echo - Max Trades Per Day: 50
echo.
echo Press Ctrl+C to stop the bot at any time.
echo.
set /p confirm=Are you ABSOLUTELY SURE you want to start LIVE trading with REAL MONEY? (yes/no):

if /i "%confirm%" NEQ "yes" (
    echo Live trading cancelled.
    goto end
)

echo.
echo Starting AI trading bot in LIVE mode...
python simple_ai_bot.py --live --iterations 0

:end
echo.
pause
