#!/usr/bin/env python3
"""
Test script to validate the implementation fixes for SP.Bot Enhanced v2.0.0
This script tests the critical fixes made to the AI integration and technical indicators
"""

import os
import sys
import json
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import modules to test
from ai_services.ai_connector import MultiAIConnector
from analysis.indicators import (
    calculate_ichimoku_cloud, 
    get_ichimoku_signals, 
    detect_cloud_support_resistance
)


class TestImplementationFixes(unittest.TestCase):
    """Test the implementation fixes"""
    
    def setUp(self):
        """Set up test environment"""
        self.ai_connector = MultiAIConnector()
        
        # Sample market data
        self.sample_market_data = {
            "symbol": "BTC/USDT",
            "current_price": 45000.0,
            "price_change_24h": 2.5,
            "volume_24h": 1000000,
            "indicators": {
                "ema50": 44500.0,
                "ema200": 43000.0,
                "rsi": 65.0,
                "macd": 150.0,
                "atr": 800.0,
                "bollinger_upper": 46000.0,
                "bollinger_lower": 44000.0,
                "is_uptrend": True,
                "is_downtrend": False
            }
        }
        
        # Sample price data for technical indicators
        self.sample_highs = [45000, 45200, 45100, 45300, 45250, 45400, 45350, 45500, 45450, 45600] * 6
        self.sample_lows = [44800, 44900, 44850, 45000, 44950, 45100, 45050, 45200, 45150, 45300] * 6
        self.sample_closes = [44900, 45100, 44950, 45200, 45100, 45300, 45200, 45400, 45300, 45500] * 6
    
    def test_ai_connector_fallback_analysis(self):
        """Test AI connector fallback analysis"""
        print("\n=== Testing AI Connector Fallback Analysis ===")
        
        # Test fallback analysis
        fallback = self.ai_connector._get_fallback_analysis("openai")
        
        self.assertIsNotNone(fallback)
        self.assertEqual(fallback["service"], "openai")
        self.assertEqual(fallback["account_id"], "fallback")
        self.assertEqual(fallback["analysis"]["recommendation"], "hold")
        self.assertEqual(fallback["analysis"]["confidence"], 30)
        
        print("✅ Fallback analysis working correctly")
    
    def test_ai_response_validation(self):
        """Test AI response validation"""
        print("\n=== Testing AI Response Validation ===")
        
        # Test valid response
        valid_response = {
            "recommendation": "buy",
            "confidence": 75,
            "sentiment": "bullish",
            "explanation": "Good market conditions",
            "key_factors": ["momentum", "volume"]
        }
        
        validated = self.ai_connector._validate_ai_response(valid_response)
        self.assertEqual(validated["recommendation"], "buy")
        self.assertEqual(validated["confidence"], 75)
        self.assertEqual(validated["sentiment"], "bullish")
        
        print("✅ Valid response validation working")
        
        # Test invalid response
        invalid_response = {
            "recommendation": "invalid_action",
            "confidence": 150,  # Invalid
            "sentiment": "unknown",
            "explanation": None
        }
        
        validated = self.ai_connector._validate_ai_response(invalid_response)
        self.assertEqual(validated["recommendation"], "hold")
        self.assertEqual(validated["confidence"], 50)
        self.assertEqual(validated["sentiment"], "neutral")
        
        print("✅ Invalid response validation working")
    
    def test_fallback_parser(self):
        """Test fallback response parser"""
        print("\n=== Testing Fallback Response Parser ===")
        
        # Test clear buy signal
        buy_text = "I recommend BUY with 85% confidence. The market is bullish."
        parsed = self.ai_connector._parse_ai_response_fallback(buy_text)
        
        self.assertEqual(parsed["recommendation"], "buy")
        self.assertEqual(parsed["confidence"], 85)
        self.assertEqual(parsed["sentiment"], "bullish")
        
        print("✅ Buy signal parsing working")
        
        # Test sell signal
        sell_text = "SELL recommendation with 70% confidence. Bearish outlook."
        parsed = self.ai_connector._parse_ai_response_fallback(sell_text)
        
        self.assertEqual(parsed["recommendation"], "sell")
        self.assertEqual(parsed["confidence"], 70)
        self.assertEqual(parsed["sentiment"], "bearish")
        
        print("✅ Sell signal parsing working")
    
    def test_market_data_formatting(self):
        """Test market data formatting"""
        print("\n=== Testing Market Data Formatting ===")
        
        formatted = self.ai_connector._format_market_data(self.sample_market_data)
        
        # Check that important data is included
        self.assertIn("BTC/USDT", formatted)
        self.assertIn("45000.0", formatted)
        self.assertIn("EMA50", formatted)
        self.assertIn("RSI", formatted)
        
        print("✅ Market data formatting working")
        print(f"Sample formatted data: {formatted[:200]}...")
    
    def test_ichimoku_cloud_calculation(self):
        """Test Ichimoku Cloud calculation"""
        print("\n=== Testing Ichimoku Cloud Calculation ===")
        
        ichimoku = calculate_ichimoku_cloud(
            self.sample_highs, 
            self.sample_lows, 
            self.sample_closes
        )
        
        self.assertIsNotNone(ichimoku)
        self.assertIn('tenkan_sen', ichimoku)
        self.assertIn('kijun_sen', ichimoku)
        self.assertIn('senkou_span_a', ichimoku)
        self.assertIn('senkou_span_b', ichimoku)
        self.assertIn('chikou_span', ichimoku)
        
        # Check that we have valid data
        self.assertTrue(len(ichimoku['tenkan_sen']) > 0)
        self.assertTrue(len(ichimoku['kijun_sen']) > 0)
        
        print("✅ Ichimoku Cloud calculation working")
        print(f"Tenkan-sen (last 3): {ichimoku['tenkan_sen'][-3:]}")
        print(f"Kijun-sen (last 3): {ichimoku['kijun_sen'][-3:]}")
    
    def test_ichimoku_signals(self):
        """Test Ichimoku signal generation"""
        print("\n=== Testing Ichimoku Signal Generation ===")
        
        ichimoku = calculate_ichimoku_cloud(
            self.sample_highs, 
            self.sample_lows, 
            self.sample_closes
        )
        
        signals = get_ichimoku_signals(ichimoku, 45500)
        
        self.assertIsNotNone(signals)
        self.assertIn('signal', signals)
        self.assertIn('strength', signals)
        self.assertIn('cloud_position', signals)
        self.assertIn('trend_direction', signals)
        
        # Signal should be valid
        self.assertIn(signals['signal'], ['buy', 'sell', 'hold'])
        self.assertTrue(0 <= signals['strength'] <= 100)
        
        print("✅ Ichimoku signal generation working")
        print(f"Signal: {signals['signal']}")
        print(f"Strength: {signals['strength']}")
        print(f"Cloud position: {signals['cloud_position']}")
        print(f"Trend direction: {signals['trend_direction']}")
    
    def test_cloud_support_resistance(self):
        """Test cloud support/resistance detection"""
        print("\n=== Testing Cloud Support/Resistance Detection ===")
        
        ichimoku = calculate_ichimoku_cloud(
            self.sample_highs, 
            self.sample_lows, 
            self.sample_closes
        )
        
        sr_levels = detect_cloud_support_resistance(ichimoku)
        
        self.assertIsNotNone(sr_levels)
        self.assertIn('support_levels', sr_levels)
        self.assertIn('resistance_levels', sr_levels)
        self.assertIn('cloud_thickness', sr_levels)
        
        print("✅ Cloud support/resistance detection working")
        print(f"Support levels: {sr_levels['support_levels']}")
        print(f"Resistance levels: {sr_levels['resistance_levels']}")
        print(f"Cloud thickness: {sr_levels['cloud_thickness']:.2f}%")
    
    def test_openai_api_structure(self):
        """Test OpenAI API call structure (mocked)"""
        print("\n=== Testing OpenAI API Call Structure ===")
        
        # Mock account
        mock_account = Mock()
        mock_account.get_api_key.return_value = "test_key"
        mock_account.account_id = "test_account"
        mock_account.mark_used = Mock()
        
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "recommendation": "buy",
            "confidence": 75,
            "sentiment": "bullish",
            "explanation": "Strong signals",
            "key_factors": ["momentum"]
        })
        mock_response.usage.total_tokens = 350
        
        with patch('openai.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_openai.return_value = mock_client
            mock_client.chat.completions.create.return_value = mock_response
            
            # Test API call
            result = self.ai_connector._call_openai_api(
                mock_account,
                "test market data"
            )
            
            self.assertIsNotNone(result)
            self.assertEqual(result["service"], "openai")
            self.assertEqual(result["analysis"]["recommendation"], "buy")
            
            print("✅ OpenAI API call structure working")
    
    def test_deepseek_api_structure(self):
        """Test DeepSeek API call structure (mocked)"""
        print("\n=== Testing DeepSeek API Call Structure ===")
        
        # Mock account
        mock_account = Mock()
        mock_account.get_api_key.return_value = "test_key"
        mock_account.account_id = "test_account"
        mock_account.mark_used = Mock()
        
        # Mock response
        mock_response = Mock()
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": json.dumps({
                        "recommendation": "sell",
                        "confidence": 80,
                        "sentiment": "bearish",
                        "explanation": "Overbought",
                        "key_factors": ["overbought"]
                    })
                }
            }],
            "usage": {"total_tokens": 420}
        }
        mock_response.raise_for_status = Mock()
        
        with patch('requests.post') as mock_post:
            mock_post.return_value = mock_response
            
            result = self.ai_connector._call_deepseek_api(
                mock_account,
                "test market data"
            )
            
            self.assertIsNotNone(result)
            self.assertEqual(result["service"], "deepseek")
            self.assertEqual(result["analysis"]["recommendation"], "sell")
            
            print("✅ DeepSeek API call structure working")
    
    def test_qwen_api_structure(self):
        """Test Qwen API call structure (mocked)"""
        print("\n=== Testing Qwen API Call Structure ===")
        
        # Mock account
        mock_account = Mock()
        mock_account.get_api_key.return_value = "test_key"
        mock_account.account_id = "test_account"
        mock_account.mark_used = Mock()
        
        # Mock response
        mock_response = Mock()
        mock_response.json.return_value = {
            "output": {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "recommendation": "hold",
                            "confidence": 60,
                            "sentiment": "neutral",
                            "explanation": "Mixed signals",
                            "key_factors": ["uncertainty"]
                        })
                    }
                }]
            },
            "usage": {"total_tokens": 380}
        }
        mock_response.raise_for_status = Mock()
        
        with patch('requests.post') as mock_post:
            mock_post.return_value = mock_response
            
            result = self.ai_connector._call_qwen_api(
                mock_account,
                "test market data"
            )
            
            self.assertIsNotNone(result)
            self.assertEqual(result["service"], "qwen")
            self.assertEqual(result["analysis"]["recommendation"], "hold")
            
            print("✅ Qwen API call structure working")


def run_comprehensive_test():
    """Run comprehensive test of implementation fixes"""
    print("=" * 80)
    print("SP.Bot Enhanced v2.0.0 - Implementation Fixes Validation")
    print("=" * 80)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestImplementationFixes)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    if result.wasSuccessful():
        print("🎉 ALL TESTS PASSED!")
        print("✅ AI Integration fixes are working correctly")
        print("✅ Ichimoku Cloud implementation is functional")
        print("✅ Response validation and error handling working")
        print("\nNext steps:")
        print("1. Test with real API keys")
        print("2. Implement ADX and OBV indicators")
        print("3. Run integration tests")
        print("4. Validate with paper trading")
    else:
        print("❌ SOME TESTS FAILED!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        
        for test, error in result.failures + result.errors:
            print(f"\n❌ {test}: {error}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
