info = {
    "name": "sw",
    "date_order": "DM<PERSON>",
    "january": [
        "jan",
        "januari"
    ],
    "february": [
        "feb",
        "februari"
    ],
    "march": [
        "mac",
        "machi"
    ],
    "april": [
        "apr",
        "aprili"
    ],
    "may": [
        "mei"
    ],
    "june": [
        "jun",
        "juni"
    ],
    "july": [
        "jul",
        "julai"
    ],
    "august": [
        "ago",
        "agosti"
    ],
    "september": [
        "sep",
        "septemba"
    ],
    "october": [
        "okt",
        "oktoba"
    ],
    "november": [
        "nov",
        "novemba"
    ],
    "december": [
        "des",
        "desemba"
    ],
    "monday": [
        "jumatatu"
    ],
    "tuesday": [
        "jumanne"
    ],
    "wednesday": [
        "jumatano"
    ],
    "thursday": [
        "alhamisi"
    ],
    "friday": [
        "iju<PERSON>a"
    ],
    "saturday": [
        "jumamosi"
    ],
    "sunday": [
        "jumapili"
    ],
    "am": [
        "am",
        "asubuhi"
    ],
    "pm": [
        "mchana",
        "pm"
    ],
    "year": [
        "mwaka"
    ],
    "month": [
        "mwezi"
    ],
    "week": [
        "wiki"
    ],
    "day": [
        "siku"
    ],
    "hour": [
        "saa"
    ],
    "minute": [
        "dak",
        "dakika"
    ],
    "second": [
        "sek",
        "sekunde"
    ],
    "relative-type": {
        "0 day ago": [
            "leo"
        ],
        "0 hour ago": [
            "saa hii"
        ],
        "0 minute ago": [
            "dakika hii"
        ],
        "0 month ago": [
            "mwezi huu"
        ],
        "0 second ago": [
            "sasa hivi"
        ],
        "0 week ago": [
            "wiki hii"
        ],
        "0 year ago": [
            "mwaka huu"
        ],
        "1 day ago": [
            "jana"
        ],
        "1 month ago": [
            "mwezi uliopita"
        ],
        "1 week ago": [
            "wiki iliyopita"
        ],
        "1 year ago": [
            "mwaka uliopita"
        ],
        "in 1 day": [
            "kesho"
        ],
        "in 1 month": [
            "mwezi ujao"
        ],
        "in 1 week": [
            "wiki ijayo"
        ],
        "in 1 year": [
            "mwaka ujao"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "siku (\\d+[.,]?\\d*) iliyopita",
            "siku (\\d+[.,]?\\d*) zilizopita"
        ],
        "\\1 hour ago": [
            "saa (\\d+[.,]?\\d*) iliyopita",
            "saa (\\d+[.,]?\\d*) zilizopita"
        ],
        "\\1 minute ago": [
            "dakika (\\d+[.,]?\\d*) iliyopita",
            "dakika (\\d+[.,]?\\d*) zilizopita"
        ],
        "\\1 month ago": [
            "miezi (\\d+[.,]?\\d*) iliyopita",
            "mwezi (\\d+[.,]?\\d*) uliopita"
        ],
        "\\1 second ago": [
            "sekunde (\\d+[.,]?\\d*) iliyopita",
            "sekunde (\\d+[.,]?\\d*) zilizopita"
        ],
        "\\1 week ago": [
            "wiki (\\d+[.,]?\\d*) iliyopita",
            "wiki (\\d+[.,]?\\d*) zilizopita"
        ],
        "\\1 year ago": [
            "miaka (\\d+[.,]?\\d*) iliyopita",
            "mwaka (\\d+[.,]?\\d*) uliopita"
        ],
        "in \\1 day": [
            "baada ya siku (\\d+[.,]?\\d*)"
        ],
        "in \\1 hour": [
            "baada ya saa (\\d+[.,]?\\d*)"
        ],
        "in \\1 minute": [
            "baada ya dakika (\\d+[.,]?\\d*)"
        ],
        "in \\1 month": [
            "baada ya miezi (\\d+[.,]?\\d*)",
            "baada ya mwezi (\\d+[.,]?\\d*)"
        ],
        "in \\1 second": [
            "baada ya sekunde (\\d+[.,]?\\d*)"
        ],
        "in \\1 week": [
            "baada ya wiki (\\d+[.,]?\\d*)"
        ],
        "in \\1 year": [
            "baada ya miaka (\\d+[.,]?\\d*)",
            "baada ya mwaka (\\d+[.,]?\\d*)"
        ]
    },
    "locale_specific": {
        "sw-CD": {
            "name": "sw-CD",
            "week": [
                "juma"
            ]
        },
        "sw-KE": {
            "name": "sw-KE"
        },
        "sw-UG": {
            "name": "sw-UG"
        }
    },
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
