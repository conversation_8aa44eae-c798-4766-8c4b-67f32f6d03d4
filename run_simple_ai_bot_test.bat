@echo off
echo ========================================
echo AI TRADING BOT - TEST MODE
echo ========================================
echo.
echo This will use Binance Testnet for trading (no real money).
echo Please make sure your API keys are correctly set in the .env file.
echo.
echo Trading Parameters:
echo - Symbol: BTC/USDT
echo - Trade Amount: Dynamic (10-30%% of available balance)
echo - Stop Loss: 2.0%%
echo - Take Profit: 3.0%%
echo - Max Trades Per Day: 50
echo.
set /p confirm=Do you want to start TEST trading? (yes/no):

if /i "%confirm%" NEQ "yes" (
    echo Test trading cancelled.
    goto end
)

echo.
echo Starting AI trading bot in TEST mode...
python simple_ai_bot.py --test --iterations 10

:end
echo.
pause
