info = {
    "name": "en",
    "date_order": "MD<PERSON>",
    "january": [
        "jan",
        "january"
    ],
    "february": [
        "feb",
        "february"
    ],
    "march": [
        "mar",
        "march"
    ],
    "april": [
        "apr",
        "april"
    ],
    "may": [
        "may"
    ],
    "june": [
        "jun",
        "june"
    ],
    "july": [
        "jul",
        "july"
    ],
    "august": [
        "aug",
        "august"
    ],
    "september": [
        "sep",
        "september",
        "sept"
    ],
    "october": [
        "oct",
        "october"
    ],
    "november": [
        "nov",
        "november"
    ],
    "december": [
        "dec",
        "december"
    ],
    "monday": [
        "mon",
        "monday"
    ],
    "tuesday": [
        "tue",
        "tuesday",
        "Tues"
    ],
    "wednesday": [
        "wed",
        "wednesday"
    ],
    "thursday": [
        "thu",
        "thursday"
    ],
    "friday": [
        "fri",
        "friday"
    ],
    "saturday": [
        "sat",
        "saturday"
    ],
    "sunday": [
        "sun",
        "sunday"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "year",
        "yr",
        "y",
        "years"
    ],
    "month": [
        "mo",
        "month",
        "months"
    ],
    "week": [
        "week",
        "wk",
        "weeks"
    ],
    "day": [
        "day",
        "d",
        "days"
    ],
    "hour": [
        "hour",
        "hr",
        "h",
        "hours",
        "hrs"
    ],
    "minute": [
        "min",
        "minute",
        "m",
        "minutes",
        "mins"
    ],
    "second": [
        "sec",
        "second",
        "s",
        "seconds",
        "secs"
    ],
    "relative-type": {
        "0 day ago": [
            "today",
            "till date"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this mo",
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week",
            "this wk"
        ],
        "0 year ago": [
            "this year",
            "this yr"
        ],
        "1 day ago": [
            "yesterday"
        ],
        "1 month ago": [
            "last mo",
            "last month"
        ],
        "1 week ago": [
            "last week",
            "last wk"
        ],
        "1 year ago": [
            "last year",
            "last yr"
        ],
        "in 1 day": [
            "tomorrow"
        ],
        "in 1 month": [
            "next mo",
            "next month"
        ],
        "in 1 week": [
            "next week",
            "next wk"
        ],
        "in 1 year": [
            "next year",
            "next yr"
        ],
        "2 day ago": [
            "day before yesterday"
        ],
        "1 decade ago": [
            "last decade",
            "this decade"
        ],
        "in 1 decade": [
            "next decade"
        ],
        "in 2 day": [
            "day after tomorrow"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) day ago",
            "(\\d+[.,]?\\d*) days ago"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) hour ago",
            "(\\d+[.,]?\\d*) hours ago",
            "(\\d+[.,]?\\d*) hr ago"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) min ago",
            "(\\d+[.,]?\\d*) minute ago",
            "(\\d+[.,]?\\d*) minutes ago"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) mo ago",
            "(\\d+[.,]?\\d*) month ago",
            "(\\d+[.,]?\\d*) months ago"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) sec ago",
            "(\\d+[.,]?\\d*) second ago",
            "(\\d+[.,]?\\d*) seconds ago"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) week ago",
            "(\\d+[.,]?\\d*) weeks ago",
            "(\\d+[.,]?\\d*) wk ago"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) year ago",
            "(\\d+[.,]?\\d*) years ago",
            "(\\d+[.,]?\\d*) yr ago"
        ],
        "in \\1 day": [
            "in (\\d+[.,]?\\d*) day",
            "in (\\d+[.,]?\\d*) days"
        ],
        "in \\1 hour": [
            "in (\\d+[.,]?\\d*) hour",
            "in (\\d+[.,]?\\d*) hours",
            "in (\\d+[.,]?\\d*) hr"
        ],
        "in \\1 minute": [
            "in (\\d+[.,]?\\d*) min",
            "in (\\d+[.,]?\\d*) minute",
            "in (\\d+[.,]?\\d*) minutes"
        ],
        "in \\1 month": [
            "in (\\d+[.,]?\\d*) mo",
            "in (\\d+[.,]?\\d*) month",
            "in (\\d+[.,]?\\d*) months"
        ],
        "in \\1 second": [
            "in (\\d+[.,]?\\d*) sec",
            "in (\\d+[.,]?\\d*) second",
            "in (\\d+[.,]?\\d*) seconds"
        ],
        "in \\1 week": [
            "in (\\d+[.,]?\\d*) week",
            "in (\\d+[.,]?\\d*) weeks",
            "in (\\d+[.,]?\\d*) wk"
        ],
        "in \\1 year": [
            "in (\\d+[.,]?\\d*) year",
            "in (\\d+[.,]?\\d*) years",
            "in (\\d+[.,]?\\d*) yr"
        ],
        "in \\1 decade": [
            "in (\\d+[.,]?\\d*) decades?"
        ],
        "\\1 decade ago": [
            "(\\d+[.,]?\\d*) decades? ago"
        ]
    },
    "locale_specific": {
        "en-001": {
            "name": "en-001",
            "date_order": "DMY"
        },
        "en-150": {
            "name": "en-150",
            "date_order": "DMY"
        },
        "en-AG": {
            "name": "en-AG",
            "date_order": "DMY"
        },
        "en-AI": {
            "name": "en-AI",
            "date_order": "DMY"
        },
        "en-AS": {
            "name": "en-AS"
        },
        "en-AT": {
            "name": "en-AT",
            "date_order": "DMY"
        },
        "en-AU": {
            "name": "en-AU",
            "date_order": "DMY",
            "hour": [
                "h"
            ]
        },
        "en-BB": {
            "name": "en-BB",
            "date_order": "DMY"
        },
        "en-BE": {
            "name": "en-BE",
            "date_order": "DMY"
        },
        "en-BI": {
            "name": "en-BI"
        },
        "en-BM": {
            "name": "en-BM",
            "date_order": "DMY"
        },
        "en-BS": {
            "name": "en-BS",
            "date_order": "DMY"
        },
        "en-BW": {
            "name": "en-BW",
            "date_order": "DMY"
        },
        "en-BZ": {
            "name": "en-BZ",
            "date_order": "DMY"
        },
        "en-CA": {
            "name": "en-CA",
            "date_order": "YMD",
            "relative-type-regex": {
                "\\1 hour ago": [
                    "(\\d+[.,]?\\d*) hrs ago"
                ],
                "\\1 minute ago": [
                    "(\\d+[.,]?\\d*) mins ago"
                ],
                "\\1 month ago": [
                    "(\\d+[.,]?\\d*) mos ago"
                ],
                "\\1 second ago": [
                    "(\\d+[.,]?\\d*) secs ago"
                ],
                "\\1 week ago": [
                    "(\\d+[.,]?\\d*) wks ago"
                ],
                "\\1 year ago": [
                    "(\\d+[.,]?\\d*) yrs ago"
                ],
                "in \\1 hour": [
                    "in (\\d+[.,]?\\d*) hrs"
                ],
                "in \\1 minute": [
                    "in (\\d+[.,]?\\d*) mins"
                ],
                "in \\1 month": [
                    "in (\\d+[.,]?\\d*) mos"
                ],
                "in \\1 second": [
                    "in (\\d+[.,]?\\d*) secs"
                ],
                "in \\1 week": [
                    "in (\\d+[.,]?\\d*) wks"
                ],
                "in \\1 year": [
                    "in (\\d+[.,]?\\d*) yrs"
                ]
            }
        },
        "en-CC": {
            "name": "en-CC",
            "date_order": "DMY"
        },
        "en-CH": {
            "name": "en-CH",
            "date_order": "DMY"
        },
        "en-CK": {
            "name": "en-CK",
            "date_order": "DMY"
        },
        "en-CM": {
            "name": "en-CM",
            "date_order": "DMY"
        },
        "en-CX": {
            "name": "en-CX",
            "date_order": "DMY"
        },
        "en-CY": {
            "name": "en-CY",
            "date_order": "DMY"
        },
        "en-DE": {
            "name": "en-DE",
            "date_order": "DMY"
        },
        "en-DG": {
            "name": "en-DG",
            "date_order": "DMY"
        },
        "en-DK": {
            "name": "en-DK",
            "date_order": "DMY"
        },
        "en-DM": {
            "name": "en-DM",
            "date_order": "DMY"
        },
        "en-ER": {
            "name": "en-ER",
            "date_order": "DMY"
        },
        "en-FI": {
            "name": "en-FI",
            "date_order": "DMY"
        },
        "en-FJ": {
            "name": "en-FJ",
            "date_order": "DMY"
        },
        "en-FK": {
            "name": "en-FK",
            "date_order": "DMY"
        },
        "en-FM": {
            "name": "en-FM",
            "date_order": "DMY"
        },
        "en-GB": {
            "name": "en-GB",
            "date_order": "DMY"
        },
        "en-GD": {
            "name": "en-GD",
            "date_order": "DMY"
        },
        "en-GG": {
            "name": "en-GG",
            "date_order": "DMY"
        },
        "en-GH": {
            "name": "en-GH",
            "date_order": "DMY"
        },
        "en-GI": {
            "name": "en-GI",
            "date_order": "DMY"
        },
        "en-GM": {
            "name": "en-GM",
            "date_order": "DMY"
        },
        "en-GU": {
            "name": "en-GU"
        },
        "en-GY": {
            "name": "en-GY",
            "date_order": "DMY"
        },
        "en-HK": {
            "name": "en-HK",
            "date_order": "DMY"
        },
        "en-IE": {
            "name": "en-IE",
            "date_order": "DMY"
        },
        "en-IL": {
            "name": "en-IL",
            "date_order": "DMY"
        },
        "en-IM": {
            "name": "en-IM",
            "date_order": "DMY"
        },
        "en-IN": {
            "name": "en-IN",
            "date_order": "DMY"
        },
        "en-IO": {
            "name": "en-IO",
            "date_order": "DMY"
        },
        "en-JE": {
            "name": "en-JE",
            "date_order": "DMY"
        },
        "en-JM": {
            "name": "en-JM",
            "date_order": "DMY"
        },
        "en-KE": {
            "name": "en-KE",
            "date_order": "DMY"
        },
        "en-KI": {
            "name": "en-KI",
            "date_order": "DMY"
        },
        "en-KN": {
            "name": "en-KN",
            "date_order": "DMY"
        },
        "en-KY": {
            "name": "en-KY",
            "date_order": "DMY"
        },
        "en-LC": {
            "name": "en-LC",
            "date_order": "DMY"
        },
        "en-LR": {
            "name": "en-LR",
            "date_order": "DMY"
        },
        "en-LS": {
            "name": "en-LS",
            "date_order": "DMY"
        },
        "en-MG": {
            "name": "en-MG",
            "date_order": "DMY"
        },
        "en-MH": {
            "name": "en-MH"
        },
        "en-MO": {
            "name": "en-MO",
            "date_order": "DMY"
        },
        "en-MP": {
            "name": "en-MP"
        },
        "en-MS": {
            "name": "en-MS",
            "date_order": "DMY"
        },
        "en-MT": {
            "name": "en-MT",
            "date_order": "DMY"
        },
        "en-MU": {
            "name": "en-MU",
            "date_order": "DMY"
        },
        "en-MW": {
            "name": "en-MW",
            "date_order": "DMY"
        },
        "en-MY": {
            "name": "en-MY",
            "date_order": "DMY"
        },
        "en-NA": {
            "name": "en-NA",
            "date_order": "DMY"
        },
        "en-NF": {
            "name": "en-NF",
            "date_order": "DMY"
        },
        "en-NG": {
            "name": "en-NG",
            "date_order": "DMY"
        },
        "en-NL": {
            "name": "en-NL",
            "date_order": "DMY"
        },
        "en-NR": {
            "name": "en-NR",
            "date_order": "DMY"
        },
        "en-NU": {
            "name": "en-NU",
            "date_order": "DMY"
        },
        "en-NZ": {
            "name": "en-NZ",
            "date_order": "DMY"
        },
        "en-PG": {
            "name": "en-PG",
            "date_order": "DMY"
        },
        "en-PH": {
            "name": "en-PH",
            "date_order": "DMY"
        },
        "en-PK": {
            "name": "en-PK",
            "date_order": "DMY"
        },
        "en-PN": {
            "name": "en-PN",
            "date_order": "DMY"
        },
        "en-PR": {
            "name": "en-PR"
        },
        "en-PW": {
            "name": "en-PW",
            "date_order": "DMY"
        },
        "en-RW": {
            "name": "en-RW",
            "date_order": "DMY"
        },
        "en-SB": {
            "name": "en-SB",
            "date_order": "DMY"
        },
        "en-SC": {
            "name": "en-SC",
            "date_order": "DMY"
        },
        "en-SD": {
            "name": "en-SD",
            "date_order": "DMY"
        },
        "en-SE": {
            "name": "en-SE",
            "date_order": "YMD"
        },
        "en-SG": {
            "name": "en-SG",
            "date_order": "DMY",
            "month": [
                "mth"
            ],
            "relative-type": {
                "0 month ago": [
                    "this mth"
                ],
                "1 month ago": [
                    "last mth"
                ],
                "in 1 month": [
                    "next mth"
                ]
            },
            "relative-type-regex": {
                "\\1 month ago": [
                    "(\\d+[.,]?\\d*) mth ago"
                ],
                "in \\1 month": [
                    "in (\\d+[.,]?\\d*) mth"
                ]
            }
        },
        "en-SH": {
            "name": "en-SH",
            "date_order": "DMY"
        },
        "en-SI": {
            "name": "en-SI",
            "date_order": "DMY"
        },
        "en-SL": {
            "name": "en-SL",
            "date_order": "DMY"
        },
        "en-SS": {
            "name": "en-SS",
            "date_order": "DMY"
        },
        "en-SX": {
            "name": "en-SX",
            "date_order": "DMY"
        },
        "en-SZ": {
            "name": "en-SZ",
            "date_order": "DMY"
        },
        "en-TC": {
            "name": "en-TC",
            "date_order": "DMY"
        },
        "en-TK": {
            "name": "en-TK",
            "date_order": "DMY"
        },
        "en-TO": {
            "name": "en-TO",
            "date_order": "DMY"
        },
        "en-TT": {
            "name": "en-TT",
            "date_order": "DMY"
        },
        "en-TV": {
            "name": "en-TV",
            "date_order": "DMY"
        },
        "en-TZ": {
            "name": "en-TZ",
            "date_order": "DMY"
        },
        "en-UG": {
            "name": "en-UG",
            "date_order": "DMY"
        },
        "en-UM": {
            "name": "en-UM"
        },
        "en-VC": {
            "name": "en-VC",
            "date_order": "DMY"
        },
        "en-VG": {
            "name": "en-VG",
            "date_order": "DMY"
        },
        "en-VI": {
            "name": "en-VI"
        },
        "en-VU": {
            "name": "en-VU",
            "date_order": "DMY"
        },
        "en-WS": {
            "name": "en-WS",
            "date_order": "DMY"
        },
        "en-ZA": {
            "name": "en-ZA",
            "date_order": "YMD"
        },
        "en-ZM": {
            "name": "en-ZM",
            "date_order": "DMY"
        },
        "en-ZW": {
            "name": "en-ZW",
            "date_order": "DMY"
        }
    },
    "skip": [
        "about",
        "ad",
        "and",
        "at",
        "by",
        "just",
        "m",
        "nd",
        "of",
        "on",
        "rd",
        "st",
        "th",
        "the",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "pertain": [
        "of"
    ],
    "sentence_splitter_group": 1,
    "decade": [
        "decade",
        "decades"
    ],
    "ago": [
        "ago",
        "before"
    ],
    "in": [
        "in",
        "from now",
        "after"
    ],
    "simplifications": [
        {
            "an": "1"
        },
        {
            "a": "1"
        },
        {
            "(?:12\\s+)?noon": "12:00"
        },
        {
            "(?:12\\s+)?midnight": "00:00"
        },
        {
            "(\\d+[.,]?\\d*)h(\\d+[.,]?\\d*)": "\\1:\\2"
        },
        {
            "(?<=from\\s+)now": "in"
        },
        {
            "less than 1 minute ago": "45 second ago"
        },
        {
            "(\\d+[.,]?\\d*) (decade|year|month|week|day|hour|minute|second)s? later": "in \\1 \\2"
        },
        {
            "one": "1"
        },
        {
            "two": "2"
        },
        {
            "three": "3"
        },
        {
            "four": "4"
        },
        {
            "five": "5"
        },
        {
            "six": "6"
        },
        {
            "seven": "7"
        },
        {
            "eight": "8"
        },
        {
            "nine": "9"
        },
        {
            "ten": "10"
        },
        {
            "eleven": "11"
        },
        {
            "twelve": "12"
        }
    ]
}
