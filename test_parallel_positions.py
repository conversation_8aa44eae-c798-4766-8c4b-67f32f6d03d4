import os
import sys
import time
import json
import logging
import datetime
import random

# Import the SimpleAITradingBot class
from simple_ai_bot import SimpleAITradingBot

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("test_parallel_positions")

def test_parallel_positions():
    """
    Test the Parallel Position Management feature
    """
    # Create a bot instance
    bot = SimpleAITradingBot(symbol="BTC/USDT", test_mode=True)

    # Override the bot's methods to simulate multiple positions
    def mock_execute_trade(self, signal):
        """
        Mock execute_trade method to create multiple positions
        """
        # Check if we already have the maximum number of positions
        if len(self.open_positions) >= self.max_positions:
            logger.info(f"Maximum number of positions reached ({self.max_positions}). No trade executed.")
            return None

        # Check if we already have a position for this symbol
        if self.symbol in self.position_symbols:
            logger.info(f"Already have a position for {self.symbol}. No trade executed.")
            return None

        # Create a position
        price = self.check_price()

        # Create a position
        position = {
            "symbol": self.symbol,
            "side": "buy",
            "entry_price": price,
            "quantity": 0.001,
            "timestamp": datetime.datetime.now().isoformat(),
            "order_id": f"test_{random.randint(1000, 9999)}",
            "signal": signal,
            "initial_stop_loss_price": price * 0.98,
            "stop_loss_price": price * 0.98,
            "stop_loss_pct": 0.02,
            "risk_value": 0.02 * price * 0.001,
            "tp1_price": price * 1.01,
            "tp2_price": price * 1.02,
            "tp3_price": price * 1.03,
            "breakeven_price": price,
            "trailing_stop_price": price * 0.98,
            "trailing_stop_activated": False,
            "reached_tp1": False,
            "reached_tp2": False,
            "reached_breakeven": False
        }

        # Set current position
        self.current_position = position

        # Add to open positions list
        self.open_positions.append(position)

        # Add symbol to the set of position symbols
        self.position_symbols.add(self.symbol)

        logger.info(f"Created position: {position['side']} {position['quantity']} {position['symbol']} at ${position['entry_price']:.2f}")
        logger.info(f"Total open positions: {len(self.open_positions)}/{self.max_positions}")

        return position

    # Override the execute_trade method
    bot.execute_trade = lambda signal: mock_execute_trade(bot, signal)

    # Create multiple positions with different symbols
    symbols = ["BTC/USDT", "ETH/USDT", "XRP/USDT", "LTC/USDT"]

    for i, symbol in enumerate(symbols):
        if i >= bot.max_positions:
            logger.info(f"Trying to create position {i+1} (max: {bot.max_positions})")

        # Set the bot's symbol
        bot.symbol = symbol

        # Create a mock signal
        signal = {
            "recommendation": "buy",
            "confidence": 0.85,
            "price": bot.check_price(),
            "timestamp": datetime.datetime.now().isoformat()
        }

        # Execute trade
        position = bot.execute_trade(signal)

        if position:
            logger.info(f"Position created for {symbol}")
        else:
            logger.info(f"Position not created for {symbol}")

        # Wait a bit
        time.sleep(1)

    # Print summary
    logger.info(f"Total positions: {len(bot.open_positions)}")
    logger.info(f"Position symbols: {bot.position_symbols}")

    # Check if the maximum number of positions is enforced
    assert len(bot.open_positions) <= bot.max_positions, f"Too many positions: {len(bot.open_positions)} > {bot.max_positions}"

    logger.info("Test passed!")

if __name__ == "__main__":
    test_parallel_positions()
