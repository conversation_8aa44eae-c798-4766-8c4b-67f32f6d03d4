"""
News Connector Module for Google News and Google Trends
Handles fetching and analyzing news and trends data
"""

import os
import json
import time
import logging
import requests
from datetime import datetime, timedelta
from pytrends.request import TrendReq
from security.key_manager import KeyManager

# Set up logging
logger = logging.getLogger("news_connector")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/news_analysis.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class GoogleNewsConnector:
    """
    Connector for Google News API
    """
    
    def __init__(self, key_manager=None):
        """
        Initialize the Google News connector
        
        Args:
            key_manager (KeyManager, optional): Key manager instance for API keys
        """
        self.key_manager = key_manager or KeyManager()
        self.service_name = "google_news"
        self.api_url = "https://newsapi.org/v2/everything"
        self.retry_attempts = 3
        self.retry_delay = 2  # seconds
        self.circuit_breaker_threshold = 3  # consecutive failures
        self.circuit_breaker_timeout = 300  # seconds
        self.circuit_breaker = {
            "failures": 0,
            "last_failure": None,
            "open": False
        }
    
    def _check_circuit_breaker(self):
        """
        Check if circuit breaker is open
        
        Returns:
            bool: True if circuit breaker is open, False otherwise
        """
        if not self.circuit_breaker["open"]:
            return False
        
        # Check if circuit breaker timeout has passed
        if self.circuit_breaker["last_failure"] is not None:
            elapsed = (datetime.now() - self.circuit_breaker["last_failure"]).total_seconds()
            if elapsed > self.circuit_breaker_timeout:
                # Reset circuit breaker
                self.circuit_breaker["open"] = False
                self.circuit_breaker["failures"] = 0
                logger.info("Circuit breaker reset after timeout")
                return False
        
        return True
    
    def _handle_failure(self, error):
        """
        Handle API call failure and update circuit breaker
        
        Args:
            error: The error that occurred
        """
        self.circuit_breaker["failures"] += 1
        self.circuit_breaker["last_failure"] = datetime.now()
        
        if self.circuit_breaker["failures"] >= self.circuit_breaker_threshold:
            self.circuit_breaker["open"] = True
            logger.warning(f"Circuit breaker opened after {self.circuit_breaker_threshold} consecutive failures")
        
        logger.error(f"API call failed: {error}")
    
    def _handle_success(self):
        """
        Handle API call success and reset circuit breaker
        """
        self.circuit_breaker["failures"] = 0
        self.circuit_breaker["open"] = False
    
    def get_bitcoin_news(self, hours=24):
        """
        Get recent Bitcoin news
        
        Args:
            hours (int): Number of hours to look back
            
        Returns:
            dict: News data and sentiment analysis
        """
        if self._check_circuit_breaker():
            logger.warning("Circuit breaker is open. Skipping Google News fetch.")
            return {"error": "Circuit breaker open", "service": self.service_name}
        
        # Get API key using round-robin
        api_key_data = self.key_manager.get_next_key(self.service_name)
        if not api_key_data:
            logger.error("No Google News API key available")
            return {"error": "No API key available", "service": self.service_name}
        
        api_key = api_key_data["key"]
        
        # Calculate date range
        to_date = datetime.now()
        from_date = to_date - timedelta(hours=hours)
        
        # Format dates for API
        from_date_str = from_date.strftime("%Y-%m-%d")
        to_date_str = to_date.strftime("%Y-%m-%d")
        
        # Prepare request parameters
        params = {
            "q": "bitcoin OR cryptocurrency",
            "from": from_date_str,
            "to": to_date_str,
            "language": "en",
            "sortBy": "publishedAt",
            "apiKey": api_key
        }
        
        # Make API call with retry logic
        for attempt in range(self.retry_attempts):
            try:
                response = requests.get(self.api_url, params=params, timeout=30)
                response.raise_for_status()
                
                # Parse response
                news_data = response.json()
                
                # Process news data
                processed_news = self._process_news_data(news_data)
                self._handle_success()
                
                return {
                    "service": self.service_name,
                    "news": processed_news,
                    "timestamp": datetime.now().isoformat()
                }
            
            except requests.exceptions.RequestException as e:
                self._handle_failure(e)
                if attempt < self.retry_attempts - 1:
                    wait_time = self.retry_delay * (2 ** attempt)
                    logger.warning(f"Retrying Google News API call in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    return {"error": str(e), "service": self.service_name}
        
        return {"error": "Max retry attempts reached", "service": self.service_name}
    
    def _process_news_data(self, news_data):
        """
        Process news data and extract relevant information
        
        Args:
            news_data (dict): Raw news data from API
            
        Returns:
            dict: Processed news data with sentiment analysis
        """
        try:
            articles = news_data.get("articles", [])
            
            # Filter articles related to Bitcoin
            bitcoin_articles = []
            for article in articles:
                title = article.get("title", "").lower()
                description = article.get("description", "").lower()
                
                if "bitcoin" in title or "btc" in title or "bitcoin" in description or "btc" in description:
                    bitcoin_articles.append(article)
            
            # Limit to top 10 articles
            bitcoin_articles = bitcoin_articles[:10]
            
            # Extract relevant information
            processed_articles = []
            for article in bitcoin_articles:
                processed_article = {
                    "title": article.get("title", ""),
                    "source": article.get("source", {}).get("name", "Unknown"),
                    "published_at": article.get("publishedAt", ""),
                    "url": article.get("url", ""),
                    "sentiment": self._analyze_sentiment(article.get("title", "") + " " + article.get("description", ""))
                }
                processed_articles.append(processed_article)
            
            # Calculate overall sentiment
            if processed_articles:
                sentiment_scores = [article["sentiment"]["score"] for article in processed_articles]
                avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
                
                if avg_sentiment > 0.2:
                    overall_sentiment = "bullish"
                elif avg_sentiment < -0.2:
                    overall_sentiment = "bearish"
                else:
                    overall_sentiment = "neutral"
            else:
                overall_sentiment = "neutral"
            
            return {
                "articles": processed_articles,
                "overall_sentiment": overall_sentiment,
                "article_count": len(processed_articles)
            }
        
        except Exception as e:
            logger.error(f"Error processing news data: {e}")
            return {
                "articles": [],
                "overall_sentiment": "neutral",
                "article_count": 0,
                "error": str(e)
            }
    
    def _analyze_sentiment(self, text):
        """
        Simple sentiment analysis for news text
        
        Args:
            text (str): Text to analyze
            
        Returns:
            dict: Sentiment analysis results
        """
        # This is a very simple sentiment analysis
        # In a real implementation, you would use a more sophisticated approach
        
        positive_words = ["bullish", "surge", "rally", "gain", "positive", "up", "rise", "soar", "growth", "profit"]
        negative_words = ["bearish", "crash", "fall", "drop", "negative", "down", "decline", "plunge", "loss", "risk"]
        
        text = text.lower()
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        score = (positive_count - negative_count) / (positive_count + negative_count + 1)  # +1 to avoid division by zero
        
        if score > 0.2:
            sentiment = "bullish"
        elif score < -0.2:
            sentiment = "bearish"
        else:
            sentiment = "neutral"
        
        return {
            "score": score,
            "sentiment": sentiment,
            "positive_count": positive_count,
            "negative_count": negative_count
        }

class GoogleTrendsConnector:
    """
    Connector for Google Trends API using pytrends
    """
    
    def __init__(self):
        """
        Initialize the Google Trends connector
        """
        self.service_name = "google_trends"
        self.pytrends = TrendReq(hl='en-US', tz=360)
        self.retry_attempts = 3
        self.retry_delay = 2  # seconds
        self.circuit_breaker_threshold = 3  # consecutive failures
        self.circuit_breaker_timeout = 300  # seconds
        self.circuit_breaker = {
            "failures": 0,
            "last_failure": None,
            "open": False
        }
    
    def _check_circuit_breaker(self):
        """
        Check if circuit breaker is open
        
        Returns:
            bool: True if circuit breaker is open, False otherwise
        """
        if not self.circuit_breaker["open"]:
            return False
        
        # Check if circuit breaker timeout has passed
        if self.circuit_breaker["last_failure"] is not None:
            elapsed = (datetime.now() - self.circuit_breaker["last_failure"]).total_seconds()
            if elapsed > self.circuit_breaker_timeout:
                # Reset circuit breaker
                self.circuit_breaker["open"] = False
                self.circuit_breaker["failures"] = 0
                logger.info("Circuit breaker reset after timeout")
                return False
        
        return True
    
    def _handle_failure(self, error):
        """
        Handle API call failure and update circuit breaker
        
        Args:
            error: The error that occurred
        """
        self.circuit_breaker["failures"] += 1
        self.circuit_breaker["last_failure"] = datetime.now()
        
        if self.circuit_breaker["failures"] >= self.circuit_breaker_threshold:
            self.circuit_breaker["open"] = True
            logger.warning(f"Circuit breaker opened after {self.circuit_breaker_threshold} consecutive failures")
        
        logger.error(f"API call failed: {error}")
    
    def _handle_success(self):
        """
        Handle API call success and reset circuit breaker
        """
        self.circuit_breaker["failures"] = 0
        self.circuit_breaker["open"] = False
    
    def get_bitcoin_interest(self, timeframe='now 7-d'):
        """
        Get Bitcoin interest from Google Trends
        
        Args:
            timeframe (str): Time frame for trends data
            
        Returns:
            dict: Trends data and analysis
        """
        if self._check_circuit_breaker():
            logger.warning("Circuit breaker is open. Skipping Google Trends fetch.")
            return {"error": "Circuit breaker open", "service": self.service_name}
        
        # Make API call with retry logic
        for attempt in range(self.retry_attempts):
            try:
                # Build payload
                self.pytrends.build_payload(kw_list=['Bitcoin', 'BTC', 'cryptocurrency'], timeframe=timeframe)
                
                # Get interest over time
                interest_over_time = self.pytrends.interest_over_time()
                
                # Get related queries
                related_queries = self.pytrends.related_queries()
                
                # Process trends data
                processed_trends = self._process_trends_data(interest_over_time, related_queries)
                self._handle_success()
                
                return {
                    "service": self.service_name,
                    "trends": processed_trends,
                    "timestamp": datetime.now().isoformat()
                }
            
            except Exception as e:
                self._handle_failure(e)
                if attempt < self.retry_attempts - 1:
                    wait_time = self.retry_delay * (2 ** attempt)
                    logger.warning(f"Retrying Google Trends API call in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    return {"error": str(e), "service": self.service_name}
        
        return {"error": "Max retry attempts reached", "service": self.service_name}
    
    def _process_trends_data(self, interest_over_time, related_queries):
        """
        Process trends data and extract relevant information
        
        Args:
            interest_over_time (DataFrame): Interest over time data
            related_queries (dict): Related queries data
            
        Returns:
            dict: Processed trends data with analysis
        """
        try:
            # Convert interest_over_time to dict
            interest_dict = {}
            if not interest_over_time.empty:
                for index, row in interest_over_time.iterrows():
                    date_str = index.strftime("%Y-%m-%d")
                    interest_dict[date_str] = {
                        "Bitcoin": int(row.get("Bitcoin", 0)),
                        "BTC": int(row.get("BTC", 0)),
                        "cryptocurrency": int(row.get("cryptocurrency", 0))
                    }
            
            # Extract top related queries
            top_queries = {}
            for keyword in ['Bitcoin', 'BTC', 'cryptocurrency']:
                if keyword in related_queries and related_queries[keyword]['top'] is not None:
                    top_queries[keyword] = related_queries[keyword]['top'].head(5).to_dict('records')
                else:
                    top_queries[keyword] = []
            
            # Calculate trend direction
            trend_direction = self._calculate_trend_direction(interest_dict)
            
            return {
                "interest_over_time": interest_dict,
                "top_related_queries": top_queries,
                "trend_direction": trend_direction
            }
        
        except Exception as e:
            logger.error(f"Error processing trends data: {e}")
            return {
                "interest_over_time": {},
                "top_related_queries": {},
                "trend_direction": "neutral",
                "error": str(e)
            }
    
    def _calculate_trend_direction(self, interest_dict):
        """
        Calculate trend direction based on interest data
        
        Args:
            interest_dict (dict): Interest over time data
            
        Returns:
            str: Trend direction (bullish, bearish, or neutral)
        """
        try:
            if not interest_dict:
                return "neutral"
            
            # Get dates in chronological order
            dates = sorted(interest_dict.keys())
            
            if len(dates) < 2:
                return "neutral"
            
            # Calculate average interest for first and second half of the period
            half_point = len(dates) // 2
            first_half = dates[:half_point]
            second_half = dates[half_point:]
            
            first_half_avg = sum(interest_dict[date]["Bitcoin"] for date in first_half) / len(first_half)
            second_half_avg = sum(interest_dict[date]["Bitcoin"] for date in second_half) / len(second_half)
            
            # Calculate percent change
            percent_change = ((second_half_avg - first_half_avg) / first_half_avg) * 100 if first_half_avg > 0 else 0
            
            # Determine trend direction
            if percent_change > 10:
                return "bullish"
            elif percent_change < -10:
                return "bearish"
            else:
                return "neutral"
        
        except Exception as e:
            logger.error(f"Error calculating trend direction: {e}")
            return "neutral"
