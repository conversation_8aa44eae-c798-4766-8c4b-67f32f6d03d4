# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.base.exchange import Exchange
from ccxt.abstract.ndax import ImplicitAPI
import hashlib
import json
from ccxt.base.types import Account, Any, Balances, Currencies, Currency, DepositAddress, IndexType, Int, LedgerEntry, Market, Num, Order, OrderBook, OrderSide, OrderType, Str, Ticker, Trade, Transaction
from typing import List
from ccxt.base.errors import ExchangeError
from ccxt.base.errors import AuthenticationError
from ccxt.base.errors import BadSymbol
from ccxt.base.errors import InsufficientFunds
from ccxt.base.errors import OrderNotFound
from ccxt.base.decimal_to_precision import TICK_SIZE
from ccxt.base.precise import Precise


class ndax(Exchange, ImplicitAPI):

    def describe(self) -> Any:
        return self.deep_extend(super(ndax, self).describe(), {
            'id': 'ndax',
            'name': 'NDAX',
            'countries': ['CA'],  # Canada
            'rateLimit': 1000,
            'pro': True,
            'has': {
                'CORS': None,
                'spot': True,
                'margin': False,
                'swap': False,
                'future': False,
                'option': False,
                'addMargin': False,
                'cancelAllOrders': True,
                'cancelOrder': True,
                'closeAllPositions': False,
                'closePosition': False,
                'createDepositAddress': True,
                'createOrder': True,
                'createReduceOnlyOrder': False,
                'createStopLimitOrder': True,
                'createStopMarketOrder': True,
                'createStopOrder': True,
                'editOrder': True,
                'fetchAccounts': True,
                'fetchBalance': True,
                'fetchBorrowInterest': False,
                'fetchBorrowRate': False,
                'fetchBorrowRateHistories': False,
                'fetchBorrowRateHistory': False,
                'fetchBorrowRates': False,
                'fetchBorrowRatesPerSymbol': False,
                'fetchCrossBorrowRate': False,
                'fetchCrossBorrowRates': False,
                'fetchCurrencies': True,
                'fetchDepositAddress': True,
                'fetchDepositAddresses': False,
                'fetchDepositAddressesByNetwork': False,
                'fetchDeposits': True,
                'fetchFundingHistory': False,
                'fetchFundingInterval': False,
                'fetchFundingIntervals': False,
                'fetchFundingRate': False,
                'fetchFundingRateHistory': False,
                'fetchFundingRates': False,
                'fetchGreeks': False,
                'fetchIndexOHLCV': False,
                'fetchIsolatedBorrowRate': False,
                'fetchIsolatedBorrowRates': False,
                'fetchIsolatedPositions': False,
                'fetchLedger': True,
                'fetchLeverage': False,
                'fetchLeverages': False,
                'fetchLeverageTiers': False,
                'fetchLiquidations': False,
                'fetchMarginAdjustmentHistory': False,
                'fetchMarginMode': False,
                'fetchMarginModes': False,
                'fetchMarketLeverageTiers': False,
                'fetchMarkets': True,
                'fetchMarkOHLCV': False,
                'fetchMarkPrices': False,
                'fetchMyLiquidations': False,
                'fetchMySettlementHistory': False,
                'fetchMyTrades': True,
                'fetchOHLCV': True,
                'fetchOpenInterest': False,
                'fetchOpenInterestHistory': False,
                'fetchOpenOrders': True,
                'fetchOption': False,
                'fetchOptionChain': False,
                'fetchOrder': True,
                'fetchOrderBook': True,
                'fetchOrders': True,
                'fetchOrderTrades': True,
                'fetchPosition': False,
                'fetchPositionHistory': False,
                'fetchPositionMode': False,
                'fetchPositions': False,
                'fetchPositionsForSymbol': False,
                'fetchPositionsHistory': False,
                'fetchPositionsRisk': False,
                'fetchPremiumIndexOHLCV': False,
                'fetchSettlementHistory': False,
                'fetchTicker': True,
                'fetchTickers': False,
                'fetchTime': False,
                'fetchTrades': True,
                'fetchTradingFee': False,
                'fetchTradingFees': False,
                'fetchUnderlyingAssets': False,
                'fetchVolatilityHistory': False,
                'fetchWithdrawals': True,
                'reduceMargin': False,
                'repayCrossMargin': False,
                'repayIsolatedMargin': False,
                'sandbox': True,
                'setLeverage': False,
                'setMargin': False,
                'setMarginMode': False,
                'setPositionMode': False,
                'signIn': True,
                'transfer': False,
                'withdraw': True,
            },
            'timeframes': {
                '1m': '60',
                '5m': '300',
                '15m': '900',
                '30m': '1800',
                '1h': '3600',
                '2h': '7200',
                '4h': '14400',
                '6h': '21600',
                '12h': '43200',
                '1d': '86400',
                '1w': '604800',
                '1M': '2419200',
                '4M': '9676800',
            },
            'urls': {
                'logo': 'https://user-images.githubusercontent.com/1294454/108623144-67a3ef00-744e-11eb-8140-75c6b851e945.jpg',
                'test': {
                    'public': 'https://ndaxmarginstaging.cdnhop.net:8443/AP',
                    'private': 'https://ndaxmarginstaging.cdnhop.net:8443/AP',
                },
                'api': {
                    'public': 'https://api.ndax.io:8443/AP',
                    'private': 'https://api.ndax.io:8443/AP',
                },
                'www': 'https://ndax.io',
                'doc': [
                    'https://apidoc.ndax.io/',
                ],
                'fees': 'https://ndax.io/fees',
                'referral': 'https://one.ndax.io/bfQiSL',
            },
            'api': {
                'public': {
                    'get': {
                        'Activate2FA': 1,
                        'Authenticate2FA': 1,
                        'AuthenticateUser': 1,
                        'GetL2Snapshot': 1,
                        'GetLevel1': 1,
                        'GetValidate2FARequiredEndpoints': 1,
                        'LogOut': 1,
                        'GetTickerHistory': 1,
                        'GetProduct': 1,
                        'GetProducts': 1,
                        'GetInstrument': 1,
                        'GetInstruments': 1,
                        'Ping': 1,
                        'trades': 1,  # undocumented
                        'GetLastTrades': 1,  # undocumented
                        'SubscribeLevel1': 1,
                        'SubscribeLevel2': 1,
                        'SubscribeTicker': 1,
                        'SubscribeTrades': 1,
                        'SubscribeBlockTrades': 1,
                        'UnsubscribeBlockTrades': 1,
                        'UnsubscribeLevel1': 1,
                        'UnsubscribeLevel2': 1,
                        'UnsubscribeTicker': 1,
                        'UnsubscribeTrades': 1,
                        'Authenticate': 1,  # undocumented
                    },
                },
                'private': {
                    'get': {
                        'GetUserAccountInfos': 1,
                        'GetUserAccounts': 1,
                        'GetUserAffiliateCount': 1,
                        'GetUserAffiliateTag': 1,
                        'GetUserConfig': 1,
                        'GetAllUnredactedUserConfigsForUser': 1,
                        'GetUnredactedUserConfigByKey': 1,
                        'GetUserDevices': 1,
                        'GetUserReportTickets': 1,
                        'GetUserReportWriterResultRecords': 1,
                        'GetAccountInfo': 1,
                        'GetAccountPositions': 1,
                        'GetAllAccountConfigs': 1,
                        'GetTreasuryProductsForAccount': 1,
                        'GetAccountTrades': 1,
                        'GetAccountTransactions': 1,
                        'GetOpenTradeReports': 1,
                        'GetAllOpenTradeReports': 1,
                        'GetTradesHistory': 1,
                        'GetOpenOrders': 1,
                        'GetOpenQuotes': 1,
                        'GetOrderFee': 1,
                        'GetOrderHistory': 1,
                        'GetOrdersHistory': 1,
                        'GetOrderStatus': 1,
                        'GetOmsFeeTiers': 1,
                        'GetAccountDepositTransactions': 1,
                        'GetAccountWithdrawTransactions': 1,
                        'GetAllDepositRequestInfoTemplates': 1,
                        'GetDepositInfo': 1,
                        'GetDepositRequestInfoTemplate': 1,
                        'GetDeposits': 1,
                        'GetDepositTicket': 1,
                        'GetDepositTickets': 1,
                        'GetOMSWithdrawFees': 1,
                        'GetWithdrawFee': 1,
                        'GetWithdraws': 1,
                        'GetWithdrawTemplate': 1,
                        'GetWithdrawTemplateTypes': 1,
                        'GetWithdrawTicket': 1,
                        'GetWithdrawTickets': 1,
                    },
                    'post': {
                        'AddUserAffiliateTag': 1,
                        'CancelUserReport': 1,
                        'RegisterNewDevice': 1,
                        'SubscribeAccountEvents': 1,
                        'UpdateUserAffiliateTag': 1,
                        'GenerateTradeActivityReport': 1,
                        'GenerateTransactionActivityReport': 1,
                        'GenerateTreasuryActivityReport': 1,
                        'ScheduleTradeActivityReport': 1,
                        'ScheduleTransactionActivityReport': 1,
                        'ScheduleTreasuryActivityReport': 1,
                        'CancelAllOrders': 1,
                        'CancelOrder': 1,
                        'CancelQuote': 1,
                        'CancelReplaceOrder': 1,
                        'CreateQuote': 1,
                        'ModifyOrder': 1,
                        'SendOrder': 1,
                        'SubmitBlockTrade': 1,
                        'UpdateQuote': 1,
                        'CancelWithdraw': 1,
                        'CreateDepositTicket': 1,
                        'CreateWithdrawTicket': 1,
                        'SubmitDepositTicketComment': 1,
                        'SubmitWithdrawTicketComment': 1,
                        'GetOrderHistoryByOrderId': 1,
                    },
                },
            },
            'features': {
                'spot': {
                    'sandbox': True,
                    'createOrder': {
                        'marginMode': False,
                        'triggerPrice': True,
                        'triggerDirection': False,
                        'triggerPriceType': {
                            'last': True,
                            'mark': False,
                            'index': False,
                            # bid & ask
                        },
                        'stopLossPrice': False,  # todo
                        'takeProfitPrice': False,  # todo
                        'attachedStopLossTakeProfit': None,
                        # todo
                        'timeInForce': {
                            'IOC': True,
                            'FOK': True,
                            'PO': True,
                            'GTD': False,
                        },
                        'hedged': False,
                        'trailing': False,
                        'leverage': False,
                        'marketBuyByCost': False,
                        'marketBuyRequiresPrice': False,
                        'selfTradePrevention': False,
                        'iceberg': True,  # todo
                    },
                    'createOrders': None,
                    'fetchMyTrades': {
                        'marginMode': False,
                        'limit': 100,  # todo
                        'daysBack': 100000,  # todo
                        'untilDays': 100000,  # todo
                        'symbolRequired': False,
                    },
                    'fetchOrder': {
                        'marginMode': False,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': False,
                    },
                    'fetchOpenOrders': {
                        'marginMode': False,
                        'limit': None,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': False,
                    },
                    'fetchOrders': {
                        'marginMode': False,
                        'limit': None,
                        'daysBack': None,
                        'untilDays': None,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': False,
                    },
                    'fetchClosedOrders': None,
                    'fetchOHLCV': {
                        'limit': None,
                    },
                },
                'swap': {
                    'linear': None,
                    'inverse': None,
                },
                'future': {
                    'linear': None,
                    'inverse': None,
                },
            },
            'fees': {
                'trading': {
                    'tierBased': False,
                    'percentage': True,
                    'maker': self.parse_number('0.002'),
                    'taker': self.parse_number('0.0025'),
                },
            },
            'requiredCredentials': {
                'apiKey': True,
                'secret': True,
                'uid': True,
                # these credentials are required for signIn() and withdraw()
                'login': True,
                'password': True,
                # 'twofa': True,
            },
            'precisionMode': TICK_SIZE,
            'exceptions': {
                'exact': {
                    'Not_Enough_Funds': InsufficientFunds,  # {"status":"Rejected","errormsg":"Not_Enough_Funds","errorcode":101}
                    'Server Error': ExchangeError,  # {"result":false,"errormsg":"Server Error","errorcode":102,"detail":null}
                    'Resource Not Found': OrderNotFound,  # {"result":false,"errormsg":"Resource Not Found","errorcode":104,"detail":null}
                },
                'broad': {
                    'Invalid InstrumentId': BadSymbol,  # {"result":false,"errormsg":"Invalid InstrumentId: 10000","errorcode":100,"detail":null}
                    'This endpoint requires 2FACode along with the payload': AuthenticationError,
                },
            },
            'options': {
                'omsId': 1,
                'orderTypes': {
                    'Market': 1,
                    'Limit': 2,
                    'StopMarket': 3,
                    'StopLimit': 4,
                    'TrailingStopMarket': 5,
                    'TrailingStopLimit': 6,
                    'BlockTrade': 7,
                    '1': 1,
                    '2': 2,
                    '3': 3,
                    '4': 4,
                    '5': 5,
                    '6': 6,
                    '7': 7,
                },
            },
        })

    def sign_in(self, params={}):
        """
        sign in, must be called prior to using other authenticated methods

        https://apidoc.ndax.io/#authenticate2fa

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns: response from exchange
        """
        self.check_required_credentials()
        if self.login is None or self.password is None:
            raise AuthenticationError(self.id + ' signIn() requires exchange.login, exchange.password')
        request: dict = {
            'grant_type': 'client_credentials',  # the only supported value
        }
        response = self.publicGetAuthenticate(self.extend(request, params))
        #
        #     {
        #         "Authenticated":true,
        #         "Requires2FA":true,
        #         "AuthType":"Google",
        #         "AddtlInfo":"",
        #         "Pending2FaToken": "6f5c4e66-f3ee-493e-9227-31cc0583b55f"
        #     }
        #
        sessionToken = self.safe_string(response, 'SessionToken')
        if sessionToken is not None:
            self.options['sessionToken'] = sessionToken
            return response
        pending2faToken = self.safe_string(response, 'Pending2FaToken')
        if pending2faToken is not None:
            if self.twofa is None:
                raise AuthenticationError(self.id + ' signIn() requires exchange.twofa credentials')
            self.options['pending2faToken'] = pending2faToken
            request = {
                'Code': self.totp(self.twofa),
            }
            responseInner = self.publicGetAuthenticate2FA(self.extend(request, params))
            #
            #     {
            #         "Authenticated": True,
            #         "UserId":57764,
            #         "SessionToken":"4a2a5857-c4e5-4fac-b09e-2c4c30b591a0"
            #     }
            #
            sessionToken = self.safe_string(responseInner, 'SessionToken')
            self.options['sessionToken'] = sessionToken
            return responseInner
        return response

    def fetch_currencies(self, params={}) -> Currencies:
        """
        fetches all available currencies on an exchange

        https://apidoc.ndax.io/#getproduct

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an associative dictionary of currencies
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        request: dict = {
            'omsId': omsId,
        }
        response = self.publicGetGetProducts(self.extend(request, params))
        #
        #     [
        #         {
        #             "OMSId":1,
        #             "ProductId":1,
        #             "Product":"BTC",
        #             "ProductFullName":"Bitcoin",
        #             "ProductType":"CryptoCurrency",
        #             "DecimalPlaces":8,
        #             "TickSize":0.0000000100000000000000000000,
        #             "NoFees":false,
        #             "IsDisabled":false,
        #             "MarginEnabled":false
        #         },
        #     ]
        #
        result: dict = {}
        for i in range(0, len(response)):
            currency = response[i]
            id = self.safe_string(currency, 'ProductId')
            name = self.safe_string(currency, 'ProductFullName')
            ProductType = self.safe_string(currency, 'ProductType')
            type = 'fiat' if (ProductType == 'NationalCurrency') else 'crypto'
            if ProductType == 'Unknown':
                # such currency is just a blanket entry
                type = 'other'
            code = self.safe_currency_code(self.safe_string(currency, 'Product'))
            isDisabled = self.safe_value(currency, 'IsDisabled')
            active = not isDisabled
            result[code] = {
                'id': id,
                'name': name,
                'code': code,
                'type': type,
                'precision': self.safe_number(currency, 'TickSize'),
                'info': currency,
                'active': active,
                'deposit': None,
                'withdraw': None,
                'fee': None,
                'limits': {
                    'amount': {
                        'min': None,
                        'max': None,
                    },
                    'withdraw': {
                        'min': None,
                        'max': None,
                    },
                },
                'networks': {},
            }
        return result

    def fetch_markets(self, params={}) -> List[Market]:
        """
        retrieves data on all markets for ndax

        https://apidoc.ndax.io/#getinstruments

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: an array of objects representing market data
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        request: dict = {
            'omsId': omsId,
        }
        response = self.publicGetGetInstruments(self.extend(request, params))
        #
        #     [
        #         {
        #             "OMSId":1,
        #             "InstrumentId":3,
        #             "Symbol":"LTCBTC",
        #             "Product1":3,
        #             "Product1Symbol":"LTC",
        #             "Product2":1,
        #             "Product2Symbol":"BTC",
        #             "InstrumentType":"Standard",
        #             "VenueInstrumentId":3,
        #             "VenueId":1,
        #             "SortIndex":0,
        #             "SessionStatus":"Running",
        #             "PreviousSessionStatus":"Stopped",
        #             "SessionStatusDateTime":"2020-11-25T19:42:15.245Z",
        #             "SelfTradePrevention":true,
        #             "QuantityIncrement":0.0000000100000000000000000000,
        #             "PriceIncrement":0.0000000100000000000000000000,
        #             "MinimumQuantity":0.0100000000000000000000000000,
        #             "MinimumPrice":0.0000010000000000000000000000,
        #             "VenueSymbol":"LTCBTC",
        #             "IsDisable":false,
        #             "MasterDataId":0,
        #             "PriceCollarThreshold":0.0000000000000000000000000000,
        #             "PriceCollarPercent":0.0000000000000000000000000000,
        #             "PriceCollarEnabled":false,
        #             "PriceFloorLimit":0.0000000000000000000000000000,
        #             "PriceFloorLimitEnabled":false,
        #             "PriceCeilingLimit":0.0000000000000000000000000000,
        #             "PriceCeilingLimitEnabled":false,
        #             "CreateWithMarketRunning":true,
        #             "AllowOnlyMarketMakerCounterParty":false,
        #             "PriceCollarIndexDifference":0.0000000000000000000000000000,
        #             "PriceCollarConvertToOtcEnabled":false,
        #             "PriceCollarConvertToOtcClientUserId":0,
        #             "PriceCollarConvertToOtcAccountId":0,
        #             "PriceCollarConvertToOtcThreshold":0.0000000000000000000000000000,
        #             "OtcConvertSizeThreshold":0.0000000000000000000000000000,
        #             "OtcConvertSizeEnabled":false,
        #             "OtcTradesPublic":true,
        #             "PriceTier":0
        #         },
        #     ]
        #
        return self.parse_markets(response)

    def parse_market(self, market: dict) -> Market:
        id = self.safe_string(market, 'InstrumentId')
        # lowercaseId = self.safe_string_lower(market, 'symbol')
        baseId = self.safe_string(market, 'Product1')
        quoteId = self.safe_string(market, 'Product2')
        base = self.safe_currency_code(self.safe_string(market, 'Product1Symbol'))
        quote = self.safe_currency_code(self.safe_string(market, 'Product2Symbol'))
        sessionStatus = self.safe_string(market, 'SessionStatus')
        isDisable = self.safe_value(market, 'IsDisable')
        sessionRunning = (sessionStatus == 'Running')
        return {
            'id': id,
            'symbol': base + '/' + quote,
            'base': base,
            'quote': quote,
            'settle': None,
            'baseId': baseId,
            'quoteId': quoteId,
            'settleId': None,
            'type': 'spot',
            'spot': True,
            'margin': False,
            'swap': False,
            'future': False,
            'option': False,
            'active': (sessionRunning and not isDisable),
            'contract': False,
            'linear': None,
            'inverse': None,
            'contractSize': None,
            'expiry': None,
            'expiryDatetime': None,
            'strike': None,
            'optionType': None,
            'precision': {
                'amount': self.safe_number(market, 'QuantityIncrement'),
                'price': self.safe_number(market, 'PriceIncrement'),
            },
            'limits': {
                'leverage': {
                    'min': None,
                    'max': None,
                },
                'amount': {
                    'min': self.safe_number(market, 'MinimumQuantity'),
                    'max': None,
                },
                'price': {
                    'min': self.safe_number(market, 'MinimumPrice'),
                    'max': None,
                },
                'cost': {
                    'min': None,
                    'max': None,
                },
            },
            'created': None,
            'info': market,
        }

    def parse_order_book(self, orderbook, symbol, timestamp=None, bidsKey='bids', asksKey='asks', priceKey: IndexType = 6, amountKey: IndexType = 8, countOrIdKey: IndexType = 2):
        nonce = None
        result: dict = {
            'symbol': symbol,
            'bids': [],
            'asks': [],
            'timestamp': None,
            'datetime': None,
            'nonce': None,
        }
        for i in range(0, len(orderbook)):
            level = orderbook[i]
            if timestamp is None:
                timestamp = self.safe_integer(level, 2)
            else:
                newTimestamp = self.safe_integer(level, 2)
                timestamp = max(timestamp, newTimestamp)
            if nonce is None:
                nonce = self.safe_integer(level, 0)
            else:
                newNonce = self.safe_integer(level, 0)
                nonce = max(nonce, newNonce)
            bidask = self.parse_bid_ask(level, priceKey, amountKey)
            levelSide = self.safe_integer(level, 9)
            side = asksKey if levelSide else bidsKey
            resultSide = result[side]
            resultSide.append(bidask)
        result['bids'] = self.sort_by(result['bids'], 0, True)
        result['asks'] = self.sort_by(result['asks'], 0)
        result['timestamp'] = timestamp
        result['datetime'] = self.iso8601(timestamp)
        result['nonce'] = nonce
        return result

    def fetch_order_book(self, symbol: str, limit: Int = None, params={}) -> OrderBook:
        """
        fetches information on open orders with bid(buy) and ask(sell) prices, volumes and other data

        https://apidoc.ndax.io/#getl2snapshot

        :param str symbol: unified symbol of the market to fetch the order book for
        :param int [limit]: the maximum amount of order book entries to return
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: A dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbols
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        market = self.market(symbol)
        limit = 100 if (limit is None) else limit  # default 100
        request: dict = {
            'omsId': omsId,
            'InstrumentId': market['id'],
            'Depth': limit,  # default 100
        }
        response = self.publicGetGetL2Snapshot(self.extend(request, params))
        #
        #     [
        #         [
        #             0,   # 0 MDUpdateId
        #             1,   # 1 Number of Unique Accounts
        #             123,  # 2 ActionDateTime in Posix format X 1000
        #             0,   # 3 ActionType 0(New), 1(Update), 2(Delete)
        #             0.0,  # 4 LastTradePrice
        #             0,   # 5 Number of Orders
        #             0.0,  # 6 Price
        #             0,   # 7 ProductPairCode
        #             0.0,  # 8 Quantity
        #             0,   # 9 Side
        #         ],
        #         [********,1,*************,0,19069.32,1,19069.31,8,0.140095,0],
        #         [********,0,*************,0,19069.32,1,19068.64,8,0.0055,0],
        #         [********,0,*************,0,19069.32,1,19068.26,8,0.021291,0],
        #         [********,1,*************,0,19069.32,1,19069.32,8,0.099636,1],
        #         [********,0,*************,0,19069.32,1,19069.98,8,0.1,1],
        #         [********,0,*************,0,19069.32,1,19069.99,8,0.141604,1],
        #     ]
        #
        return self.parse_order_book(response, symbol)

    def parse_ticker(self, ticker: dict, market: Market = None) -> Ticker:
        #
        # fetchTicker
        #
        #     {
        #         "OMSId":1,
        #         "InstrumentId":8,
        #         "BestBid":19069.31,
        #         "BestOffer":19069.32,
        #         "LastTradedPx":19069.32,
        #         "LastTradedQty":0.0001,
        #         "LastTradeTime":1607040406424,
        #         "SessionOpen":19069.32,
        #         "SessionHigh":19069.32,
        #         "SessionLow":19069.32,
        #         "SessionClose":19069.32,
        #         "Volume":0.0001,
        #         "CurrentDayVolume":0.0001,
        #         "CurrentDayNotional":1.906932,
        #         "CurrentDayNumTrades":1,
        #         "CurrentDayPxChange":0.00,
        #         "Rolling24HrVolume":0.000000000000000000000000000,
        #         "Rolling24HrNotional":0.00000000000000000000000,
        #         "Rolling24NumTrades":0,
        #         "Rolling24HrPxChange":0,
        #         "TimeStamp":"1607040406425",
        #         "BidQty":0,
        #         "AskQty":0,
        #         "BidOrderCt":0,
        #         "AskOrderCt":0,
        #         "Rolling24HrPxChangePercent":0,
        #     }
        #
        timestamp = self.safe_integer(ticker, 'TimeStamp')
        marketId = self.safe_string(ticker, 'InstrumentId')
        market = self.safe_market(marketId, market)
        symbol = self.safe_symbol(marketId, market)
        last = self.safe_string(ticker, 'LastTradedPx')
        percentage = self.safe_string(ticker, 'Rolling24HrPxChangePercent')
        change = self.safe_string(ticker, 'Rolling24HrPxChange')
        open = self.safe_string(ticker, 'SessionOpen')
        baseVolume = self.safe_string(ticker, 'Rolling24HrVolume')
        quoteVolume = self.safe_string(ticker, 'Rolling24HrNotional')
        return self.safe_ticker({
            'symbol': symbol,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'high': self.safe_string(ticker, 'SessionHigh'),
            'low': self.safe_string(ticker, 'SessionLow'),
            'bid': self.safe_string(ticker, 'BestBid'),
            'bidVolume': None,  # self.safe_number(ticker, 'BidQty'), always shows 0
            'ask': self.safe_string(ticker, 'BestOffer'),
            'askVolume': None,  # self.safe_number(ticker, 'AskQty'), always shows 0
            'vwap': None,
            'open': open,
            'close': last,
            'last': last,
            'previousClose': None,
            'change': change,
            'percentage': percentage,
            'average': None,
            'baseVolume': baseVolume,
            'quoteVolume': quoteVolume,
            'info': ticker,
        }, market)

    def fetch_ticker(self, symbol: str, params={}) -> Ticker:
        """
        fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market

        https://apidoc.ndax.io/#getlevel1

        :param str symbol: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'omsId': omsId,
            'InstrumentId': market['id'],
        }
        response = self.publicGetGetLevel1(self.extend(request, params))
        #
        #     {
        #         "OMSId":1,
        #         "InstrumentId":8,
        #         "BestBid":19069.31,
        #         "BestOffer":19069.32,
        #         "LastTradedPx":19069.32,
        #         "LastTradedQty":0.0001,
        #         "LastTradeTime":1607040406424,
        #         "SessionOpen":19069.32,
        #         "SessionHigh":19069.32,
        #         "SessionLow":19069.32,
        #         "SessionClose":19069.32,
        #         "Volume":0.0001,
        #         "CurrentDayVolume":0.0001,
        #         "CurrentDayNotional":1.906932,
        #         "CurrentDayNumTrades":1,
        #         "CurrentDayPxChange":0.00,
        #         "Rolling24HrVolume":0.000000000000000000000000000,
        #         "Rolling24HrNotional":0.00000000000000000000000,
        #         "Rolling24NumTrades":0,
        #         "Rolling24HrPxChange":0,
        #         "TimeStamp":"1607040406425",
        #         "BidQty":0,
        #         "AskQty":0,
        #         "BidOrderCt":0,
        #         "AskOrderCt":0,
        #         "Rolling24HrPxChangePercent":0,
        #     }
        #
        return self.parse_ticker(response, market)

    def parse_ohlcv(self, ohlcv, market: Market = None) -> list:
        #
        #     [
        #         1501603632000,  # 0 DateTime
        #         2700.33,       # 1 High
        #         2687.01,       # 2 Low
        #         2687.01,       # 3 Open
        #         2687.01,       # 4 Close
        #         24.86100992,   # 5 Volume
        #         0,             # 6 Inside Bid Price
        #         2870.95,       # 7 Inside Ask Price
        #         1              # 8 InstrumentId
        #     ]
        #
        return [
            self.safe_integer(ohlcv, 0),
            self.safe_number(ohlcv, 3),
            self.safe_number(ohlcv, 1),
            self.safe_number(ohlcv, 2),
            self.safe_number(ohlcv, 4),
            self.safe_number(ohlcv, 5),
        ]

    def fetch_ohlcv(self, symbol: str, timeframe='1m', since: Int = None, limit: Int = None, params={}) -> List[list]:
        """
        fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market

        https://apidoc.ndax.io/#gettickerhistory

        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'omsId': omsId,
            'InstrumentId': market['id'],
            'Interval': self.safe_string(self.timeframes, timeframe, timeframe),
        }
        duration = self.parse_timeframe(timeframe)
        now = self.milliseconds()
        if since is None:
            if limit is not None:
                request['FromDate'] = self.ymdhms(now - duration * limit * 1000)
                request['ToDate'] = self.ymdhms(now)
        else:
            request['FromDate'] = self.ymdhms(since)
            if limit is None:
                request['ToDate'] = self.ymdhms(now)
            else:
                request['ToDate'] = self.ymdhms(self.sum(since, duration * limit * 1000))
        response = self.publicGetGetTickerHistory(self.extend(request, params))
        #
        #     [
        #         [1607299260000,19069.32,19069.32,19069.32,19069.32,0,19069.31,19069.32,8,1607299200000],
        #         [1607299320000,19069.32,19069.32,19069.32,19069.32,0,19069.31,19069.32,8,1607299260000],
        #         [1607299380000,19069.32,19069.32,19069.32,19069.32,0,19069.31,19069.32,8,1607299320000],
        #     ]
        #
        return self.parse_ohlcvs(response, market, timeframe, since, limit)

    def parse_trade(self, trade: dict, market: Market = None) -> Trade:
        #
        # fetchTrades(public)
        #
        #     [
        #         6913253,       #  0 TradeId
        #         8,             #  1 ProductPairCode
        #         0.********,    #  2 Quantity
        #         19116.08,      #  3 Price
        #         **********,    #  4 Order1
        #         **********,    #  5 Order2
        #         *************,  #  6 Tradetime
        #         0,             #  7 Direction
        #         1,             #  8 TakerSide
        #         0,             #  9 BlockTrade
        #         0,             # 10 Either Order1ClientId or Order2ClientId
        #     ]
        #
        # fetchMyTrades(private)
        #
        #     {
        #         "OMSId":1,
        #         "ExecutionId":********,
        #         "TradeId":********,
        #         "OrderId":**********,
        #         "AccountId":449,
        #         "AccountName":"<EMAIL>",
        #         "SubAccountId":0,
        #         "ClientOrderId":0,
        #         "InstrumentId":8,
        #         "Side":"Sell",
        #         "OrderType":"Market",
        #         "Quantity":0.1230000000000000000000000000,
        #         "RemainingQuantity":0.0000000000000000000000000000,
        #         "Price":19069.*****************0000000,
        #         "Value":2345.5251300000000000000000000,
        #         "CounterParty":"7",
        #         "OrderTradeRevision":1,
        #         "Direction":"NoChange",
        #         "IsBlockTrade":false,
        #         "Fee":1.1727625650000000000000000000,
        #         "FeeProductId":8,
        #         "OrderOriginator":446,
        #         "UserName":"<EMAIL>",
        #         "TradeTimeMS":*************,
        #         "MakerTaker":"Taker",
        #         "AdapterTradeId":0,
        #         "InsideBid":19069.*****************0000000,
        #         "InsideBidSize":0.2400950000000000000000000000,
        #         "InsideAsk":19069.320000000000000000000000,
        #         "InsideAskSize":0.0997360000000000000000000000,
        #         "IsQuote":false,
        #         "CounterPartyClientUserId":1,
        #         "NotionalProductId":2,
        #         "NotionalRate":1.0000000000000000000000000000,
        #         "NotionalValue":2345.5251300000000000000000000,
        #         "NotionalHoldAmount":0,
        #         "TradeTime":637431618315686826
        #     }
        #
        # fetchOrderTrades
        #
        #     {
        #         "Side":"Sell",
        #         "OrderId":**********,
        #         "Price":18600.000000000000000000000000,
        #         "Quantity":0.0000000000000000000000000000,
        #         "DisplayQuantity":0.0000000000000000000000000000,
        #         "Instrument":8,
        #         "Account":449,
        #         "AccountName":"<EMAIL>",
        #         "OrderType":"Limit",
        #         "ClientOrderId":0,
        #         "OrderState":"FullyExecuted",
        #         "ReceiveTime":*************,
        #         "ReceiveTimeTicks":637431826449564182,
        #         "LastUpdatedTime":*************,
        #         "LastUpdatedTimeTicks":637431826449593893,
        #         "OrigQuantity":0.1230000000000000000000000000,
        #         "QuantityExecuted":0.1230000000000000000000000000,
        #         "GrossValueExecuted":2345.3947500000000000000000000,
        #         "ExecutableValue":0.0000000000000000000000000000,
        #         "AvgPrice":19068.250000000000000000000000,
        #         "CounterPartyId":0,
        #         "ChangeReason":"Trade",
        #         "OrigOrderId":**********,
        #         "OrigClOrdId":0,
        #         "EnteredBy":446,
        #         "UserName":"<EMAIL>",
        #         "IsQuote":false,
        #         "InsideAsk":19069.320000000000000000000000,
        #         "InsideAskSize":0.0997360000000000000000000000,
        #         "InsideBid":19068.250000000000000000000000,
        #         "InsideBidSize":1.3300010000000000000000000000,
        #         "LastTradePrice":19068.250000000000000000000000,
        #         "RejectReason":"",
        #         "IsLockedIn":false,
        #         "CancelReason":"",
        #         "OrderFlag":"0",
        #         "UseMargin":false,
        #         "StopPrice":0.0000000000000000000000000000,
        #         "PegPriceType":"Unknown",
        #         "PegOffset":0.0000000000000000000000000000,
        #         "PegLimitOffset":0.0000000000000000000000000000,
        #         "IpAddress":"x.x.x.x",
        #         "ClientOrderIdUuid":null,
        #         "OMSId":1
        #     }
        #
        priceString = None
        amountString = None
        costString = None
        timestamp = None
        id = None
        marketId = None
        side = None
        orderId = None
        takerOrMaker = None
        fee = None
        type = None
        if isinstance(trade, list):
            priceString = self.safe_string(trade, 3)
            amountString = self.safe_string(trade, 2)
            timestamp = self.safe_integer(trade, 6)
            id = self.safe_string(trade, 0)
            marketId = self.safe_string(trade, 1)
            takerSide = self.safe_value(trade, 8)
            side = 'sell' if takerSide else 'buy'
            orderId = self.safe_string(trade, 4)
        else:
            timestamp = self.safe_integer_2(trade, 'TradeTimeMS', 'ReceiveTime')
            id = self.safe_string(trade, 'TradeId')
            orderId = self.safe_string_2(trade, 'OrderId', 'OrigOrderId')
            marketId = self.safe_string_2(trade, 'InstrumentId', 'Instrument')
            priceString = self.safe_string(trade, 'Price')
            amountString = self.safe_string(trade, 'Quantity')
            costString = self.safe_string_2(trade, 'Value', 'GrossValueExecuted')
            takerOrMaker = self.safe_string_lower(trade, 'MakerTaker')
            side = self.safe_string_lower(trade, 'Side')
            type = self.safe_string_lower(trade, 'OrderType')
            feeCostString = self.safe_string(trade, 'Fee')
            if feeCostString is not None:
                feeCurrencyId = self.safe_string(trade, 'FeeProductId')
                feeCurrencyCode = self.safe_currency_code(feeCurrencyId)
                fee = {
                    'cost': feeCostString,
                    'currency': feeCurrencyCode,
                }
        symbol = self.safe_symbol(marketId, market)
        return self.safe_trade({
            'info': trade,
            'id': id,
            'symbol': symbol,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'order': orderId,
            'type': type,
            'side': side,
            'takerOrMaker': takerOrMaker,
            'price': priceString,
            'amount': amountString,
            'cost': costString,
            'fee': fee,
        }, market)

    def fetch_trades(self, symbol: str, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """
        get the list of most recent trades for a particular symbol
        :param str symbol: unified symbol of the market to fetch trades for
        :param int [since]: timestamp in ms of the earliest trade to fetch
        :param int [limit]: the maximum amount of trades to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=public-trades>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'omsId': omsId,
            'InstrumentId': market['id'],
        }
        if limit is not None:
            request['Count'] = limit
        response = self.publicGetGetLastTrades(self.extend(request, params))
        #
        #     [
        #         [6913253,8,0.********,19116.08,**********,**********,*************,0,1,0,0],
        #         [6913254,8,0.********,19117.42,**********,**********,*************,1,1,0,0],
        #         [6913255,8,0.000006,19107.81,**********,**********,*************,2,0,0,0],
        #     ]
        #
        return self.parse_trades(response, market, since, limit)

    def fetch_accounts(self, params={}) -> List[Account]:
        """
        fetch all the accounts associated with a profile

        https://apidoc.ndax.io/#getuseraccounts

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `account structures <https://docs.ccxt.com/#/?id=account-structure>` indexed by the account type
        """
        if not self.login:
            raise AuthenticationError(self.id + ' fetchAccounts() requires exchange.login email credential')
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.check_required_credentials()
        request: dict = {
            'omsId': omsId,
            'UserId': self.uid,
            'UserName': self.login,
        }
        response = self.privateGetGetUserAccounts(self.extend(request, params))
        #
        #     [449]  # comma-separated list of account ids
        #
        result = []
        for i in range(0, len(response)):
            accountId = self.safe_string(response, i)
            result.append({
                'id': accountId,
                'type': None,
                'currency': None,
                'info': accountId,
            })
        return result

    def parse_balance(self, response) -> Balances:
        result: dict = {
            'info': response,
            'timestamp': None,
            'datetime': None,
        }
        for i in range(0, len(response)):
            balance = response[i]
            currencyId = self.safe_string(balance, 'ProductId')
            if currencyId in self.currencies_by_id:
                code = self.safe_currency_code(currencyId)
                account = self.account()
                account['total'] = self.safe_string(balance, 'Amount')
                account['used'] = self.safe_string(balance, 'Hold')
                result[code] = account
        return self.safe_balance(result)

    def fetch_balance(self, params={}) -> Balances:
        """
        query for balance and get the amount of funds available for trading or funds locked in orders

        https://apidoc.ndax.io/#getaccountpositions

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `balance structure <https://docs.ccxt.com/#/?id=balance-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId')
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        if accountId is None:
            accountId = int(self.accounts[0]['id'])
        params = self.omit(params, ['accountId', 'AccountId'])
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
        }
        response = self.privateGetGetAccountPositions(self.extend(request, params))
        #
        #     [
        #         {
        #             "OMSId":1,
        #             "AccountId":449,
        #             "ProductSymbol":"BTC",
        #             "ProductId":1,
        #             "Amount":10.000000000000000000000000000,
        #             "Hold":0,
        #             "PendingDeposits":0.0000000000000000000000000000,
        #             "PendingWithdraws":0.0000000000000000000000000000,
        #             "TotalDayDeposits":10.000000000000000000000000000,
        #             "TotalMonthDeposits":10.000000000000000000000000000,
        #             "TotalYearDeposits":10.000000000000000000000000000,
        #             "TotalDayDepositNotional":10.000000000000000000000000000,
        #             "TotalMonthDepositNotional":10.000000000000000000000000000,
        #             "TotalYearDepositNotional":10.000000000000000000000000000,
        #             "TotalDayWithdraws":0,
        #             "TotalMonthWithdraws":0,
        #             "TotalYearWithdraws":0,
        #             "TotalDayWithdrawNotional":0,
        #             "TotalMonthWithdrawNotional":0,
        #             "TotalYearWithdrawNotional":0,
        #             "NotionalProductId":8,
        #             "NotionalProductSymbol":"USDT",
        #             "NotionalValue":10.000000000000000000000000000,
        #             "NotionalHoldAmount":0,
        #             "NotionalRate":1
        #         },
        #     ]
        #
        return self.parse_balance(response)

    def parse_ledger_entry_type(self, type):
        types: dict = {
            'Trade': 'trade',
            'Deposit': 'transaction',
            'Withdraw': 'transaction',
            'Transfer': 'transfer',
            'OrderHold': 'trade',
            'WithdrawHold': 'transaction',
            'DepositHold': 'transaction',
            'MarginHold': 'trade',
            'ManualHold': 'trade',
            'ManualEntry': 'trade',
            'MarginAcquisition': 'trade',
            'MarginRelinquish': 'trade',
            'MarginQuoteHold': 'trade',
        }
        return self.safe_string(types, type, type)

    def parse_ledger_entry(self, item: dict, currency: Currency = None) -> LedgerEntry:
        #
        #     {
        #         "TransactionId": **********,
        #         "ReferenceId": 68,
        #         "OMSId": 1,
        #         "AccountId": 449,
        #         "CR": 10.000000000000000000000000000,
        #         "DR": 0.0000000000000000000000000000,
        #         "Counterparty": 3,
        #         "TransactionType": "Other",
        #         "ReferenceType": "Deposit",
        #         "ProductId": 1,
        #         "Balance": 10.000000000000000000000000000,
        #         "TimeStamp": *************
        #     }
        #
        currencyId = self.safe_string(item, 'ProductId')
        currency = self.safe_currency(currencyId, currency)
        credit = self.safe_string(item, 'CR')
        debit = self.safe_string(item, 'DR')
        amount = None
        direction = None
        if Precise.string_lt(credit, '0'):
            amount = credit
            direction = 'in'
        elif Precise.string_lt(debit, '0'):
            amount = debit
            direction = 'out'
        before = None
        after = self.safe_string(item, 'Balance')
        if direction == 'out':
            before = Precise.string_add(after, amount)
        elif direction == 'in':
            before = Precise.string_max('0', Precise.string_sub(after, amount))
        timestamp = self.safe_integer(item, 'TimeStamp')
        return self.safe_ledger_entry({
            'info': item,
            'id': self.safe_string(item, 'TransactionId'),
            'direction': direction,
            'account': self.safe_string(item, 'AccountId'),
            'referenceId': self.safe_string(item, 'ReferenceId'),
            'referenceAccount': self.safe_string(item, 'Counterparty'),
            'type': self.parse_ledger_entry_type(self.safe_string(item, 'ReferenceType')),
            'currency': self.safe_currency_code(currencyId, currency),
            'amount': self.parse_number(amount),
            'before': self.parse_number(before),
            'after': self.parse_number(after),
            'status': 'ok',
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'fee': None,
        }, currency)

    def fetch_ledger(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[LedgerEntry]:
        """
        fetch the history of changes, actions done by the user or operations that altered the balance of the user

        https://apidoc.ndax.io/#getaccounttransactions

        :param str [code]: unified currency code, default is None
        :param int [since]: timestamp in ms of the earliest ledger entry, default is None
        :param int [limit]: max number of ledger entries to return, default is None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ledger structure <https://docs.ccxt.com/#/?id=ledger>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
        }
        if limit is not None:
            request['Depth'] = limit
        response = self.privateGetGetAccountTransactions(self.extend(request, params))
        #
        #     [
        #         {
        #             "TransactionId":**********,
        #             "ReferenceId":68,
        #             "OMSId":1,
        #             "AccountId":449,
        #             "CR":10.000000000000000000000000000,
        #             "DR":0.0000000000000000000000000000,
        #             "Counterparty":3,
        #             "TransactionType":"Other",
        #             "ReferenceType":"Deposit",
        #             "ProductId":1,
        #             "Balance":10.000000000000000000000000000,
        #             "TimeStamp":*************
        #         },
        #     ]
        #
        currency = None
        if code is not None:
            currency = self.currency(code)
        return self.parse_ledger(response, currency, since, limit)

    def parse_order_status(self, status: Str):
        statuses: dict = {
            'Accepted': 'open',
            'Rejected': 'rejected',
            'Working': 'open',
            'Canceled': 'canceled',
            'Expired': 'expired',
            'FullyExecuted': 'closed',
        }
        return self.safe_string(statuses, status, status)

    def parse_order(self, order: dict, market: Market = None) -> Order:
        #
        # createOrder
        #
        #     {
        #         "status":"Accepted",
        #         "errormsg":"",
        #         "OrderId": **********
        #     }
        #
        # editOrder
        #
        #     {
        #         "ReplacementOrderId": 1234,
        #         "ReplacementClOrdId": 1561,
        #         "OrigOrderId": 5678,
        #         "OrigClOrdId": 91011,
        #     }
        #
        # fetchOpenOrders, fetchClosedOrders
        #
        #     {
        #         "Side":"Buy",
        #         "OrderId":**********,
        #         "Price":19010,
        #         "Quantity":0.345,
        #         "DisplayQuantity":0.345,
        #         "Instrument":8,
        #         "Account":449,
        #         "AccountName":"<EMAIL>",
        #         "OrderType":"Limit",
        #         "ClientOrderId":0,
        #         "OrderState":"Working",
        #         "ReceiveTime":*************,
        #         "ReceiveTimeTicks":637431761260028981,
        #         "LastUpdatedTime":*************,
        #         "LastUpdatedTimeTicks":637431761260054714,
        #         "OrigQuantity":0.345,
        #         "QuantityExecuted":0,
        #         "GrossValueExecuted":0,
        #         "ExecutableValue":0,
        #         "AvgPrice":0,
        #         "CounterPartyId":0,
        #         "ChangeReason":"NewInputAccepted",
        #         "OrigOrderId":**********,
        #         "OrigClOrdId":0,
        #         "EnteredBy":446,
        #         "UserName":"<EMAIL>",
        #         "IsQuote":false,
        #         "InsideAsk":19069.32,
        #         "InsideAskSize":0.099736,
        #         "InsideBid":19068.25,
        #         "InsideBidSize":1.330001,
        #         "LastTradePrice":19068.25,
        #         "RejectReason":"",
        #         "IsLockedIn":false,
        #         "CancelReason":"",
        #         "OrderFlag":"AddedToBook",
        #         "UseMargin":false,
        #         "StopPrice":0,
        #         "PegPriceType":"Unknown",
        #         "PegOffset":0,
        #         "PegLimitOffset":0,
        #         "IpAddress":null,
        #         "ClientOrderIdUuid":null,
        #         "OMSId":1
        #     }
        #
        timestamp = self.safe_integer(order, 'ReceiveTime')
        marketId = self.safe_string(order, 'Instrument')
        return self.safe_order({
            'id': self.safe_string_2(order, 'ReplacementOrderId', 'OrderId'),
            'clientOrderId': self.safe_string_2(order, 'ReplacementClOrdId', 'ClientOrderId'),
            'info': order,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'lastTradeTimestamp': self.safe_integer(order, 'LastUpdatedTime'),
            'status': self.parse_order_status(self.safe_string(order, 'OrderState')),
            'symbol': self.safe_symbol(marketId, market),
            'type': self.safe_string_lower(order, 'OrderType'),
            'timeInForce': None,
            'postOnly': None,
            'side': self.safe_string_lower(order, 'Side'),
            'price': self.safe_string(order, 'Price'),
            'triggerPrice': self.parse_number(self.omit_zero(self.safe_string(order, 'StopPrice'))),
            'cost': self.safe_string(order, 'GrossValueExecuted'),
            'amount': self.safe_string(order, 'OrigQuantity'),
            'filled': self.safe_string(order, 'QuantityExecuted'),
            'average': self.safe_string(order, 'AvgPrice'),
            'remaining': None,
            'fee': None,
            'trades': None,
        }, market)

    def create_order(self, symbol: str, type: OrderType, side: OrderSide, amount: float, price: Num = None, params={}):
        """
        create a trade order

        https://apidoc.ndax.io/#sendorder

        :param str symbol: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much of currency you want to trade in units of base currency
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param float [params.triggerPrice]: the price at which a trigger order would be triggered
        :param str [params.clientOrderId]: a unique id for the order
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        clientOrderId = self.safe_integer_2(params, 'ClientOrderId', 'clientOrderId')
        orderType = self.safe_integer(self.options['orderTypes'], self.capitalize(type))
        triggerPrice = self.safe_string(params, 'triggerPrice')
        if triggerPrice is not None:
            if type == 'market':
                orderType = 3
            elif type == 'limit':
                orderType = 4
        params = self.omit(params, ['accountId', 'AccountId', 'clientOrderId', 'ClientOrderId', 'triggerPrice'])
        market = self.market(symbol)
        orderSide = 0 if (side == 'buy') else 1
        request: dict = {
            'InstrumentId': int(market['id']),
            'omsId': omsId,
            'AccountId': accountId,
            'TimeInForce': 1,  # 0 Unknown, 1 GTC by default, 2 OPG execute to opening price, 3 IOC immediate or canceled,  4 FOK fill-or-kill, 5 GTX good 'til executed, 6 GTD good 'til date
            # 'ClientOrderId': clientOrderId,  # defaults to 0
            # If self order is order A, OrderIdOCO refers to the order ID of an order B(which is not the order being created by self call).
            # If order B executes, then order A created by self call is canceled.
            # You can also set up order B to watch order A in the same way, but that may require an update to order B to make it watch self one, which could have implications for priority in the order book.
            # See CancelReplaceOrder and ModifyOrder.
            # 'OrderIdOCO': 0,  # The order ID if One Cancels the Other.
            # 'UseDisplayQuantity': False,  # If you enter a Limit order with a reserve, you must set UseDisplayQuantity to True
            'Side': orderSide,  # 0 Buy, 1 Sell, 2 Short, 3 unknown an error condition
            'Quantity': float(self.amount_to_precision(symbol, amount)),
            'OrderType': orderType,  # 0 Unknown, 1 Market, 2 Limit, 3 StopMarket, 4 StopLimit, 5 TrailingStopMarket, 6 TrailingStopLimit, 7 BlockTrade
            # 'PegPriceType': 3,  # 1 Last, 2 Bid, 3 Ask, 4 Midpoint
            # 'LimitPrice': float(self.price_to_precision(symbol, price)),
        }
        # If OrderType=1(Market), Side=0(Buy), and LimitPrice is supplied, the Market order will execute up to the value specified
        if price is not None:
            request['LimitPrice'] = float(self.price_to_precision(symbol, price))
        if clientOrderId is not None:
            request['ClientOrderId'] = clientOrderId
        if triggerPrice is not None:
            request['StopPrice'] = triggerPrice
        response = self.privatePostSendOrder(self.extend(request, params))
        #
        #     {
        #         "status":"Accepted",
        #         "errormsg":"",
        #         "OrderId": **********
        #     }
        #
        return self.parse_order(response, market)

    def edit_order(self, id: str, symbol: str, type: OrderType, side: OrderSide, amount: Num = None, price: Num = None, params={}):
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        clientOrderId = self.safe_integer_2(params, 'ClientOrderId', 'clientOrderId')
        params = self.omit(params, ['accountId', 'AccountId', 'clientOrderId', 'ClientOrderId'])
        market = self.market(symbol)
        orderSide = 0 if (side == 'buy') else 1
        request: dict = {
            'OrderIdToReplace': int(id),
            'InstrumentId': int(market['id']),
            'omsId': omsId,
            'AccountId': accountId,
            'TimeInForce': 1,  # 0 Unknown, 1 GTC by default, 2 OPG execute to opening price, 3 IOC immediate or canceled,  4 FOK fill-or-kill, 5 GTX good 'til executed, 6 GTD good 'til date
            # 'ClientOrderId': clientOrderId,  # defaults to 0
            # If self order is order A, OrderIdOCO refers to the order ID of an order B(which is not the order being created by self call).
            # If order B executes, then order A created by self call is canceled.
            # You can also set up order B to watch order A in the same way, but that may require an update to order B to make it watch self one, which could have implications for priority in the order book.
            # See CancelReplaceOrder and ModifyOrder.
            # 'OrderIdOCO': 0,  # The order ID if One Cancels the Other.
            # 'UseDisplayQuantity': False,  # If you enter a Limit order with a reserve, you must set UseDisplayQuantity to True
            'Side': orderSide,  # 0 Buy, 1 Sell, 2 Short, 3 unknown an error condition
            'Quantity': float(self.amount_to_precision(symbol, amount)),
            'OrderType': self.safe_integer(self.options['orderTypes'], self.capitalize(type)),  # 0 Unknown, 1 Market, 2 Limit, 3 StopMarket, 4 StopLimit, 5 TrailingStopMarket, 6 TrailingStopLimit, 7 BlockTrade
            # 'PegPriceType': 3,  # 1 Last, 2 Bid, 3 Ask, 4 Midpoint
            # 'LimitPrice': float(self.price_to_precision(symbol, price)),
        }
        # If OrderType=1(Market), Side=0(Buy), and LimitPrice is supplied, the Market order will execute up to the value specified
        if price is not None:
            request['LimitPrice'] = float(self.price_to_precision(symbol, price))
        if clientOrderId is not None:
            request['ClientOrderId'] = clientOrderId
        response = self.privatePostCancelReplaceOrder(self.extend(request, params))
        #
        #     {
        #         "replacementOrderId": 1234,
        #         "replacementClOrdId": 1561,
        #         "origOrderId": 5678,
        #         "origClOrdId": 91011,
        #     }
        #
        return self.parse_order(response, market)

    def fetch_my_trades(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all trades made by the user

        https://apidoc.ndax.io/#gettradeshistory

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
            # 'InstrumentId': market['id'],
            # 'TradeId': 123,  # If you specify TradeId, GetTradesHistory can return all states for a single trade
            # 'OrderId': 456,  # If specified, the call returns all trades associated with the order
            # 'UserId': integer. The ID of the logged-in user. If not specified, the call returns trades associated with the users belonging to the default account for the logged-in user of self OMS.
            # 'StartTimeStamp': long integer. The historical date and time at which to begin the trade report, in POSIX format. If not specified, reverts to the start date of self account on the trading venue.
            # 'EndTimeStamp': long integer. Date at which to end the trade report, in POSIX format.
            # 'Depth': integer. In self case, the count of trades to return, counting from the StartIndex. If Depth is not specified, returns all trades between BeginTimeStamp and EndTimeStamp, beginning at StartIndex.
            # 'StartIndex': 0  # from the most recent trade 0 and moving backwards in time
            # 'ExecutionId': 123,  # The ID of the individual buy or sell execution. If not specified, returns all.
        }
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['InstrumentId'] = market['id']
        if since is not None:
            request['StartTimeStamp'] = self.parse_to_int(since / 1000)
        if limit is not None:
            request['Depth'] = limit
        response = self.privateGetGetTradesHistory(self.extend(request, params))
        #
        #     [
        #         {
        #             "OMSId":1,
        #             "ExecutionId":********,
        #             "TradeId":********,
        #             "OrderId":**********,
        #             "AccountId":449,
        #             "AccountName":"<EMAIL>",
        #             "SubAccountId":0,
        #             "ClientOrderId":0,
        #             "InstrumentId":8,
        #             "Side":"Sell",
        #             "OrderType":"Market",
        #             "Quantity":0.1230000000000000000000000000,
        #             "RemainingQuantity":0.0000000000000000000000000000,
        #             "Price":19069.*****************0000000,
        #             "Value":2345.5251300000000000000000000,
        #             "CounterParty":"7",
        #             "OrderTradeRevision":1,
        #             "Direction":"NoChange",
        #             "IsBlockTrade":false,
        #             "Fee":1.1727625650000000000000000000,
        #             "FeeProductId":8,
        #             "OrderOriginator":446,
        #             "UserName":"<EMAIL>",
        #             "TradeTimeMS":*************,
        #             "MakerTaker":"Taker",
        #             "AdapterTradeId":0,
        #             "InsideBid":19069.*****************0000000,
        #             "InsideBidSize":0.2400950000000000000000000000,
        #             "InsideAsk":19069.320000000000000000000000,
        #             "InsideAskSize":0.0997360000000000000000000000,
        #             "IsQuote":false,
        #             "CounterPartyClientUserId":1,
        #             "NotionalProductId":2,
        #             "NotionalRate":1.0000000000000000000000000000,
        #             "NotionalValue":2345.5251300000000000000000000,
        #             "NotionalHoldAmount":0,
        #             "TradeTime":637431618315686826
        #         }
        #     ]
        #
        return self.parse_trades(response, market, since, limit)

    def cancel_all_orders(self, symbol: Str = None, params={}):
        """
        cancel all open orders

        https://apidoc.ndax.io/#cancelallorders

        :param str symbol: unified market symbol, only orders in the market of self symbol are cancelled when symbol is not None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
        }
        if symbol is not None:
            market = self.market(symbol)
            request['IntrumentId'] = market['id']
        response = self.privatePostCancelAllOrders(self.extend(request, params))
        #
        #     {
        #         "result":true,
        #         "errormsg":null,
        #         "errorcode":0,
        #         "detail":null
        #     }
        #
        return [
            self.safe_order({
                'info': response,
            }),
        ]

    def cancel_order(self, id: str, symbol: Str = None, params={}):
        """
        cancels an open order

        https://apidoc.ndax.io/#cancelorder

        :param str id: order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.clientOrderId]: a unique id for the order
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        # defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        # accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        # params = self.omit(params, ['accountId', 'AccountId'])
        market = None
        if symbol is not None:
            market = self.market(symbol)
        request: dict = {
            'omsId': omsId,
            # 'AccountId': accountId,
        }
        clientOrderId = self.safe_integer_2(params, 'clientOrderId', 'ClOrderId')
        if clientOrderId is not None:
            request['ClOrderId'] = clientOrderId
        else:
            request['OrderId'] = int(id)
        params = self.omit(params, ['clientOrderId', 'ClOrderId'])
        response = self.privatePostCancelOrder(self.extend(request, params))
        order = self.parse_order(response, market)
        return self.extend(order, {
            'id': id,
            'clientOrderId': clientOrderId,
        })

    def fetch_open_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetch all unfilled currently open orders

        https://apidoc.ndax.io/#getopenorders

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch open orders for
        :param int [limit]: the maximum number of  open orders structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        market = None
        if symbol is not None:
            market = self.market(symbol)
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
        }
        response = self.privateGetGetOpenOrders(self.extend(request, params))
        #
        #     [
        #         {
        #             "Side":"Buy",
        #             "OrderId":**********,
        #             "Price":19010,
        #             "Quantity":0.345,
        #             "DisplayQuantity":0.345,
        #             "Instrument":8,
        #             "Account":449,
        #             "AccountName":"<EMAIL>",
        #             "OrderType":"Limit",
        #             "ClientOrderId":0,
        #             "OrderState":"Working",
        #             "ReceiveTime":*************,
        #             "ReceiveTimeTicks":637431761260028981,
        #             "LastUpdatedTime":*************,
        #             "LastUpdatedTimeTicks":637431761260054714,
        #             "OrigQuantity":0.345,
        #             "QuantityExecuted":0,
        #             "GrossValueExecuted":0,
        #             "ExecutableValue":0,
        #             "AvgPrice":0,
        #             "CounterPartyId":0,
        #             "ChangeReason":"NewInputAccepted",
        #             "OrigOrderId":**********,
        #             "OrigClOrdId":0,
        #             "EnteredBy":446,
        #             "UserName":"<EMAIL>",
        #             "IsQuote":false,
        #             "InsideAsk":19069.32,
        #             "InsideAskSize":0.099736,
        #             "InsideBid":19068.25,
        #             "InsideBidSize":1.330001,
        #             "LastTradePrice":19068.25,
        #             "RejectReason":"",
        #             "IsLockedIn":false,
        #             "CancelReason":"",
        #             "OrderFlag":"AddedToBook",
        #             "UseMargin":false,
        #             "StopPrice":0,
        #             "PegPriceType":"Unknown",
        #             "PegOffset":0,
        #             "PegLimitOffset":0,
        #             "IpAddress":null,
        #             "ClientOrderIdUuid":null,
        #             "OMSId":1
        #         }
        #     ]
        #
        return self.parse_orders(response, market, since, limit)

    def fetch_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetches information on multiple orders made by the user

        https://apidoc.ndax.io/#getorderhistory

        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
            # 'ClientOrderId': clientOrderId,
            # 'OriginalOrderId': id,
            # 'OriginalClientOrderId': long integer,
            # 'UserId': integer,
            # 'InstrumentId': market['id'],
            # 'StartTimestamp': since,
            # 'EndTimestamp': self.milliseconds(),
            # 'Depth': limit,
            # 'StartIndex': 0,
        }
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['InstrumentId'] = market['id']
        if since is not None:
            request['StartTimeStamp'] = self.parse_to_int(since / 1000)
        if limit is not None:
            request['Depth'] = limit
        response = self.privateGetGetOrdersHistory(self.extend(request, params))
        #
        #     [
        #         {
        #             "Side":"Buy",
        #             "OrderId":**********,
        #             "Price":19010.000000000000000000000000,
        #             "Quantity":0.0000000000000000000000000000,
        #             "DisplayQuantity":0.3450000000000000000000000000,
        #             "Instrument":8,
        #             "Account":449,
        #             "AccountName":"<EMAIL>",
        #             "OrderType":"Limit",
        #             "ClientOrderId":0,
        #             "OrderState":"Canceled",
        #             "ReceiveTime":*************,
        #             "ReceiveTimeTicks":637431761260028981,
        #             "LastUpdatedTime":*************,
        #             "LastUpdatedTimeTicks":637431777653463754,
        #             "OrigQuantity":0.3450000000000000000000000000,
        #             "QuantityExecuted":0.0000000000000000000000000000,
        #             "GrossValueExecuted":0.0000000000000000000000000000,
        #             "ExecutableValue":0.0000000000000000000000000000,
        #             "AvgPrice":0.0000000000000000000000000000,
        #             "CounterPartyId":0,
        #             "ChangeReason":"UserModified",
        #             "OrigOrderId":**********,
        #             "OrigClOrdId":0,
        #             "EnteredBy":446,
        #             "UserName":"<EMAIL>",
        #             "IsQuote":false,
        #             "InsideAsk":19069.320000000000000000000000,
        #             "InsideAskSize":0.0997360000000000000000000000,
        #             "InsideBid":19068.250000000000000000000000,
        #             "InsideBidSize":1.3300010000000000000000000000,
        #             "LastTradePrice":19068.250000000000000000000000,
        #             "RejectReason":"",
        #             "IsLockedIn":false,
        #             "CancelReason":"UserModified",
        #             "OrderFlag":"AddedToBook, RemovedFromBook",
        #             "UseMargin":false,
        #             "StopPrice":0.0000000000000000000000000000,
        #             "PegPriceType":"Unknown",
        #             "PegOffset":0.0000000000000000000000000000,
        #             "PegLimitOffset":0.0000000000000000000000000000,
        #             "IpAddress":"x.x.x.x",
        #             "ClientOrderIdUuid":null,
        #             "OMSId":1
        #         },
        #     ]
        #
        return self.parse_orders(response, market, since, limit)

    def fetch_order(self, id: str, symbol: Str = None, params={}):
        """
        fetches information on an order made by the user

        https://apidoc.ndax.io/#getorderstatus

        :param str id: order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        market = None
        if symbol is not None:
            market = self.market(symbol)
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
            'OrderId': int(id),
        }
        response = self.privateGetGetOrderStatus(self.extend(request, params))
        #
        #     {
        #         "Side":"Sell",
        #         "OrderId":**********,
        #         "Price":0.0000000000000000000000000000,
        #         "Quantity":0.0000000000000000000000000000,
        #         "DisplayQuantity":0.0000000000000000000000000000,
        #         "Instrument":8,
        #         "Account":449,
        #         "AccountName":"<EMAIL>",
        #         "OrderType":"Market",
        #         "ClientOrderId":0,
        #         "OrderState":"FullyExecuted",
        #         "ReceiveTime":*************,
        #         "ReceiveTimeTicks":637431662755912377,
        #         "LastUpdatedTime":*************,
        #         "LastUpdatedTimeTicks":637431662755960902,
        #         "OrigQuantity":1.0000000000000000000000000000,
        #         "QuantityExecuted":1.0000000000000000000000000000,
        #         "GrossValueExecuted":19068.270478610000000000000000,
        #         "ExecutableValue":0.0000000000000000000000000000,
        #         "AvgPrice":19068.270478610000000000000000,
        #         "CounterPartyId":0,
        #         "ChangeReason":"Trade",
        #         "OrigOrderId":**********,
        #         "OrigClOrdId":0,
        #         "EnteredBy":446,
        #         "UserName":"<EMAIL>",
        #         "IsQuote":false,
        #         "InsideAsk":19069.320000000000000000000000,
        #         "InsideAskSize":0.0997360000000000000000000000,
        #         "InsideBid":19069.*****************0000000,
        #         "InsideBidSize":0.2400950000000000000000000000,
        #         "LastTradePrice":19069.*****************0000000,
        #         "RejectReason":"",
        #         "IsLockedIn":false,
        #         "CancelReason":"",
        #         "OrderFlag":"0",
        #         "UseMargin":false,
        #         "StopPrice":0.0000000000000000000000000000,
        #         "PegPriceType":"Unknown",
        #         "PegOffset":0.0000000000000000000000000000,
        #         "PegLimitOffset":0.0000000000000000000000000000,
        #         "IpAddress":"x.x.x.x",
        #         "ClientOrderIdUuid":null,
        #         "OMSId":1
        #     }
        #
        return self.parse_order(response, market)

    def fetch_order_trades(self, id: str, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all the trades made from a single order

        https://apidoc.ndax.io/#getorderhistorybyorderid

        :param str id: order id
        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        # defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        # accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        # params = self.omit(params, ['accountId', 'AccountId'])
        market = None
        if symbol is not None:
            market = self.market(symbol)
        request: dict = {
            'OMSId': self.parse_to_int(omsId),
            # 'AccountId': accountId,
            'OrderId': int(id),
        }
        response = self.privatePostGetOrderHistoryByOrderId(self.extend(request, params))
        #
        #     [
        #         {
        #             "Side":"Sell",
        #             "OrderId":**********,
        #             "Price":18600.000000000000000000000000,
        #             "Quantity":0.0000000000000000000000000000,
        #             "DisplayQuantity":0.0000000000000000000000000000,
        #             "Instrument":8,
        #             "Account":449,
        #             "AccountName":"<EMAIL>",
        #             "OrderType":"Limit",
        #             "ClientOrderId":0,
        #             "OrderState":"FullyExecuted",
        #             "ReceiveTime":*************,
        #             "ReceiveTimeTicks":637431826449564182,
        #             "LastUpdatedTime":*************,
        #             "LastUpdatedTimeTicks":637431826449593893,
        #             "OrigQuantity":0.1230000000000000000000000000,
        #             "QuantityExecuted":0.1230000000000000000000000000,
        #             "GrossValueExecuted":2345.3947500000000000000000000,
        #             "ExecutableValue":0.0000000000000000000000000000,
        #             "AvgPrice":19068.250000000000000000000000,
        #             "CounterPartyId":0,
        #             "ChangeReason":"Trade",
        #             "OrigOrderId":**********,
        #             "OrigClOrdId":0,
        #             "EnteredBy":446,
        #             "UserName":"<EMAIL>",
        #             "IsQuote":false,
        #             "InsideAsk":19069.320000000000000000000000,
        #             "InsideAskSize":0.0997360000000000000000000000,
        #             "InsideBid":19068.250000000000000000000000,
        #             "InsideBidSize":1.3300010000000000000000000000,
        #             "LastTradePrice":19068.250000000000000000000000,
        #             "RejectReason":"",
        #             "IsLockedIn":false,
        #             "CancelReason":"",
        #             "OrderFlag":"0",
        #             "UseMargin":false,
        #             "StopPrice":0.0000000000000000000000000000,
        #             "PegPriceType":"Unknown",
        #             "PegOffset":0.0000000000000000000000000000,
        #             "PegLimitOffset":0.0000000000000000000000000000,
        #             "IpAddress":"x.x.x.x",
        #             "ClientOrderIdUuid":null,
        #             "OMSId":1
        #         },
        #     ]
        #
        grouped = self.group_by(response, 'ChangeReason')
        trades = self.safe_list(grouped, 'Trade', [])
        return self.parse_trades(trades, market, since, limit)

    def fetch_deposit_address(self, code: str, params={}) -> DepositAddress:
        """
        fetch the deposit address for a currency associated with self account
        :param str code: unified currency code
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `address structure <https://docs.ccxt.com/#/?id=address-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        currency = self.currency(code)
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
            'ProductId': currency['id'],
            'GenerateNewKey': False,
        }
        response = self.privateGetGetDepositInfo(self.extend(request, params))
        #
        #     {
        #         "result":true,
        #         "errormsg":null,
        #         "statuscode":0,
        #         "AssetManagerId":1,
        #         "AccountId":57922,
        #         "AssetId":16,
        #         "ProviderId":23,
        #         "DepositInfo":"[\"0x8A27564b5c30b91C93B1591821642420F323a210\"]"
        #     }
        #
        return self.parse_deposit_address(response, currency)

    def parse_deposit_address(self, depositAddress, currency: Currency = None) -> DepositAddress:
        #
        # fetchDepositAddress, createDepositAddress
        #
        #     {
        #         "result":true,
        #         "errormsg":null,
        #         "statuscode":0,
        #         "AssetManagerId":1,
        #         "AccountId":449,
        #         "AssetId":1,
        #         "ProviderId":1,
        #         "DepositInfo":"[\"r3e95RwVsLH7yCbnMfyh7SA8FdwUJCB4S2?memo=*********\"]"
        #     }
        #
        depositInfoString = self.safe_string(depositAddress, 'DepositInfo')
        depositInfo = json.loads(depositInfoString)
        depositInfoLength = len(depositInfo)
        lastString = self.safe_string(depositInfo, depositInfoLength - 1)
        parts = lastString.split('?memo=')
        address = self.safe_string(parts, 0)
        tag = self.safe_string(parts, 1)
        code = None
        if currency is not None:
            code = currency['code']
        self.check_address(address)
        return {
            'info': depositAddress,
            'currency': code,
            'network': None,
            'address': address,
            'tag': tag,
        }

    def create_deposit_address(self, code: str, params={}) -> DepositAddress:
        """
        create a currency deposit address
        :param str code: unified currency code of the currency for the deposit address
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `address structure <https://docs.ccxt.com/#/?id=address-structure>`
        """
        request: dict = {
            'GenerateNewKey': True,
        }
        return self.fetch_deposit_address(code, self.extend(request, params))

    def fetch_deposits(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all deposits made to an account

        https://apidoc.ndax.io/#getdeposits

        :param str code: unified currency code
        :param int [since]: not used by ndax fetchDeposits
        :param int [limit]: the maximum number of deposits structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        currency = None
        if code is not None:
            currency = self.currency(code)
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
        }
        response = self.privateGetGetDeposits(self.extend(request, params))
        #
        #    "[
        #        {
        #            "OMSId": 1,
        #            "DepositId": 44,
        #            "AccountId": 449,
        #            "SubAccountId": 0,
        #            "ProductId": 4,
        #            "Amount": 200.00000000000000000000000000,
        #            "LastUpdateTimeStamp": 637431291261187806,
        #            "ProductType": "CryptoCurrency",
        #            "TicketStatus": "FullyProcessed",
        #            "DepositInfo": "{
        #                "AccountProviderId":42,
        #                "AccountProviderName":"USDT_BSC",
        #                "TXId":"0x3879b02632c69482646409e991149290bc9a58e4603be63c7c2c90a843f45d2b",
        #                "FromAddress":"******************************************",
        #                "ToAddress":"******************************************"
        #            },",
        #            "DepositCode": "ab0e23d5-a9ce-4d94-865f-9ab464fb1de3",
        #            "TicketNumber": 71,
        #            "NotionalProductId": 13,
        #            "NotionalValue": 200.00000000000000000000000000,
        #            "FeeAmount": 0.0000000000000000000000000000,
        #        },
        #        ...
        #    ]"
        #
        if isinstance(response, str):
            return self.parse_transactions(json.loads(response), currency, since, limit)
        return self.parse_transactions(response, currency, since, limit)

    def fetch_withdrawals(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all withdrawals made from an account

        https://apidoc.ndax.io/#getwithdraws

        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch withdrawals for
        :param int [limit]: the maximum number of withdrawals structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        currency = None
        if code is not None:
            currency = self.currency(code)
        request: dict = {
            'omsId': omsId,
            'AccountId': accountId,
        }
        response = self.privateGetGetWithdraws(self.extend(request, params))
        #
        #     [
        #         {
        #             "Amount": 0.0,
        #             "FeeAmount": 0.0,
        #             "NotionalValue": 0.0,
        #             "WithdrawId": 0,
        #             "AssetManagerId": 0,
        #             "AccountId": 0,
        #             "AssetId": 0,
        #             "TemplateForm": "{\"TemplateType\": \"TetherRPCWithdraw\",\"Comment\": \"TestWithdraw\",\"ExternalAddress\": \"ms6C3pKAAr8gRCcnVebs8VRkVrjcvqNYv3\"}",
        #             "TemplateFormType": "TetherRPCWithdraw",
        #             "omsId": 0,
        #             "TicketStatus": 0,
        #             "TicketNumber": 0,
        #             "WithdrawTransactionDetails": "",
        #             "WithdrawType": "",
        #             "WithdrawCode": "490b4fa3-53fc-44f4-bd29-7e16be86fba3",
        #             "AssetType": 0,
        #             "Reaccepted": True,
        #             "NotionalProductId": 0
        #         },
        #     ]
        #
        return self.parse_transactions(response, currency, since, limit)

    def parse_transaction_status_by_type(self, status, type=None):
        statusesByType: dict = {
            'deposit': {
                'New': 'pending',  # new ticket awaiting operator review
                'AdminProcessing': 'pending',  # an admin is looking at the ticket
                'Accepted': 'pending',  # an admin accepts the ticket
                'Rejected': 'rejected',  # admin rejects the ticket
                'SystemProcessing': 'pending',  # automatic processing; an unlikely status for a deposit
                'FullyProcessed': 'ok',  # the deposit has concluded
                'Failed': 'failed',  # the deposit has failed for some reason
                'Pending': 'pending',  # Account Provider has set status to pending
                'Confirmed': 'pending',  # Account Provider confirms the deposit
                'AmlProcessing': 'pending',  # anti-money-laundering process underway
                'AmlAccepted': 'pending',  # anti-money-laundering process successful
                'AmlRejected': 'rejected',  # deposit did not stand up to anti-money-laundering process
                'AmlFailed': 'failed',  # anti-money-laundering process failed/did not complete
                'LimitsAccepted': 'pending',  # deposit meets limits for fiat or crypto asset
                'LimitsRejected': 'rejected',  # deposit does not meet limits for fiat or crypto asset
            },
            'withdrawal': {
                'New': 'pending',  # awaiting operator review
                'AdminProcessing': 'pending',  # An admin is looking at the ticket
                'Accepted': 'pending',  # withdrawal will proceed
                'Rejected': 'rejected',  # admin or automatic rejection
                'SystemProcessing': 'pending',  # automatic processing underway
                'FullyProcessed': 'ok',  # the withdrawal has concluded
                'Failed': 'failed',  # the withdrawal failed for some reason
                'Pending': 'pending',  # the admin has placed the withdrawal in pending status
                'Pending2Fa': 'pending',  # user must click 2-factor authentication confirmation link
                'AutoAccepted': 'pending',  # withdrawal will be automatically processed
                'Delayed': 'pending',  # waiting for funds to be allocated for the withdrawal
                'UserCanceled': 'canceled',  # withdraw canceled by user or Superuser
                'AdminCanceled': 'canceled',  # withdraw canceled by Superuser
                'AmlProcessing': 'pending',  # anti-money-laundering process underway
                'AmlAccepted': 'pending',  # anti-money-laundering process complete
                'AmlRejected': 'rejected',  # withdrawal did not stand up to anti-money-laundering process
                'AmlFailed': 'failed',  # withdrawal did not complete anti-money-laundering process
                'LimitsAccepted': 'pending',  # withdrawal meets limits for fiat or crypto asset
                'LimitsRejected': 'rejected',  # withdrawal does not meet limits for fiat or crypto asset
                'Submitted': 'pending',  # withdrawal sent to Account Provider; awaiting blockchain confirmation
                'Confirmed': 'pending',  # Account Provider confirms that withdrawal is on the blockchain
                'ManuallyConfirmed': 'pending',  # admin has sent withdrawal via wallet or admin function directly; marks ticket; debits account
                'Confirmed2Fa': 'pending',  # user has confirmed withdraw via 2-factor authentication.
            },
        }
        statuses = self.safe_value(statusesByType, type, {})
        return self.safe_string(statuses, status, status)

    def parse_transaction(self, transaction: dict, currency: Currency = None) -> Transaction:
        #
        # fetchDeposits
        #
        #    {
        #        "OMSId": 1,
        #        "DepositId": 44,
        #        "AccountId": 449,
        #        "SubAccountId": 0,
        #        "ProductId": 4,
        #        "Amount": 200.00000000000000000000000000,
        #        "LastUpdateTimeStamp": 637431291261187806,
        #        "ProductType": "CryptoCurrency",
        #        "TicketStatus": "FullyProcessed",
        #        "DepositInfo": "{
        #            "AccountProviderId":42,
        #            "AccountProviderName":"USDT_BSC",
        #            "TXId":"0x3879b02632c69482646409e991149290bc9a58e4603be63c7c2c90a843f45d2b",
        #            "FromAddress":"******************************************",
        #            "ToAddress":"******************************************"
        #        }",
        #        "DepositCode": "ab0e23d5-a9ce-4d94-865f-9ab464fb1de3",
        #        "TicketNumber": 71,
        #        "NotionalProductId": 13,
        #        "NotionalValue": 200.00000000000000000000000000,
        #        "FeeAmount": 0.0000000000000000000000000000,
        #     }
        #
        # fetchWithdrawals
        #
        #     {
        #         "Amount": 0.0,
        #         "FeeAmount": 0.0,
        #         "NotionalValue": 0.0,
        #         "WithdrawId": 0,
        #         "AssetManagerId": 0,
        #         "AccountId": 0,
        #         "AssetId": 0,
        #         "TemplateForm": "{\"TemplateType\": \"TetherRPCWithdraw\",\"Comment\": \"TestWithdraw\",\"ExternalAddress\": \"ms6C3pKAAr8gRCcnVebs8VRkVrjcvqNYv3\"}",
        #         "TemplateFormType": "TetherRPCWithdraw",
        #         "omsId": 0,
        #         "TicketStatus": 0,
        #         "TicketNumber": 0,
        #         "WithdrawTransactionDetails": "",
        #         "WithdrawType": "",
        #         "WithdrawCode": "490b4fa3-53fc-44f4-bd29-7e16be86fba3",
        #         "AssetType": 0,
        #         "Reaccepted": True,
        #         "NotionalProductId": 0
        #     }
        #
        id = None
        currencyId = self.safe_string(transaction, 'ProductId')
        code = self.safe_currency_code(currencyId, currency)
        type = None
        if 'DepositId' in transaction:
            id = self.safe_string(transaction, 'DepositId')
            type = 'deposit'
        elif 'WithdrawId' in transaction:
            id = self.safe_string(transaction, 'WithdrawId')
            type = 'withdrawal'
        templateForm = self.parse_json(self.safe_value_2(transaction, 'TemplateForm', 'DepositInfo'))
        updated = self.safe_integer(transaction, 'LastUpdateTimeStamp')
        if templateForm is not None:
            updated = self.safe_integer(templateForm, 'LastUpdated', updated)
        address = self.safe_string_2(templateForm, 'ExternalAddress', 'ToAddress')
        timestamp = self.safe_integer(templateForm, 'TimeSubmitted')
        feeCost = self.safe_number(transaction, 'FeeAmount')
        transactionStatus = self.safe_string(transaction, 'TicketStatus')
        fee = None
        if feeCost is not None:
            fee = {'currency': code, 'cost': feeCost}
        return {
            'info': transaction,
            'id': id,
            'txid': self.safe_string_2(templateForm, 'TxId', 'TXId'),
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'address': address,
            'addressTo': address,
            'addressFrom': self.safe_string(templateForm, 'FromAddress'),
            'tag': None,
            'tagTo': None,
            'tagFrom': None,
            'type': type,
            'amount': self.safe_number(transaction, 'Amount'),
            'currency': code,
            'status': self.parse_transaction_status_by_type(transactionStatus, type),
            'updated': updated,
            'fee': fee,
            'internal': None,
            'comment': None,
            'network': None,
        }

    def withdraw(self, code: str, amount: float, address: str, tag=None, params={}) -> Transaction:
        """
        make a withdrawal
        :param str code: unified currency code
        :param float amount: the amount to withdraw
        :param str address: the address to withdraw to
        :param str tag:
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `transaction structure <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        tag, params = self.handle_withdraw_tag_and_params(tag, params)
        # self method required login, password and twofa key
        sessionToken = self.safe_string(self.options, 'sessionToken')
        if sessionToken is None:
            raise AuthenticationError(self.id + ' call signIn() method to obtain a session token')
        if self.twofa is None:
            raise AuthenticationError(self.id + ' withdraw() requires exchange.twofa credentials')
        self.check_address(address)
        omsId = self.safe_integer(self.options, 'omsId', 1)
        self.load_markets()
        self.load_accounts()
        defaultAccountId = self.safe_integer_2(self.options, 'accountId', 'AccountId', int(self.accounts[0]['id']))
        accountId = self.safe_integer_2(params, 'accountId', 'AccountId', defaultAccountId)
        params = self.omit(params, ['accountId', 'AccountId'])
        currency = self.currency(code)
        withdrawTemplateTypesRequest: dict = {
            'omsId': omsId,
            'AccountId': accountId,
            'ProductId': currency['id'],
        }
        withdrawTemplateTypesResponse = self.privateGetGetWithdrawTemplateTypes(withdrawTemplateTypesRequest)
        #
        #     {
        #         "result": True,
        #         "errormsg": null,
        #         "statuscode": "0",
        #         "TemplateTypes": [
        #             {AccountProviderId: "14", TemplateName: "ToExternalBitcoinAddress", AccountProviderName: "BitgoRPC-BTC"},
        #             {AccountProviderId: "20", TemplateName: "ToExternalBitcoinAddress", AccountProviderName: "TrezorBTC"},
        #             {AccountProviderId: "31", TemplateName: "BTC", AccountProviderName: "BTC Fireblocks 1"}
        #         ]
        #     }
        #
        templateTypes = self.safe_value(withdrawTemplateTypesResponse, 'TemplateTypes', [])
        firstTemplateType = self.safe_value(templateTypes, 0)
        if firstTemplateType is None:
            raise ExchangeError(self.id + ' withdraw() could not find a withdraw template type for ' + currency['code'])
        templateName = self.safe_string(firstTemplateType, 'TemplateName')
        withdrawTemplateRequest: dict = {
            'omsId': omsId,
            'AccountId': accountId,
            'ProductId': currency['id'],
            'TemplateType': templateName,
            'AccountProviderId': firstTemplateType['AccountProviderId'],
        }
        withdrawTemplateResponse = self.privateGetGetWithdrawTemplate(withdrawTemplateRequest)
        #
        #     {
        #         "result": True,
        #         "errormsg": null,
        #         "statuscode": "0",
        #         "Template": "{\"TemplateType\":\"ToExternalBitcoinAddress\",\"Comment\":\"\",\"ExternalAddress\":\"\"}"
        #     }
        #
        template = self.safe_string(withdrawTemplateResponse, 'Template')
        if template is None:
            raise ExchangeError(self.id + ' withdraw() could not find a withdraw template for ' + currency['code'])
        withdrawTemplate = json.loads(template)
        withdrawTemplate['ExternalAddress'] = address
        if tag is not None:
            if 'Memo' in withdrawTemplate:
                withdrawTemplate['Memo'] = tag
        withdrawPayload: dict = {
            'omsId': omsId,
            'AccountId': accountId,
            'ProductId': currency['id'],
            'TemplateForm': self.json(withdrawTemplate),
            'TemplateType': templateName,
        }
        withdrawRequest: dict = {
            'TfaType': 'Google',
            'TFaCode': self.totp(self.twofa),
            'Payload': self.json(withdrawPayload),
        }
        response = self.privatePostCreateWithdrawTicket(self.deep_extend(withdrawRequest, params))
        return self.parse_transaction(response, currency)

    def nonce(self):
        return self.milliseconds()

    def sign(self, path, api='public', method='GET', params={}, headers=None, body=None):
        url = self.urls['api'][api] + '/' + self.implode_params(path, params)
        query = self.omit(params, self.extract_params(path))
        if api == 'public':
            if path == 'Authenticate':
                auth = self.login + ':' + self.password
                auth64 = self.string_to_base64(auth)
                headers = {
                    'Authorization': 'Basic ' + auth64,
                    # 'Content-Type': 'application/json',
                }
            elif path == 'Authenticate2FA':
                pending2faToken = self.safe_string(self.options, 'pending2faToken')
                if pending2faToken is not None:
                    headers = {
                        'Pending2FaToken': pending2faToken,
                        # 'Content-Type': 'application/json',
                    }
                    query = self.omit(query, 'pending2faToken')
            if query:
                url += '?' + self.urlencode(query)
        elif api == 'private':
            self.check_required_credentials()
            sessionToken = self.safe_string(self.options, 'sessionToken')
            if sessionToken is None:
                nonce = str(self.nonce())
                auth = nonce + self.uid + self.apiKey
                signature = self.hmac(self.encode(auth), self.encode(self.secret), hashlib.sha256)
                headers = {
                    'Nonce': nonce,
                    'APIKey': self.apiKey,
                    'Signature': signature,
                    'UserId': self.uid,
                }
            else:
                headers = {
                    'APToken': sessionToken,
                }
            if method == 'POST':
                headers['Content-Type'] = 'application/json'
                body = self.json(query)
            else:
                if query:
                    url += '?' + self.urlencode(query)
        return {'url': url, 'method': method, 'body': body, 'headers': headers}

    def handle_errors(self, code: int, reason: str, url: str, method: str, headers: dict, body: str, response, requestHeaders, requestBody):
        if code == 404:
            raise AuthenticationError(self.id + ' ' + body)
        if response is None:
            return None
        #
        #     {"status":"Rejected","errormsg":"Not_Enough_Funds","errorcode":101}
        #     {"result":false,"errormsg":"Server Error","errorcode":102,"detail":null}
        #
        message = self.safe_string(response, 'errormsg')
        if (message is not None) and (message != ''):
            feedback = self.id + ' ' + body
            self.throw_exactly_matched_exception(self.exceptions['exact'], message, feedback)
            self.throw_broadly_matched_exception(self.exceptions['broad'], body, feedback)
            raise ExchangeError(feedback)
        return None
