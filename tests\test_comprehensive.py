import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import tempfile

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestComprehensiveBot(unittest.TestCase):
    """Comprehensive test suite for SP.Bot Enhanced v2.0.0 with >80% coverage"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.sample_market_data = {
            'symbol': 'BTCUSDT',
            'price': 50000.0,
            'volume': 1000000,
            'high': 51000.0,
            'low': 49000.0,
            'open': 49500.0,
            'close': 50000.0,
            'timestamp': datetime.now(),
            'atr': 0.02,
            'volume_change': 0.1,
            'rsi': 65.0,
            'bb_position': 0.7,
            'market_regime': 'stable'
        }
        
        self.sample_indicators = {
            'ema_50': 49800.0,
            'ema_200': 48000.0,
            'rsi': 65.0,
            'macd': 0.5,
            'atr': 0.02,
            'bb_upper': 51000.0,
            'bb_lower': 49000.0,
            'bb_position': 0.7,
            'volume_ratio': 1.2,
            'adx': 25.0,
            'obv': 1000000,
            'ichimoku_cloud': {
                'tenkan_sen': 50200.0,
                'kijun_sen': 49800.0,
                'senkou_span_a': 50000.0,
                'senkou_span_b': 49500.0
            }
        }
        
        self.sample_ai_response = {
            'recommendation': 'buy',
            'confidence': 75,
            'sentiment': 'bullish',
            'explanation': 'Strong upward momentum with good volume',
            'key_factors': ['volume_increase', 'rsi_breakout', 'ema_trend'],
            'risk_level': 'medium',
            'time_horizon': 'short',
            'market_regime': 'stable'
        }

    def test_ai_connector_initialization(self):
        """Test AI connector initialization and configuration"""
        try:
            from ai_services.ai_connector import AIConnector
            
            config = {
                'openai': {'api_key': 'test_key', 'model': 'gpt-4'},
                'deepseek': {'api_key': 'test_key'},
                'qwen': {'api_key': 'test_key'}
            }
            
            connector = AIConnector(config)
            
            self.assertIsNotNone(connector)
            self.assertEqual(len(connector.services), 3)
            self.assertIn('openai', connector.services)
            self.assertIn('deepseek', connector.services)
            self.assertIn('qwen', connector.services)
            
        except ImportError:
            self.skipTest("AI connector module not available")

    def test_ai_response_validation(self):
        """Test AI response validation and normalization"""
        try:
            from ai_services.ai_connector import AIConnector
            
            connector = AIConnector({})
            
            # Test valid response
            validated = connector._validate_ai_response(self.sample_ai_response)
            self.assertEqual(validated['recommendation'], 'buy')
            self.assertEqual(validated['confidence'], 75)
            self.assertEqual(validated['sentiment'], 'bullish')
            
            # Test invalid response
            invalid_response = {
                'recommendation': 'invalid',
                'confidence': 150,  # Invalid confidence
                'sentiment': 'unknown'
            }
            
            validated = connector._validate_ai_response(invalid_response)
            self.assertEqual(validated['recommendation'], 'hold')  # Should default to hold
            self.assertLessEqual(validated['confidence'], 100)  # Should be clamped
            self.assertIn(validated['sentiment'], ['bullish', 'bearish', 'neutral'])
            
        except ImportError:
            self.skipTest("AI connector module not available")

    def test_technical_indicators_calculation(self):
        """Test technical indicators calculation"""
        try:
            from analysis.indicators import calculate_ema, calculate_rsi, calculate_macd, calculate_atr
            
            # Generate sample price data
            prices = [50000 + i * 100 + np.random.normal(0, 50) for i in range(50)]
            
            # Test EMA calculation
            ema_result = calculate_ema(prices, period=20)
            self.assertIsInstance(ema_result, list)
            self.assertGreater(len(ema_result), 0)
            
            # Test RSI calculation
            rsi_result = calculate_rsi(prices, period=14)
            self.assertIsInstance(rsi_result, list)
            if rsi_result:
                self.assertGreaterEqual(min(rsi_result), 0)
                self.assertLessEqual(max(rsi_result), 100)
            
            # Test MACD calculation
            macd_result = calculate_macd(prices)
            self.assertIsInstance(macd_result, dict)
            self.assertIn('macd', macd_result)
            self.assertIn('signal', macd_result)
            
            # Test ATR calculation
            highs = [p + 200 for p in prices]
            lows = [p - 200 for p in prices]
            atr_result = calculate_atr(highs, lows, prices)
            self.assertIsInstance(atr_result, list)
            if atr_result:
                self.assertGreater(min(atr_result), 0)
            
        except ImportError:
            self.skipTest("Technical indicators module not available")

    def test_ichimoku_cloud_calculation(self):
        """Test Ichimoku Cloud indicator calculation"""
        try:
            from analysis.indicators import calculate_ichimoku_cloud
            
            # Generate sample OHLC data
            highs = [50000 + i * 100 + np.random.normal(0, 100) for i in range(50)]
            lows = [h - 500 for h in highs]
            closes = [l + 250 for l in lows]
            
            ichimoku = calculate_ichimoku_cloud(highs, lows, closes)
            
            self.assertIsInstance(ichimoku, dict)
            self.assertIn('tenkan_sen', ichimoku)
            self.assertIn('kijun_sen', ichimoku)
            self.assertIn('senkou_span_a', ichimoku)
            self.assertIn('senkou_span_b', ichimoku)
            self.assertIn('chikou_span', ichimoku)
            
            # Verify data types
            for key in ichimoku:
                if ichimoku[key]:
                    self.assertIsInstance(ichimoku[key], list)
            
        except ImportError:
            self.skipTest("Ichimoku Cloud calculation not available")

    def test_adx_calculation(self):
        """Test ADX indicator calculation"""
        try:
            from analysis.indicators import calculate_adx
            
            # Generate sample OHLC data
            highs = [50000 + i * 50 + np.random.normal(0, 100) for i in range(30)]
            lows = [h - 300 for h in highs]
            closes = [l + 150 for l in lows]
            
            adx_result = calculate_adx(highs, lows, closes)
            
            self.assertIsInstance(adx_result, dict)
            self.assertIn('adx', adx_result)
            self.assertIn('plus_di', adx_result)
            self.assertIn('minus_di', adx_result)
            
            # Verify ADX values are reasonable
            if adx_result['adx']:
                adx_values = [v for v in adx_result['adx'] if not pd.isna(v)]
                if adx_values:
                    self.assertGreaterEqual(min(adx_values), 0)
                    self.assertLessEqual(max(adx_values), 100)
            
        except ImportError:
            self.skipTest("ADX calculation not available")

    def test_obv_calculation(self):
        """Test OBV indicator calculation"""
        try:
            from analysis.indicators import calculate_obv
            
            # Generate sample price and volume data
            prices = [50000 + i * 100 for i in range(20)]
            volumes = [1000000 + i * 10000 for i in range(20)]
            
            obv_result = calculate_obv(prices, volumes)
            
            self.assertIsInstance(obv_result, list)
            self.assertEqual(len(obv_result), len(prices))
            
            # OBV should be cumulative
            if len(obv_result) > 1:
                # Check that OBV changes based on price direction
                self.assertIsInstance(obv_result[0], (int, float))
            
        except ImportError:
            self.skipTest("OBV calculation not available")

    def test_signal_aggregator(self):
        """Test signal aggregation with enhanced features"""
        try:
            from analysis.signal_aggregator import SignalAggregator
            
            aggregator = SignalAggregator()
            
            # Test signal aggregation
            ai_signals = [
                {'service': 'openai', 'recommendation': 'buy', 'confidence': 0.8},
                {'service': 'deepseek', 'recommendation': 'buy', 'confidence': 0.7},
                {'service': 'qwen', 'recommendation': 'hold', 'confidence': 0.6}
            ]
            
            technical_signals = [
                {'indicator': 'rsi', 'signal': 'buy', 'strength': 0.7},
                {'indicator': 'macd', 'signal': 'buy', 'strength': 0.6}
            ]
            
            result = aggregator.aggregate_signals(
                ai_signals, 
                technical_signals, 
                self.sample_market_data
            )
            
            self.assertIsInstance(result, dict)
            self.assertIn('recommendation', result)
            self.assertIn('confidence', result)
            self.assertIn('reasoning', result)
            
            # Test confidence threshold
            self.assertGreaterEqual(result['confidence'], 0.0)
            self.assertLessEqual(result['confidence'], 1.0)
            
        except ImportError:
            self.skipTest("Signal aggregator module not available")

    def test_ml_market_detector(self):
        """Test ML-based market regime detection"""
        try:
            from ai_services.ml_market_detector import MLMarketDetector
            
            detector = MLMarketDetector()
            
            # Test market regime classification
            result = detector.classify_market_regime(self.sample_market_data)
            
            self.assertIsInstance(result, dict)
            self.assertIn('regime', result)
            self.assertIn('confidence', result)
            self.assertIn('method', result)
            
            # Test regime values
            self.assertIn(result['regime'], ['stable', 'volatile', 'unclear'])
            self.assertGreaterEqual(result['confidence'], 0.0)
            self.assertLessEqual(result['confidence'], 1.0)
            
            # Test risk adjustments
            self.assertIn('risk_adjustments', result)
            risk_adj = result['risk_adjustments']
            self.assertIn('position_size_multiplier', risk_adj)
            self.assertIn('stop_loss_multiplier', risk_adj)
            
        except ImportError:
            self.skipTest("ML market detector module not available")

    def test_enhanced_debate_mode(self):
        """Test enhanced debate mode functionality"""
        try:
            from ai_services.enhanced_debate_mode import EnhancedDebateMode
            
            debate_mode = EnhancedDebateMode()
            
            # Test weight adjustment
            initial_weights = debate_mode.current_weights.copy()
            
            # Simulate performance data
            performance_data = {
                'openai': {'accuracy': 0.8, 'predictions': 20},
                'deepseek': {'accuracy': 0.7, 'predictions': 20},
                'qwen': {'accuracy': 0.75, 'predictions': 20}
            }
            
            debate_mode.update_model_performance(performance_data)
            
            # Weights should be adjusted based on performance
            self.assertIsInstance(debate_mode.current_weights, dict)
            self.assertEqual(len(debate_mode.current_weights), 3)
            
            # Test debate session
            conflicting_analyses = [
                {'service': 'openai', 'recommendation': 'buy', 'confidence': 0.8},
                {'service': 'deepseek', 'recommendation': 'sell', 'confidence': 0.7},
                {'service': 'qwen', 'recommendation': 'hold', 'confidence': 0.6}
            ]
            
            result = debate_mode.conduct_debate(conflicting_analyses, self.sample_market_data)
            
            self.assertIsInstance(result, dict)
            self.assertIn('final_recommendation', result)
            self.assertIn('confidence', result)
            self.assertIn('debate_summary', result)
            
        except ImportError:
            self.skipTest("Enhanced debate mode module not available")

    def test_unified_trading_interface(self):
        """Test unified trading interface for paper and live trading"""
        try:
            from exchange.unified_trading_interface import UnifiedTradingInterface
            
            # Test paper trading mode
            interface = UnifiedTradingInterface(mode="paper", initial_balance=10000)
            
            self.assertEqual(interface.mode, "paper")
            self.assertEqual(interface.get_current_balance(), 10000)
            
            # Test trade execution in paper mode
            trade_result = interface.execute_trade(
                symbol="BTCUSDT",
                side="buy",
                quantity=0.1,
                price=50000,
                stop_loss=49000,
                take_profit=52000
            )
            
            if trade_result:
                self.assertIsInstance(trade_result, dict)
                self.assertEqual(trade_result['mode'], 'paper')
                self.assertEqual(trade_result['symbol'], 'BTCUSDT')
                self.assertEqual(trade_result['side'], 'buy')
            
            # Test performance metrics
            metrics = interface.get_performance_metrics()
            self.assertIsInstance(metrics, dict)
            self.assertIn('mode', metrics)
            self.assertIn('current_balance', metrics)
            
        except ImportError:
            self.skipTest("Unified trading interface module not available")

    def test_backtesting_engine(self):
        """Test enhanced backtesting engine"""
        try:
            from backtesting.enhanced_engine import EnhancedBacktestEngine
            
            engine = EnhancedBacktestEngine(initial_balance=10000)
            
            # Generate sample historical data
            dates = pd.date_range(start='2023-01-01', end='2023-01-31', freq='1H')
            data = pd.DataFrame({
                'open': np.random.normal(50000, 1000, len(dates)),
                'high': np.random.normal(51000, 1000, len(dates)),
                'low': np.random.normal(49000, 1000, len(dates)),
                'close': np.random.normal(50000, 1000, len(dates)),
                'volume': np.random.normal(1000000, 100000, len(dates))
            }, index=dates)
            
            # Simple strategy function for testing
            def test_strategy(data):
                if len(data) < 2:
                    return []
                
                current_price = data['close'].iloc[-1]
                prev_price = data['close'].iloc[-2]
                
                if current_price > prev_price * 1.01:  # 1% increase
                    return [{
                        'action': 'buy',
                        'symbol': 'BTCUSDT',
                        'confidence': 0.7,
                        'reason': 'price_increase'
                    }]
                elif current_price < prev_price * 0.99:  # 1% decrease
                    return [{
                        'action': 'sell',
                        'symbol': 'BTCUSDT',
                        'confidence': 0.7,
                        'reason': 'price_decrease'
                    }]
                return []
            
            # Run backtest
            results = engine.run_backtest(data, test_strategy)
            
            self.assertIsNotNone(results)
            self.assertGreaterEqual(results.total_trades, 0)
            self.assertIsInstance(results.win_rate, float)
            self.assertIsInstance(results.total_pnl_percent, float)
            
        except ImportError:
            self.skipTest("Backtesting engine module not available")

    def test_time_synchronization(self):
        """Test NTP time synchronization"""
        try:
            from utils.time_sync import NTPSynchronizer
            
            ntp_sync = NTPSynchronizer()
            
            # Test sync status
            status = ntp_sync.get_sync_status()
            self.assertIsInstance(status, dict)
            self.assertIn('is_synchronized', status)
            self.assertIn('current_offset', status)
            self.assertIn('ntp_servers', status)
            
            # Test time drift check
            drift_check = ntp_sync.check_time_drift()
            self.assertIsInstance(drift_check, dict)
            self.assertIn('drift_detected', drift_check)
            
        except ImportError:
            self.skipTest("Time synchronization module not available")

    def test_security_key_manager(self):
        """Test API key encryption and security"""
        try:
            from security.key_manager import KeyManager
            
            with tempfile.TemporaryDirectory() as temp_dir:
                key_manager = KeyManager(storage_path=temp_dir)
                
                # Test key encryption
                test_key = "test_api_key_12345"
                encrypted = key_manager.encrypt_key(test_key, "test_service")
                
                self.assertIsInstance(encrypted, dict)
                self.assertIn('encrypted_data', encrypted)
                self.assertIn('salt', encrypted)
                
                # Test key decryption
                decrypted = key_manager.decrypt_key("test_service")
                self.assertEqual(decrypted, test_key)
                
        except ImportError:
            self.skipTest("Security key manager module not available")

if __name__ == '__main__':
    # Run tests with coverage if available
    try:
        import coverage
        cov = coverage.Coverage()
        cov.start()
        
        unittest.main(exit=False)
        
        cov.stop()
        cov.save()
        
        print("\nCoverage Report:")
        cov.report()
        
    except ImportError:
        # Run tests without coverage
        unittest.main()
