info = {
    "name": "lv",
    "date_order": "DMY",
    "january": [
        "janv",
        "janvāris"
    ],
    "february": [
        "febr",
        "februāris"
    ],
    "march": [
        "marts"
    ],
    "april": [
        "apr",
        "aprīlis"
    ],
    "may": [
        "maijs"
    ],
    "june": [
        "jūn",
        "jūnijs"
    ],
    "july": [
        "jūl",
        "jūlijs"
    ],
    "august": [
        "aug",
        "augusts"
    ],
    "september": [
        "sept",
        "septembris"
    ],
    "october": [
        "okt",
        "oktobris"
    ],
    "november": [
        "nov",
        "novembris"
    ],
    "december": [
        "dec",
        "decembris"
    ],
    "monday": [
        "pirmd",
        "pirmdiena"
    ],
    "tuesday": [
        "otrd",
        "otrdiena"
    ],
    "wednesday": [
        "trešd",
        "trešdiena"
    ],
    "thursday": [
        "ceturtd",
        "ceturtdiena"
    ],
    "friday": [
        "piektd",
        "piektdiena"
    ],
    "saturday": [
        "sestd",
        "sestdiena"
    ],
    "sunday": [
        "svētd",
        "svētdiena"
    ],
    "am": [
        "priekšp",
        "priekšpusdiena",
        "priekšpusdienā"
    ],
    "pm": [
        "pēcp",
        "pēcpusd",
        "pēcpusdiena",
        "pēcpusdienā"
    ],
    "year": [
        "g",
        "gads"
    ],
    "month": [
        "mēn",
        "mēnesis"
    ],
    "week": [
        "ned",
        "nedēļa"
    ],
    "day": [
        "d",
        "diena"
    ],
    "hour": [
        "h",
        "st",
        "stundas"
    ],
    "minute": [
        "min",
        "minūtes"
    ],
    "second": [
        "s",
        "sek",
        "sekundes"
    ],
    "relative-type": {
        "0 day ago": [
            "šodien"
        ],
        "0 hour ago": [
            "šajā stundā"
        ],
        "0 minute ago": [
            "šajā minūtē"
        ],
        "0 month ago": [
            "šajā mēnesī"
        ],
        "0 second ago": [
            "tagad"
        ],
        "0 week ago": [
            "šajā nedēļā"
        ],
        "0 year ago": [
            "šajā gadā"
        ],
        "1 day ago": [
            "vakar"
        ],
        "1 month ago": [
            "pagājušajā mēnesī"
        ],
        "1 week ago": [
            "pagājušajā nedēļā"
        ],
        "1 year ago": [
            "pagājušajā gadā"
        ],
        "in 1 day": [
            "rīt"
        ],
        "in 1 month": [
            "nākamajā mēnesī"
        ],
        "in 1 week": [
            "nākamajā nedēļā"
        ],
        "in 1 year": [
            "nākamajā gadā"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "pirms (\\d+[.,]?\\d*) d",
            "pirms (\\d+[.,]?\\d*) dienas",
            "pirms (\\d+[.,]?\\d*) dienām"
        ],
        "\\1 hour ago": [
            "pirms (\\d+[.,]?\\d*) h",
            "pirms (\\d+[.,]?\\d*) st",
            "pirms (\\d+[.,]?\\d*) stundas",
            "pirms (\\d+[.,]?\\d*) stundām"
        ],
        "\\1 minute ago": [
            "pirms (\\d+[.,]?\\d*) min",
            "pirms (\\d+[.,]?\\d*) minūtes",
            "pirms (\\d+[.,]?\\d*) minūtēm"
        ],
        "\\1 month ago": [
            "pirms (\\d+[.,]?\\d*) mēn",
            "pirms (\\d+[.,]?\\d*) mēneša",
            "pirms (\\d+[.,]?\\d*) mēnešiem"
        ],
        "\\1 second ago": [
            "pirms (\\d+[.,]?\\d*) s",
            "pirms (\\d+[.,]?\\d*) sek",
            "pirms (\\d+[.,]?\\d*) sekundes",
            "pirms (\\d+[.,]?\\d*) sekundēm"
        ],
        "\\1 week ago": [
            "pirms (\\d+[.,]?\\d*) ned",
            "pirms (\\d+[.,]?\\d*) nedēļas",
            "pirms (\\d+[.,]?\\d*) nedēļām"
        ],
        "\\1 year ago": [
            "pirms (\\d+[.,]?\\d*) g",
            "pirms (\\d+[.,]?\\d*) gada",
            "pirms (\\d+[.,]?\\d*) gadiem"
        ],
        "in \\1 day": [
            "pēc (\\d+[.,]?\\d*) d",
            "pēc (\\d+[.,]?\\d*) dienas",
            "pēc (\\d+[.,]?\\d*) dienām"
        ],
        "in \\1 hour": [
            "pēc (\\d+[.,]?\\d*) h",
            "pēc (\\d+[.,]?\\d*) st",
            "pēc (\\d+[.,]?\\d*) stundas",
            "pēc (\\d+[.,]?\\d*) stundām"
        ],
        "in \\1 minute": [
            "pēc (\\d+[.,]?\\d*) min",
            "pēc (\\d+[.,]?\\d*) minūtes",
            "pēc (\\d+[.,]?\\d*) minūtēm"
        ],
        "in \\1 month": [
            "pēc (\\d+[.,]?\\d*) mēn",
            "pēc (\\d+[.,]?\\d*) mēneša",
            "pēc (\\d+[.,]?\\d*) mēnešiem"
        ],
        "in \\1 second": [
            "pēc (\\d+[.,]?\\d*) s",
            "pēc (\\d+[.,]?\\d*) sek",
            "pēc (\\d+[.,]?\\d*) sekundes",
            "pēc (\\d+[.,]?\\d*) sekundēm"
        ],
        "in \\1 week": [
            "pēc (\\d+[.,]?\\d*) ned",
            "pēc (\\d+[.,]?\\d*) nedēļas",
            "pēc (\\d+[.,]?\\d*) nedēļām"
        ],
        "in \\1 year": [
            "pēc (\\d+[.,]?\\d*) g",
            "pēc (\\d+[.,]?\\d*) gada",
            "pēc (\\d+[.,]?\\d*) gadiem"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
