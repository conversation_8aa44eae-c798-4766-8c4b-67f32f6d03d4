"""
Fix dateutil import issue
"""

import sys
import os

# Add site-packages to path
site_packages = os.path.join(os.path.dirname(sys.executable), 'Lib', 'site-packages')
if site_packages not in sys.path:
    sys.path.append(site_packages)

# Try to import dateutil
try:
    import dateutil
    print(f"Successfully imported dateutil from {dateutil.__file__}")
except ImportError as e:
    print(f"Failed to import dateutil: {e}")
    
    # Try to install dateutil
    import subprocess
    print("Attempting to install dateutil...")
    subprocess.call([sys.executable, "-m", "pip", "install", "python-dateutil"])
    
    # Try to import again
    try:
        import dateutil
        print(f"Successfully imported dateutil after installation from {dateutil.__file__}")
    except ImportError as e:
        print(f"Still failed to import dateutil: {e}")
