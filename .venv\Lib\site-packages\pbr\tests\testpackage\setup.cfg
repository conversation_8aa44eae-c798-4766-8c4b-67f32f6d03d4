[metadata]
name = pbr_testpackage
# TODO(lifeless) we should inject this as needed otherwise we're not truely
# testing postversioned codepaths.
version = 0.1.dev
author = OpenStack
author_email = <EMAIL>
home_page = http://pypi.python.org/pypi/pbr
project_urls =
    Bug Tracker = https://bugs.launchpad.net/pbr/
    Documentation = https://docs.openstack.org/pbr/
    Source Code = https://opendev.org/openstack/pbr
summary = Test package for testing pbr
description_file =
    README.txt
    CHANGES.txt
description_content_type = text/plain; charset=UTF-8
python_requires = >=2.5

requires_dist =
    setuptools

classifier =
    Development Status :: 3 - Alpha
    Intended Audience :: Developers
    License :: OSI Approved :: BSD License
    Programming Language :: Python
    Topic :: Scientific/Engineering
    Topic :: Software Development :: Build Tools
    Topic :: Software Development :: Libraries :: Python Modules
    Topic :: System :: Archiving :: Packaging

keywords = packaging, distutils, setuptools

[files]
packages = pbr_testpackage
package_data = testpackage = package_data/*.txt
data_files = testpackage/data_files = data_files/*
extra_files = extra-file.txt

[entry_points]
console_scripts =
    pbr_test_cmd = pbr_testpackage.cmd:main
    pbr_test_cmd_with_class = pbr_testpackage.cmd:Foo.bar

wsgi_scripts =
    pbr_test_wsgi = pbr_testpackage.wsgi:main
    pbr_test_wsgi_with_class = pbr_testpackage.wsgi:WSGI.app

[extension=pbr_testpackage.testext]
sources = src/testext.c
optional = True

[global]
#setup_hooks =
#    pbr_testpackage._setup_hooks.test_hook_1
#    pbr_testpackage._setup_hooks.test_hook_2
commands = pbr_testpackage._setup_hooks.test_command
