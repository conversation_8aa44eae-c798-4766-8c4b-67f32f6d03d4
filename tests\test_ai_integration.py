#!/usr/bin/env python3
"""
Test suite for AI Integration System
Tests the real API integration for OpenAI, DeepSeek, and Qwen
"""

import unittest
import os
import sys
import json
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_services.ai_connector import MultiAIConnector
from ai_services.ai_service_manager import AIServiceManager


class TestAIIntegration(unittest.TestCase):
    """Test AI integration with real API calls"""
    
    def setUp(self):
        """Set up test environment"""
        self.service_manager = AIServiceManager()
        self.ai_connector = MultiAIConnector()
        
        # Sample market data for testing
        self.sample_market_data = {
            "symbol": "BTC/USDT",
            "current_price": 45000.0,
            "price_change_24h": 2.5,
            "volume_24h": 1000000,
            "indicators": {
                "ema50": 44500.0,
                "ema200": 43000.0,
                "rsi": 65.0,
                "macd": 150.0,
                "atr": 800.0,
                "bollinger_upper": 46000.0,
                "bollinger_lower": 44000.0,
                "is_uptrend": True,
                "is_downtrend": False
            }
        }
    
    def test_openai_api_integration(self):
        """Test OpenAI API integration"""
        # Mock account
        mock_account = Mock()
        mock_account.get_api_key.return_value = "test_openai_key"
        mock_account.account_id = "openai_test_1"
        mock_account.mark_used = Mock()
        
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "recommendation": "buy",
            "confidence": 75,
            "sentiment": "bullish",
            "explanation": "Strong upward momentum with good volume",
            "key_factors": ["momentum", "volume", "trend"]
        })
        mock_response.usage.total_tokens = 350
        
        with patch('openai.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_openai.return_value = mock_client
            mock_client.chat.completions.create.return_value = mock_response
            
            # Test API call
            result = self.ai_connector._call_openai_api(
                mock_account, 
                self.ai_connector._format_market_data(self.sample_market_data)
            )
            
            # Verify result
            self.assertIsNotNone(result)
            self.assertEqual(result["service"], "openai")
            self.assertEqual(result["analysis"]["recommendation"], "buy")
            self.assertEqual(result["analysis"]["confidence"], 75)
            self.assertEqual(result["analysis"]["sentiment"], "bullish")
            
            # Verify API was called correctly
            mock_client.chat.completions.create.assert_called_once()
            mock_account.mark_used.assert_called_once_with(tokens_used=350)
    
    def test_deepseek_api_integration(self):
        """Test DeepSeek API integration"""
        # Mock account
        mock_account = Mock()
        mock_account.get_api_key.return_value = "test_deepseek_key"
        mock_account.account_id = "deepseek_test_1"
        mock_account.mark_used = Mock()
        
        # Mock DeepSeek response
        mock_response = Mock()
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": json.dumps({
                        "recommendation": "sell",
                        "confidence": 80,
                        "sentiment": "bearish",
                        "explanation": "Overbought conditions detected",
                        "key_factors": ["overbought", "resistance", "volume_decline"]
                    })
                }
            }],
            "usage": {"total_tokens": 420}
        }
        mock_response.raise_for_status = Mock()
        
        with patch('requests.post') as mock_post:
            mock_post.return_value = mock_response
            
            # Test API call
            result = self.ai_connector._call_deepseek_api(
                mock_account,
                self.ai_connector._format_market_data(self.sample_market_data)
            )
            
            # Verify result
            self.assertIsNotNone(result)
            self.assertEqual(result["service"], "deepseek")
            self.assertEqual(result["analysis"]["recommendation"], "sell")
            self.assertEqual(result["analysis"]["confidence"], 80)
            self.assertEqual(result["analysis"]["sentiment"], "bearish")
            
            # Verify API was called correctly
            mock_post.assert_called_once()
            mock_account.mark_used.assert_called_once_with(tokens_used=420)
    
    def test_qwen_api_integration(self):
        """Test Qwen API integration"""
        # Mock account
        mock_account = Mock()
        mock_account.get_api_key.return_value = "test_qwen_key"
        mock_account.account_id = "qwen_test_1"
        mock_account.mark_used = Mock()
        
        # Mock Qwen response
        mock_response = Mock()
        mock_response.json.return_value = {
            "output": {
                "choices": [{
                    "message": {
                        "content": json.dumps({
                            "recommendation": "hold",
                            "confidence": 60,
                            "sentiment": "neutral",
                            "explanation": "Mixed signals in current market",
                            "key_factors": ["mixed_signals", "consolidation", "uncertainty"]
                        })
                    }
                }]
            },
            "usage": {"total_tokens": 380}
        }
        mock_response.raise_for_status = Mock()
        
        with patch('requests.post') as mock_post:
            mock_post.return_value = mock_response
            
            # Test API call
            result = self.ai_connector._call_qwen_api(
                mock_account,
                self.ai_connector._format_market_data(self.sample_market_data)
            )
            
            # Verify result
            self.assertIsNotNone(result)
            self.assertEqual(result["service"], "qwen")
            self.assertEqual(result["analysis"]["recommendation"], "hold")
            self.assertEqual(result["analysis"]["confidence"], 60)
            self.assertEqual(result["analysis"]["sentiment"], "neutral")
            
            # Verify API was called correctly
            mock_post.assert_called_once()
            mock_account.mark_used.assert_called_once_with(tokens_used=380)
    
    def test_api_error_handling(self):
        """Test API error handling and fallback"""
        # Mock account with no API key
        mock_account = Mock()
        mock_account.get_api_key.return_value = None
        mock_account.account_id = "test_account"
        
        # Test OpenAI fallback
        result = self.ai_connector._call_openai_api(mock_account, "test_data")
        self.assertIsNotNone(result)
        self.assertEqual(result["service"], "openai")
        self.assertEqual(result["account_id"], "fallback")
        self.assertEqual(result["analysis"]["recommendation"], "hold")
        self.assertEqual(result["analysis"]["confidence"], 30)
    
    def test_response_validation(self):
        """Test AI response validation"""
        # Test valid response
        valid_response = {
            "recommendation": "buy",
            "confidence": 75,
            "sentiment": "bullish",
            "explanation": "Good signals",
            "key_factors": ["momentum", "volume"]
        }
        
        validated = self.ai_connector._validate_ai_response(valid_response)
        self.assertEqual(validated["recommendation"], "buy")
        self.assertEqual(validated["confidence"], 75)
        
        # Test invalid response
        invalid_response = {
            "recommendation": "invalid_action",
            "confidence": 150,  # Invalid confidence
            "sentiment": "unknown",
            "explanation": None
        }
        
        validated = self.ai_connector._validate_ai_response(invalid_response)
        self.assertEqual(validated["recommendation"], "hold")  # Should default to hold
        self.assertEqual(validated["confidence"], 50)  # Should default to 50
        self.assertEqual(validated["sentiment"], "neutral")  # Should default to neutral
    
    def test_fallback_parser(self):
        """Test fallback response parser"""
        # Test text with clear signals
        text_response = "I recommend BUY with 85% confidence. The market is bullish."
        
        parsed = self.ai_connector._parse_ai_response_fallback(text_response)
        self.assertEqual(parsed["recommendation"], "buy")
        self.assertEqual(parsed["confidence"], 85)
        self.assertEqual(parsed["sentiment"], "bullish")
    
    def test_market_data_formatting(self):
        """Test market data formatting for AI"""
        formatted = self.ai_connector._format_market_data(self.sample_market_data)
        
        # Check that all important data is included
        self.assertIn("BTC/USDT", formatted)
        self.assertIn("45000.0", formatted)
        self.assertIn("EMA50", formatted)
        self.assertIn("RSI", formatted)
        self.assertIn("uptrend", formatted)
    
    def test_debate_mode_activation(self):
        """Test debate mode activation when models disagree"""
        # Mock different recommendations from each service
        mock_signals = {
            "openai": {
                "service": "openai",
                "analysis": {"recommendation": "buy", "confidence": 75}
            },
            "deepseek": {
                "service": "deepseek", 
                "analysis": {"recommendation": "sell", "confidence": 80}
            },
            "qwen": {
                "service": "qwen",
                "analysis": {"recommendation": "hold", "confidence": 60}
            }
        }
        
        # Mock the service manager to return accounts
        with patch.object(self.ai_connector.service_manager, 'get_next_openai_account') as mock_openai_acc, \
             patch.object(self.ai_connector.service_manager, 'get_next_deepseek_account') as mock_deepseek_acc, \
             patch.object(self.ai_connector.service_manager, 'get_next_qwen_account') as mock_qwen_acc:
            
            mock_openai_acc.return_value = None  # No accounts available for debate
            mock_deepseek_acc.return_value = None
            mock_qwen_acc.return_value = None
            
            # This should trigger debate mode logic
            # The actual debate implementation would be tested separately
            pass


class TestAIServiceManager(unittest.TestCase):
    """Test AI Service Manager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.service_manager = AIServiceManager()
    
    def test_account_rotation(self):
        """Test account rotation functionality"""
        # This would test the account rotation logic
        # Implementation depends on the actual AIServiceManager structure
        pass
    
    def test_account_error_handling(self):
        """Test account error handling and disabling"""
        # This would test error counting and account disabling
        pass


if __name__ == "__main__":
    # Run tests
    unittest.main(verbosity=2)
