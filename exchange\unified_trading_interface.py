"""
Unified Trading Interface for SP.Bot Enhanced v2.0.0

This module provides a unified interface that supports both:
- LIVE Mode: Real trading using Binance API
- Paper Trading Mode: Simulation without real risk

Features:
- Seamless switching between modes
- Performance comparison between paper and live trading
- Comprehensive reporting and analytics
"""

import logging
import datetime
import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Union

logger = logging.getLogger(__name__)

class UnifiedTradingInterface:
    """
    Unified Trading Interface supporting both LIVE and Paper Trading modes
    """
    
    def __init__(self, mode="paper", initial_balance=1000.0, binance_api=None, config=None):
        """
        Initialize the unified trading interface
        
        Args:
            mode (str): Trading mode - "live" or "paper"
            initial_balance (float): Initial balance for paper trading
            binance_api: Binance API instance for live trading
            config (dict): Configuration parameters
        """
        self.mode = mode.lower()
        self.config = config or {}
        self.initial_balance = initial_balance
        self.binance_api = binance_api
        
        # Validate mode
        if self.mode not in ["live", "paper"]:
            raise ValueError("Mode must be 'live' or 'paper'")
        
        # Validate live trading requirements
        if self.mode == "live" and not binance_api:
            raise ValueError("Binance API required for live trading mode")
        
        # Initialize mode-specific attributes
        self._initialize_mode_specific_attributes()
        
        # Common tracking attributes
        self.trade_history = []
        self.performance_metrics = {}
        self.comparison_data = {
            'paper_trades': [],
            'live_trades': [],
            'performance_comparison': {}
        }
        
        # Create reports directory
        self.reports_dir = Path("reports/unified_trading")
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Unified Trading Interface initialized in {self.mode.upper()} mode")

    def _initialize_mode_specific_attributes(self):
        """Initialize attributes specific to the trading mode"""
        if self.mode == "paper":
            # Paper trading attributes
            self.balance = self.initial_balance
            self.positions = {}
            self.total_trades = 0
            self.winning_trades = 0
            self.losing_trades = 0
            self.total_profit = 0.0
            self.total_loss = 0.0
            self.max_drawdown = 0.0
            self.peak_balance = self.initial_balance
            
        else:  # live mode
            # Live trading attributes
            self.balance = None  # Will be fetched from exchange
            self.positions = {}
            self.api_calls_count = 0
            self.last_balance_update = None

    def execute_trade(self, symbol: str, side: str, quantity: float, price: float, 
                     stop_loss: Optional[float] = None, take_profit: Optional[float] = None,
                     leverage: float = 1.0, **kwargs) -> Optional[Dict]:
        """
        Execute a trade in the current mode
        
        Args:
            symbol (str): Trading pair symbol
            side (str): Trade side ('buy' or 'sell')
            quantity (float): Trade quantity
            price (float): Trade price
            stop_loss (float, optional): Stop loss price
            take_profit (float, optional): Take profit price
            leverage (float): Leverage for the trade
            **kwargs: Additional parameters
            
        Returns:
            dict: Trade execution result
        """
        try:
            if self.mode == "paper":
                return self._execute_paper_trade(symbol, side, quantity, price, stop_loss, take_profit, leverage, **kwargs)
            else:
                return self._execute_live_trade(symbol, side, quantity, price, stop_loss, take_profit, leverage, **kwargs)
                
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return None

    def _execute_paper_trade(self, symbol: str, side: str, quantity: float, price: float,
                           stop_loss: Optional[float], take_profit: Optional[float], 
                           leverage: float, **kwargs) -> Dict:
        """Execute a paper trade"""
        # Calculate trade value
        trade_value = quantity * price
        margin_required = trade_value / leverage if leverage > 0 else trade_value
        
        # Check balance for buy orders
        if side.lower() == 'buy' and margin_required > self.balance:
            logger.warning(f"Insufficient balance for paper trade: {margin_required} required, {self.balance} available")
            return None
        
        # Create trade ID
        trade_id = f"paper_{len(self.trade_history) + 1}"
        
        # Create trade object
        trade = {
            "id": trade_id,
            "mode": "paper",
            "symbol": symbol,
            "side": side.lower(),
            "quantity": quantity,
            "price": price,
            "value": trade_value,
            "margin_used": margin_required,
            "leverage": leverage,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "timestamp": datetime.datetime.now().isoformat(),
            "status": "filled",
            "fees": trade_value * 0.001,  # 0.1% fee simulation
            **kwargs
        }
        
        # Update balance
        if side.lower() == 'buy':
            self.balance -= trade_value
            self.positions[trade_id] = trade
        else:
            self.balance += trade_value
            # Handle position closing logic here
            self._close_paper_position(symbol, trade)
        
        # Add to history
        self.trade_history.append(trade)
        self.comparison_data['paper_trades'].append(trade)
        
        logger.info(f"Paper trade executed: {side.upper()} {quantity} {symbol} at {price}")
        return trade

    def _execute_live_trade(self, symbol: str, side: str, quantity: float, price: float,
                          stop_loss: Optional[float], take_profit: Optional[float], 
                          leverage: float, **kwargs) -> Dict:
        """Execute a live trade using Binance API"""
        try:
            # Prepare order parameters
            order_params = {
                'symbol': symbol,
                'side': side.upper(),
                'type': 'LIMIT',
                'timeInForce': 'GTC',
                'quantity': quantity,
                'price': price
            }
            
            # Execute order through Binance API
            result = self.binance_api.create_order(**order_params)
            self.api_calls_count += 1
            
            if result and result.get('status') == 'FILLED':
                # Create trade object
                trade = {
                    "id": result.get('orderId'),
                    "mode": "live",
                    "symbol": symbol,
                    "side": side.lower(),
                    "quantity": float(result.get('executedQty', quantity)),
                    "price": float(result.get('price', price)),
                    "value": float(result.get('cummulativeQuoteQty', quantity * price)),
                    "leverage": leverage,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "timestamp": datetime.datetime.now().isoformat(),
                    "status": "filled",
                    "fees": float(result.get('fills', [{}])[0].get('commission', 0)),
                    "binance_result": result,
                    **kwargs
                }
                
                # Add to history
                self.trade_history.append(trade)
                self.comparison_data['live_trades'].append(trade)
                
                # Update positions
                if side.lower() == 'buy':
                    self.positions[trade['id']] = trade
                else:
                    self._close_live_position(symbol, trade)
                
                logger.info(f"Live trade executed: {side.upper()} {quantity} {symbol} at {price}")
                return trade
            else:
                logger.error(f"Live trade failed: {result}")
                return None
                
        except Exception as e:
            logger.error(f"Error executing live trade: {e}")
            return None

    def _close_paper_position(self, symbol: str, sell_trade: Dict):
        """Close paper trading position and calculate P&L"""
        for pos_id, position in list(self.positions.items()):
            if position["symbol"] == symbol and position["side"] == "buy":
                # Calculate P&L
                buy_value = position["value"]
                sell_value = sell_trade["value"]
                pnl = sell_value - buy_value
                pnl_percent = (pnl / buy_value) * 100
                
                # Update position
                position["exit_price"] = sell_trade["price"]
                position["exit_timestamp"] = sell_trade["timestamp"]
                position["pnl"] = pnl
                position["pnl_percent"] = pnl_percent
                position["status"] = "closed"
                
                # Update statistics
                self.total_trades += 1
                if pnl > 0:
                    self.winning_trades += 1
                    self.total_profit += pnl
                else:
                    self.losing_trades += 1
                    self.total_loss += abs(pnl)
                
                # Update drawdown
                current_equity = self.get_current_equity()
                if current_equity > self.peak_balance:
                    self.peak_balance = current_equity
                else:
                    drawdown = (self.peak_balance - current_equity) / self.peak_balance
                    self.max_drawdown = max(self.max_drawdown, drawdown)
                
                # Remove from open positions
                del self.positions[pos_id]
                break

    def _close_live_position(self, symbol: str, sell_trade: Dict):
        """Close live trading position"""
        # Implementation for closing live positions
        # This would involve updating position tracking and P&L calculation
        pass

    def get_current_balance(self) -> float:
        """Get current account balance"""
        if self.mode == "paper":
            return self.balance
        else:
            try:
                # Fetch balance from Binance API
                account_info = self.binance_api.get_account()
                self.api_calls_count += 1
                
                # Find USDT balance
                for balance in account_info.get('balances', []):
                    if balance['asset'] == 'USDT':
                        self.balance = float(balance['free'])
                        self.last_balance_update = datetime.datetime.now()
                        return self.balance
                        
                return 0.0
            except Exception as e:
                logger.error(f"Error fetching live balance: {e}")
                return 0.0

    def get_current_equity(self) -> float:
        """Get current total equity (balance + open positions value)"""
        balance = self.get_current_balance()
        
        # Calculate open positions value
        positions_value = 0.0
        for position in self.positions.values():
            if position.get('status') != 'closed':
                # For paper trading, use current market price if available
                current_price = position.get('current_price', position['price'])
                positions_value += position['quantity'] * current_price
        
        return balance + positions_value

    def get_performance_metrics(self) -> Dict:
        """Get comprehensive performance metrics"""
        current_equity = self.get_current_equity()
        
        # Calculate basic metrics
        total_return = current_equity - self.initial_balance
        total_return_percent = (total_return / self.initial_balance) * 100 if self.initial_balance > 0 else 0
        
        # Calculate trade statistics
        completed_trades = [t for t in self.trade_history if t.get('status') == 'closed']
        win_rate = (self.winning_trades / max(1, self.total_trades)) * 100
        
        # Calculate profit factor
        profit_factor = self.total_profit / max(0.01, self.total_loss) if self.total_loss > 0 else float('inf')
        
        metrics = {
            'mode': self.mode,
            'current_balance': self.get_current_balance(),
            'current_equity': current_equity,
            'total_return': total_return,
            'total_return_percent': total_return_percent,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'max_drawdown': self.max_drawdown * 100,
            'open_positions': len([p for p in self.positions.values() if p.get('status') != 'closed']),
            'last_updated': datetime.datetime.now().isoformat()
        }
        
        if self.mode == "live":
            metrics['api_calls_count'] = self.api_calls_count
            metrics['last_balance_update'] = self.last_balance_update.isoformat() if self.last_balance_update else None
        
        return metrics

    def generate_comparison_report(self) -> Dict:
        """Generate a comparison report between paper and live trading performance"""
        paper_trades = self.comparison_data['paper_trades']
        live_trades = self.comparison_data['live_trades']
        
        def calculate_metrics(trades):
            if not trades:
                return {}
            
            total_pnl = sum(t.get('pnl', 0) for t in trades if 'pnl' in t)
            winning_trades = [t for t in trades if t.get('pnl', 0) > 0]
            losing_trades = [t for t in trades if t.get('pnl', 0) <= 0]
            
            return {
                'total_trades': len(trades),
                'total_pnl': total_pnl,
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'win_rate': len(winning_trades) / len(trades) * 100 if trades else 0,
                'avg_win': sum(t['pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0,
                'avg_loss': sum(t['pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0
            }
        
        paper_metrics = calculate_metrics(paper_trades)
        live_metrics = calculate_metrics(live_trades)
        
        comparison = {
            'timestamp': datetime.datetime.now().isoformat(),
            'paper_trading': paper_metrics,
            'live_trading': live_metrics,
            'comparison': {
                'performance_difference': live_metrics.get('total_pnl', 0) - paper_metrics.get('total_pnl', 0),
                'win_rate_difference': live_metrics.get('win_rate', 0) - paper_metrics.get('win_rate', 0),
                'trade_count_difference': live_metrics.get('total_trades', 0) - paper_metrics.get('total_trades', 0)
            }
        }
        
        # Save comparison report
        report_file = self.reports_dir / f"comparison_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(comparison, f, indent=2)
        
        logger.info(f"Comparison report generated: {report_file}")
        return comparison

    def switch_mode(self, new_mode: str):
        """Switch between paper and live trading modes"""
        if new_mode.lower() not in ["live", "paper"]:
            raise ValueError("Mode must be 'live' or 'paper'")
        
        if new_mode.lower() == "live" and not self.binance_api:
            raise ValueError("Binance API required for live trading mode")
        
        old_mode = self.mode
        self.mode = new_mode.lower()
        
        # Reinitialize mode-specific attributes
        self._initialize_mode_specific_attributes()
        
        logger.info(f"Switched from {old_mode.upper()} to {self.mode.upper()} mode")

    def get_status(self) -> Dict:
        """Get current status of the trading interface"""
        return {
            'mode': self.mode,
            'balance': self.get_current_balance(),
            'equity': self.get_current_equity(),
            'open_positions': len([p for p in self.positions.values() if p.get('status') != 'closed']),
            'total_trades': len(self.trade_history),
            'api_calls': self.api_calls_count if self.mode == "live" else 0,
            'last_updated': datetime.datetime.now().isoformat()
        }
