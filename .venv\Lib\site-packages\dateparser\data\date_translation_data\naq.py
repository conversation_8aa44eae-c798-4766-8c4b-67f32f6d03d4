info = {
    "name": "naq",
    "date_order": "DMY",
    "january": [
        "jan",
        "ǃkhanni"
    ],
    "february": [
        "feb",
        "ǃkhanǀgôab"
    ],
    "march": [
        "mar",
        "ǀkhuuǁkhâb"
    ],
    "april": [
        "apr",
        "ǃhôaǂkhaib"
    ],
    "may": [
        "may",
        "ǃkhaitsâb"
    ],
    "june": [
        "gamaǀaeb",
        "jun"
    ],
    "july": [
        "jul",
        "ǂkhoesaob"
    ],
    "august": [
        "aoǁkhuumûǁkhâb",
        "aug"
    ],
    "september": [
        "sep",
        "taraǀkhuumûǁkhâb"
    ],
    "october": [
        "oct",
        "ǂnûǁnâiseb"
    ],
    "november": [
        "nov",
        "ǀhooǂgaeb"
    ],
    "december": [
        "dec",
        "hôasoreǁkhâb"
    ],
    "monday": [
        "ma",
        "mantaxtsees"
    ],
    "tuesday": [
        "de",
        "denstaxtsees"
    ],
    "wednesday": [
        "wu",
        "wunstaxtsees"
    ],
    "thursday": [
        "do",
        "dondertaxtsees"
    ],
    "friday": [
        "fr",
        "fraitaxtsees"
    ],
    "saturday": [
        "sat",
        "satertaxtsees"
    ],
    "sunday": [
        "son",
        "sontaxtsees"
    ],
    "am": [
        "ǁgoagas"
    ],
    "pm": [
        "ǃuias"
    ],
    "year": [
        "kurib"
    ],
    "month": [
        "ǁkhâb"
    ],
    "week": [
        "wekheb"
    ],
    "day": [
        "tsees"
    ],
    "hour": [
        "iiri"
    ],
    "minute": [
        "haib"
    ],
    "second": [
        "ǀgâub"
    ],
    "relative-type": {
        "0 day ago": [
            "neetsee"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week"
        ],
        "0 year ago": [
            "this year"
        ],
        "1 day ago": [
            "yesterday"
        ],
        "1 month ago": [
            "last month"
        ],
        "1 week ago": [
            "last week"
        ],
        "1 year ago": [
            "last year"
        ],
        "in 1 day": [
            "tomorrow"
        ],
        "in 1 month": [
            "next month"
        ],
        "in 1 week": [
            "next week"
        ],
        "in 1 year": [
            "next year"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
