info = {
    "name": "hsb",
    "date_order": "DMY",
    "january": [
        "jan",
        "januar",
        "januara"
    ],
    "february": [
        "feb",
        "februar",
        "februara"
    ],
    "march": [
        "měr",
        "měrc",
        "měrca"
    ],
    "april": [
        "apr",
        "apryl",
        "apryla"
    ],
    "may": [
        "mej",
        "meja",
        "meje"
    ],
    "june": [
        "jun",
        "junij",
        "junija"
    ],
    "july": [
        "jul",
        "julij",
        "julija"
    ],
    "august": [
        "awg",
        "awgust",
        "awgusta"
    ],
    "september": [
        "sep",
        "september",
        "septembra"
    ],
    "october": [
        "okt",
        "oktober",
        "oktobra"
    ],
    "november": [
        "now",
        "nowember",
        "nowembra"
    ],
    "december": [
        "dec",
        "december",
        "decembra"
    ],
    "monday": [
        "pón",
        "póndźela"
    ],
    "tuesday": [
        "wut",
        "wutora"
    ],
    "wednesday": [
        "srj",
        "srjeda"
    ],
    "thursday": [
        "štw",
        "štwórtk"
    ],
    "friday": [
        "pja",
        "pjatk"
    ],
    "saturday": [
        "sob",
        "sobota"
    ],
    "sunday": [
        "nje",
        "njedźela"
    ],
    "am": [
        "dopołdnja"
    ],
    "pm": [
        "popołdnju"
    ],
    "year": [
        "l",
        "lěto"
    ],
    "month": [
        "měs",
        "měsac"
    ],
    "week": [
        "tydź",
        "tydźeń"
    ],
    "day": [
        "d",
        "dźeń"
    ],
    "hour": [
        "h",
        "hodź",
        "hodźina"
    ],
    "minute": [
        "m",
        "min",
        "minuta"
    ],
    "second": [
        "s",
        "sek",
        "sekunda"
    ],
    "relative-type": {
        "0 day ago": [
            "dźensa"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "tutón měsac"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "tutón tydźeń"
        ],
        "0 year ago": [
            "lětsa"
        ],
        "1 day ago": [
            "wčera"
        ],
        "1 month ago": [
            "zašły měsac"
        ],
        "1 week ago": [
            "zašły tydźeń"
        ],
        "1 year ago": [
            "loni"
        ],
        "in 1 day": [
            "jutře"
        ],
        "in 1 month": [
            "přichodny měsac"
        ],
        "in 1 week": [
            "přichodny tydźeń"
        ],
        "in 1 year": [
            "klětu"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "před (\\d+[.,]?\\d*) d",
            "před (\\d+[.,]?\\d*) dnj",
            "před (\\d+[.,]?\\d*) dnjemi",
            "před (\\d+[.,]?\\d*) dnjom"
        ],
        "\\1 hour ago": [
            "před (\\d+[.,]?\\d*) h",
            "před (\\d+[.,]?\\d*) hodź",
            "před (\\d+[.,]?\\d*) hodźinami",
            "před (\\d+[.,]?\\d*) hodźinu"
        ],
        "\\1 minute ago": [
            "před (\\d+[.,]?\\d*) m",
            "před (\\d+[.,]?\\d*) min",
            "před (\\d+[.,]?\\d*) minutami",
            "před (\\d+[.,]?\\d*) minutu"
        ],
        "\\1 month ago": [
            "před (\\d+[.,]?\\d*) měs",
            "před (\\d+[.,]?\\d*) měsacami",
            "před (\\d+[.,]?\\d*) měsacom"
        ],
        "\\1 second ago": [
            "před (\\d+[.,]?\\d*) s",
            "před (\\d+[.,]?\\d*) sek",
            "před (\\d+[.,]?\\d*) sekundami",
            "před (\\d+[.,]?\\d*) sekundu"
        ],
        "\\1 week ago": [
            "před (\\d+[.,]?\\d*) tydź",
            "před (\\d+[.,]?\\d*) tydźenjemi",
            "před (\\d+[.,]?\\d*) tydźenjom"
        ],
        "\\1 year ago": [
            "před (\\d+[.,]?\\d*) l",
            "před (\\d+[.,]?\\d*) lětami",
            "před (\\d+[.,]?\\d*) lětom"
        ],
        "in \\1 day": [
            "za (\\d+[.,]?\\d*) d",
            "za (\\d+[.,]?\\d*) dnj",
            "za (\\d+[.,]?\\d*) dnjow",
            "za (\\d+[.,]?\\d*) dźeń"
        ],
        "in \\1 hour": [
            "za (\\d+[.,]?\\d*) h",
            "za (\\d+[.,]?\\d*) hodź",
            "za (\\d+[.,]?\\d*) hodźin",
            "za (\\d+[.,]?\\d*) hodźinu"
        ],
        "in \\1 minute": [
            "za (\\d+[.,]?\\d*) m",
            "za (\\d+[.,]?\\d*) min",
            "za (\\d+[.,]?\\d*) minutow",
            "za (\\d+[.,]?\\d*) minutu"
        ],
        "in \\1 month": [
            "za (\\d+[.,]?\\d*) měs",
            "za (\\d+[.,]?\\d*) měsac",
            "za (\\d+[.,]?\\d*) měsacow"
        ],
        "in \\1 second": [
            "za (\\d+[.,]?\\d*) s",
            "za (\\d+[.,]?\\d*) sek",
            "za (\\d+[.,]?\\d*) sekundow",
            "za (\\d+[.,]?\\d*) sekundu"
        ],
        "in \\1 week": [
            "za (\\d+[.,]?\\d*) tydź",
            "za (\\d+[.,]?\\d*) tydźenjow",
            "za (\\d+[.,]?\\d*) tydźeń"
        ],
        "in \\1 year": [
            "za (\\d+[.,]?\\d*) l",
            "za (\\d+[.,]?\\d*) lět",
            "za (\\d+[.,]?\\d*) lěto"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
