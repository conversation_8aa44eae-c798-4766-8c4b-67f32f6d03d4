info = {
    "name": "pt",
    "date_order": "DMY",
    "january": [
        "jan",
        "janeiro"
    ],
    "february": [
        "fev",
        "fevereiro"
    ],
    "march": [
        "mar",
        "março"
    ],
    "april": [
        "abr",
        "abril"
    ],
    "may": [
        "mai",
        "maio"
    ],
    "june": [
        "jun",
        "junho"
    ],
    "july": [
        "jul",
        "julho"
    ],
    "august": [
        "ago",
        "agosto"
    ],
    "september": [
        "set",
        "setembro",
        "Septembro"
    ],
    "october": [
        "out",
        "outubro"
    ],
    "november": [
        "nov",
        "novembro"
    ],
    "december": [
        "dez",
        "dezembro"
    ],
    "monday": [
        "seg",
        "segunda-feira",
        "Segunda"
    ],
    "tuesday": [
        "ter",
        "terça-feira",
        "<PERSON><PERSON><PERSON>"
    ],
    "wednesday": [
        "qua",
        "quarta-feira",
        "Quarta"
    ],
    "thursday": [
        "qui",
        "quinta-feira",
        "Quinta"
    ],
    "friday": [
        "sex",
        "sexta-feira",
        "Sexta"
    ],
    "saturday": [
        "sáb",
        "sábado",
        "Sab"
    ],
    "sunday": [
        "dom",
        "domingo"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "ano",
        "anos"
    ],
    "month": [
        "mês",
        "meses"
    ],
    "week": [
        "sem",
        "semana",
        "semanas"
    ],
    "day": [
        "dia",
        "dias"
    ],
    "hour": [
        "h",
        "hora",
        "horas"
    ],
    "minute": [
        "m",
        "min",
        "minuto",
        "minutos"
    ],
    "second": [
        "s",
        "seg",
        "segundo",
        "segundos"
    ],
    "relative-type": {
        "0 day ago": [
            "hoje"
        ],
        "0 hour ago": [
            "esta hora"
        ],
        "0 minute ago": [
            "este minuto"
        ],
        "0 month ago": [
            "este mês"
        ],
        "0 second ago": [
            "agora"
        ],
        "0 week ago": [
            "esta semana"
        ],
        "0 year ago": [
            "este ano"
        ],
        "1 day ago": [
            "ontem"
        ],
        "1 month ago": [
            "mês passado"
        ],
        "1 week ago": [
            "semana passada"
        ],
        "1 year ago": [
            "ano passado"
        ],
        "in 1 day": [
            "amanhã"
        ],
        "in 1 month": [
            "próximo mês"
        ],
        "in 1 week": [
            "próxima semana"
        ],
        "in 1 year": [
            "próximo ano"
        ],
        "2 day ago": [
            "anteontem"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "há (\\d+[.,]?\\d*) dia",
            "há (\\d+[.,]?\\d*) dias"
        ],
        "\\1 hour ago": [
            "há (\\d+[.,]?\\d*) h",
            "há (\\d+[.,]?\\d*) hora",
            "há (\\d+[.,]?\\d*) horas"
        ],
        "\\1 minute ago": [
            "há (\\d+[.,]?\\d*) min",
            "há (\\d+[.,]?\\d*) mins",
            "há (\\d+[.,]?\\d*) minuto",
            "há (\\d+[.,]?\\d*) minutos"
        ],
        "\\1 month ago": [
            "há (\\d+[.,]?\\d*) meses",
            "há (\\d+[.,]?\\d*) mês"
        ],
        "\\1 second ago": [
            "há (\\d+[.,]?\\d*) seg",
            "há (\\d+[.,]?\\d*) segundo",
            "há (\\d+[.,]?\\d*) segundos"
        ],
        "\\1 week ago": [
            "há (\\d+[.,]?\\d*) sem",
            "há (\\d+[.,]?\\d*) semana",
            "há (\\d+[.,]?\\d*) semanas"
        ],
        "\\1 year ago": [
            "há (\\d+[.,]?\\d*) ano",
            "há (\\d+[.,]?\\d*) anos"
        ],
        "in \\1 day": [
            "em (\\d+[.,]?\\d*) dia",
            "em (\\d+[.,]?\\d*) dias"
        ],
        "in \\1 hour": [
            "em (\\d+[.,]?\\d*) h",
            "em (\\d+[.,]?\\d*) hora",
            "em (\\d+[.,]?\\d*) horas"
        ],
        "in \\1 minute": [
            "em (\\d+[.,]?\\d*) min",
            "em (\\d+[.,]?\\d*) mins",
            "em (\\d+[.,]?\\d*) minuto",
            "em (\\d+[.,]?\\d*) minutos"
        ],
        "in \\1 month": [
            "em (\\d+[.,]?\\d*) meses",
            "em (\\d+[.,]?\\d*) mês"
        ],
        "in \\1 second": [
            "em (\\d+[.,]?\\d*) seg",
            "em (\\d+[.,]?\\d*) segs",
            "em (\\d+[.,]?\\d*) segundo",
            "em (\\d+[.,]?\\d*) segundos"
        ],
        "in \\1 week": [
            "em (\\d+[.,]?\\d*) sem",
            "em (\\d+[.,]?\\d*) semana",
            "em (\\d+[.,]?\\d*) semanas"
        ],
        "in \\1 year": [
            "em (\\d+[.,]?\\d*) ano",
            "em (\\d+[.,]?\\d*) anos"
        ]
    },
    "locale_specific": {
        "pt-AO": {
            "name": "pt-AO",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-CH": {
            "name": "pt-CH",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-CV": {
            "name": "pt-CV",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-GQ": {
            "name": "pt-GQ",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-GW": {
            "name": "pt-GW",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-LU": {
            "name": "pt-LU",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-MO": {
            "name": "pt-MO",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-MZ": {
            "name": "pt-MZ",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-PT": {
            "name": "pt-PT",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-ST": {
            "name": "pt-ST",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        },
        "pt-TL": {
            "name": "pt-TL",
            "monday": [
                "segunda"
            ],
            "tuesday": [
                "terça"
            ],
            "wednesday": [
                "quarta"
            ],
            "thursday": [
                "quinta"
            ],
            "friday": [
                "sexta"
            ],
            "am": [
                "da manhã",
                "manhã"
            ],
            "pm": [
                "da tarde",
                "tarde"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "há (\\d+[.,]?\\d*) s"
                ],
                "in \\1 day": [
                    "dentro de (\\d+[.,]?\\d*) dia",
                    "dentro de (\\d+[.,]?\\d*) dias"
                ],
                "in \\1 hour": [
                    "dentro de (\\d+[.,]?\\d*) h",
                    "dentro de (\\d+[.,]?\\d*) hora",
                    "dentro de (\\d+[.,]?\\d*) horas"
                ],
                "in \\1 minute": [
                    "dentro de (\\d+[.,]?\\d*) min",
                    "dentro de (\\d+[.,]?\\d*) minuto",
                    "dentro de (\\d+[.,]?\\d*) minutos"
                ],
                "in \\1 month": [
                    "dentro de (\\d+[.,]?\\d*) meses",
                    "dentro de (\\d+[.,]?\\d*) mês"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) s",
                    "dentro de (\\d+[.,]?\\d*) segundo",
                    "dentro de (\\d+[.,]?\\d*) segundos"
                ],
                "in \\1 week": [
                    "dentro de (\\d+[.,]?\\d*) sem",
                    "dentro de (\\d+[.,]?\\d*) semana",
                    "dentro de (\\d+[.,]?\\d*) semanas"
                ],
                "in \\1 year": [
                    "dentro de (\\d+[.,]?\\d*) ano",
                    "dentro de (\\d+[.,]?\\d*) anos"
                ]
            }
        }
    },
    "skip": [
        "cerca",
        "de",
        "e",
        "às",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "pertain": [
        "de"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "atrás",
        "há"
    ],
    "in": [
        "em"
    ],
    "simplifications": [
        {
            "uma": "1"
        },
        {
            "um": "1"
        },
        {
            "alguns segundos": "44 segundos"
        }
    ]
}
