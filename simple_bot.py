"""
Simple Trading Bot
A simplified version of the trading bot that works with minimal dependencies
"""

import os
import sys
import time
import logging
import datetime
import random
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/simple_bot.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("simple_bot")

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

class SimpleBinanceAPI:
    """
    Simplified Binance API client for testing
    """
    
    def __init__(self, api_key=None, api_secret=None, use_testnet=False):
        self.api_key = api_key
        self.api_secret = api_secret
        self.use_testnet = use_testnet
        logger.info("SimpleBinanceAPI initialized")
    
    def get_ticker(self, symbol):
        """
        Simulate getting ticker data
        """
        # Simulate price with some randomness
        price = 60000 + random.uniform(-1000, 1000)
        
        ticker = {
            'symbol': symbol,
            'last': price,
            'bid': price - 10,
            'ask': price + 10,
            'high': price + 500,
            'low': price - 500,
            'volume': random.uniform(1000, 5000),
            'timestamp': datetime.datetime.now().timestamp() * 1000
        }
        
        logger.info(f"Simulated ticker for {symbol}: ${price:.2f}")
        return ticker
    
    def get_balance(self):
        """
        Simulate getting account balance
        """
        balance = {
            'USDT': {
                'free': 1000.0,
                'used': 0.0,
                'total': 1000.0
            },
            'BTC': {
                'free': 0.01,
                'used': 0.0,
                'total': 0.01
            }
        }
        
        logger.info(f"Simulated balance: {balance['USDT']['free']} USDT, {balance['BTC']['free']} BTC")
        return balance
    
    def create_market_buy_order(self, symbol, amount):
        """
        Simulate creating a market buy order
        """
        price = self.get_ticker(symbol)['last']
        quantity = amount / price
        
        order = {
            'id': f"simulated_{int(time.time())}",
            'symbol': symbol,
            'side': 'buy',
            'type': 'market',
            'price': price,
            'amount': quantity,
            'cost': amount,
            'timestamp': datetime.datetime.now().timestamp() * 1000,
            'status': 'closed'
        }
        
        logger.info(f"Simulated market buy order: {quantity:.8f} {symbol} at ${price:.2f}")
        return order
    
    def create_market_sell_order(self, symbol, amount):
        """
        Simulate creating a market sell order
        """
        price = self.get_ticker(symbol)['last']
        
        order = {
            'id': f"simulated_{int(time.time())}",
            'symbol': symbol,
            'side': 'sell',
            'type': 'market',
            'price': price,
            'amount': amount,
            'cost': amount * price,
            'timestamp': datetime.datetime.now().timestamp() * 1000,
            'status': 'closed'
        }
        
        logger.info(f"Simulated market sell order: {amount:.8f} {symbol} at ${price:.2f}")
        return order

class SimpleBot:
    """
    Simple trading bot for testing
    """
    
    def __init__(self, api_key=None, api_secret=None, test_mode=True):
        self.api = SimpleBinanceAPI(api_key, api_secret, use_testnet=test_mode)
        self.test_mode = test_mode
        self.symbol = "BTC/USDT"
        self.check_interval = 60  # seconds
        self.trade_amount = 100  # USDT
        self.stop_loss = 0.02  # 2%
        self.take_profit = 0.03  # 3%
        self.current_position = None
        self.trade_history = []
        
        logger.info(f"SimpleBot initialized in {'TEST' if test_mode else 'LIVE'} mode")
    
    def check_price(self):
        """
        Check current price
        """
        ticker = self.api.get_ticker(self.symbol)
        price = ticker['last']
        logger.info(f"Current price for {self.symbol}: ${price:.2f}")
        return price
    
    def analyze_market(self):
        """
        Simple market analysis
        """
        # Generate a random signal for demonstration
        signal = random.choice(['buy', 'sell', 'hold', 'hold', 'hold'])  # Bias towards holding
        
        if signal == 'buy':
            logger.info("Analysis: Bullish signal detected")
        elif signal == 'sell':
            logger.info("Analysis: Bearish signal detected")
        else:
            logger.info("Analysis: No clear signal, holding")
        
        return signal
    
    def execute_trade(self, signal, price):
        """
        Execute a trade based on the signal
        """
        if signal == 'buy' and self.current_position is None:
            # Execute buy order
            order = self.api.create_market_buy_order(self.symbol, self.trade_amount)
            
            # Record position
            self.current_position = {
                'side': 'buy',
                'entry_price': price,
                'quantity': order['amount'],
                'timestamp': datetime.datetime.now().isoformat(),
                'order_id': order['id']
            }
            
            logger.info(f"Opened BUY position: {order['amount']:.8f} {self.symbol} at ${price:.2f}")
            
        elif signal == 'sell' and self.current_position is None:
            # Calculate quantity (assuming we have BTC)
            balance = self.api.get_balance()
            quantity = min(balance['BTC']['free'], 0.01)  # Limit to 0.01 BTC for testing
            
            if quantity > 0:
                # Execute sell order
                order = self.api.create_market_sell_order(self.symbol, quantity)
                
                # Record position
                self.current_position = {
                    'side': 'sell',
                    'entry_price': price,
                    'quantity': quantity,
                    'timestamp': datetime.datetime.now().isoformat(),
                    'order_id': order['id']
                }
                
                logger.info(f"Opened SELL position: {quantity:.8f} {self.symbol} at ${price:.2f}")
            else:
                logger.warning("Cannot open SELL position: Insufficient BTC balance")
    
    def manage_positions(self, current_price):
        """
        Manage open positions (check stop loss and take profit)
        """
        if self.current_position is None:
            return
        
        entry_price = self.current_position['entry_price']
        side = self.current_position['side']
        quantity = self.current_position['quantity']
        
        if side == 'buy':
            # Calculate profit/loss percentage
            pnl_percent = (current_price - entry_price) / entry_price * 100
            
            if pnl_percent <= -self.stop_loss * 100:
                # Stop loss triggered
                logger.info(f"Stop loss triggered: {pnl_percent:.2f}% loss")
                order = self.api.create_market_sell_order(self.symbol, quantity)
                
                # Record trade in history
                self.trade_history.append({
                    'entry': self.current_position,
                    'exit': {
                        'price': current_price,
                        'timestamp': datetime.datetime.now().isoformat(),
                        'reason': 'stop_loss',
                        'pnl_percent': pnl_percent,
                        'order_id': order['id']
                    }
                })
                
                # Clear current position
                self.current_position = None
                
            elif pnl_percent >= self.take_profit * 100:
                # Take profit triggered
                logger.info(f"Take profit triggered: {pnl_percent:.2f}% profit")
                order = self.api.create_market_sell_order(self.symbol, quantity)
                
                # Record trade in history
                self.trade_history.append({
                    'entry': self.current_position,
                    'exit': {
                        'price': current_price,
                        'timestamp': datetime.datetime.now().isoformat(),
                        'reason': 'take_profit',
                        'pnl_percent': pnl_percent,
                        'order_id': order['id']
                    }
                })
                
                # Clear current position
                self.current_position = None
        
        elif side == 'sell':
            # For short positions, profit/loss is reversed
            pnl_percent = (entry_price - current_price) / entry_price * 100
            
            if pnl_percent <= -self.stop_loss * 100:
                # Stop loss triggered
                logger.info(f"Stop loss triggered: {pnl_percent:.2f}% loss")
                order = self.api.create_market_buy_order(self.symbol, self.trade_amount)
                
                # Record trade in history
                self.trade_history.append({
                    'entry': self.current_position,
                    'exit': {
                        'price': current_price,
                        'timestamp': datetime.datetime.now().isoformat(),
                        'reason': 'stop_loss',
                        'pnl_percent': pnl_percent,
                        'order_id': order['id']
                    }
                })
                
                # Clear current position
                self.current_position = None
                
            elif pnl_percent >= self.take_profit * 100:
                # Take profit triggered
                logger.info(f"Take profit triggered: {pnl_percent:.2f}% profit")
                order = self.api.create_market_buy_order(self.symbol, self.trade_amount)
                
                # Record trade in history
                self.trade_history.append({
                    'entry': self.current_position,
                    'exit': {
                        'price': current_price,
                        'timestamp': datetime.datetime.now().isoformat(),
                        'reason': 'take_profit',
                        'pnl_percent': pnl_percent,
                        'order_id': order['id']
                    }
                })
                
                # Clear current position
                self.current_position = None
    
    def run(self):
        """
        Run the trading bot
        """
        logger.info(f"Starting SimpleBot in {'TEST' if self.test_mode else 'LIVE'} mode")
        logger.info(f"Trading {self.symbol} with {self.trade_amount} USDT per trade")
        logger.info(f"Stop Loss: {self.stop_loss*100}%, Take Profit: {self.take_profit*100}%")
        
        try:
            iteration = 1
            while True:
                logger.info(f"Iteration {iteration}")
                
                # Check current price
                current_price = self.check_price()
                
                # Manage open positions
                self.manage_positions(current_price)
                
                # If no open position, analyze market for new signals
                if self.current_position is None:
                    signal = self.analyze_market()
                    
                    # Execute trade if signal is generated
                    if signal in ['buy', 'sell']:
                        self.execute_trade(signal, current_price)
                
                # Wait for next iteration
                logger.info(f"Waiting {self.check_interval} seconds until next check...")
                time.sleep(self.check_interval)
                iteration += 1
        
        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        except Exception as e:
            logger.error(f"Error: {e}")
        finally:
            logger.info("Bot shutting down")

def main():
    """
    Main function
    """
    # Load environment variables
    load_dotenv()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Simple Trading Bot")
    parser.add_argument("--test", action="store_true", help="Run in test mode (no real trades)")
    args = parser.parse_args()
    
    # Determine test mode
    test_mode = args.test or os.getenv("TEST_MODE", "true").lower() == "true"
    
    # Get API keys from environment
    api_key = os.getenv("BINANCE_API_KEY")
    api_secret = os.getenv("BINANCE_API_SECRET")
    
    # Initialize and run bot
    bot = SimpleBot(api_key, api_secret, test_mode)
    bot.run()

if __name__ == "__main__":
    main()
