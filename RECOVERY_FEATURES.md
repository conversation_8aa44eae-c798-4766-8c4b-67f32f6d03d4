# Recovery Features

This document outlines the recovery features implemented in the SimpleAITradingBot to make it more robust and resilient to errors.

## Memory Management

- **Aggressive Garbage Collection**: The bot now performs aggressive garbage collection to free up memory when needed.
- **Memory Cleanup Routines**: Added routines to clean up memory when RAM usage is high.
- **Circuit Breaker Pattern**: Implemented a circuit breaker pattern to prevent the bot from crashing when system resources are low.

## API Health Check

- **Retry Mechanisms**: Added retry mechanisms for API calls to handle temporary network issues.
- **Fallback Strategies**: Implemented fallback strategies to use alternative API methods when primary methods fail.
- **Alternative API Methods**: Added support for alternative API methods to fetch data when primary methods are unavailable.

## Error Handling and Recovery

- **Comprehensive Exception Handling**: Added comprehensive exception handling to catch and handle errors gracefully.
- **Crash Recovery Mechanisms**: Implemented crash recovery mechanisms to recover from crashes.
- **State Tracking**: Added state tracking to save the bot's state for recovery.

## Watchdog Process

- **Process Monitoring**: Created a watchdog script to monitor the bot process and restart it if it crashes.
- **Process Health Checking**: Added process health checking to detect unresponsive processes.
- **Graceful Shutdown**: Implemented graceful shutdown to close positions and save state before shutting down.

## Resource Optimization

- **Resource Management**: Implemented better resource management to optimize CPU and RAM usage.
- **Throttling**: Added throttling during high CPU/RAM usage to prevent crashes.
- **Memory Usage Optimization**: Optimized memory usage to reduce the bot's memory footprint.

## Configuration

- **Recovery Settings**: Added recovery settings to the .env file to configure recovery behavior.
- **Increased Thresholds**: Increased RAM and CPU thresholds to allow the bot to run on systems with limited resources.
- **Error Cooldown and Retry Settings**: Added error cooldown and retry settings to control how the bot handles errors.

## Usage

To run the bot with recovery features:

```bash
.\run_bot_with_recovery.bat
```

To run the bot with the watchdog process:

```bash
.\run_watchdog.bat
```

## Configuration Options

The following configuration options are available in the .env file:

```
# Recovery settings
MAX_CONSECUTIVE_ERRORS="3"
ERROR_COOLDOWN="30"
ENABLE_CRASH_RECOVERY="true"

# Windows Performance
MAX_RAM_USAGE="95"
CPU_THRESHOLD="95"
RAM_THRESHOLD="95"
```

- `MAX_CONSECUTIVE_ERRORS`: Maximum number of consecutive errors before emergency shutdown.
- `ERROR_COOLDOWN`: Cooldown period in seconds between error retries.
- `ENABLE_CRASH_RECOVERY`: Enable or disable crash recovery features.
- `MAX_RAM_USAGE`: Maximum RAM usage percentage before throttling.
- `CPU_THRESHOLD`: CPU usage threshold percentage before throttling.
- `RAM_THRESHOLD`: RAM usage threshold percentage before throttling.
