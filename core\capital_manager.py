"""
Dynamic Capital Management module for the trading bot
"""

import logging
import os
from analysis.indicators import is_market_volatile, calculate_total_risk, calculate_position_size

# Set up logging
logger = logging.getLogger("capital_manager")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/capital_manager.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class DynamicCapitalManager:
    """
    Dynamic Capital Manager for the trading bot
    """

    def __init__(self, exchange, min_balance=7.0, max_risk_pct=5.0):
        """
        Initialize the capital manager

        Args:
            exchange: Exchange API client
            min_balance (float): Minimum balance required to trade (in USDT)
            max_risk_pct (float): Maximum total risk percentage across all positions
        """
        self.exchange = exchange
        self.min_balance = min_balance
        self.max_risk_pct = max_risk_pct

        # Get risk percentages from environment variables if available
        import os
        self.normal_risk_pct = float(os.getenv("NORMAL_RISK_PCT", "2.0"))  # 2% risk in normal conditions
        self.volatile_risk_pct = float(os.getenv("VOLATILE_RISK_PCT", "1.0"))  # 1% risk in volatile conditions

        logger.info(f"DynamicCapitalManager initialized with min_balance={min_balance} USDT, max_risk_pct={max_risk_pct}%")
        logger.info(f"Risk percentages: normal={self.normal_risk_pct}%, volatile={self.volatile_risk_pct}%")

    def get_available_balance(self, quote_currency="USDT"):
        """
        Get available balance for trading

        Args:
            quote_currency (str): Quote currency (e.g., USDT)

        Returns:
            float: Available balance
        """
        try:
            balance = self.exchange.get_balance()
            available = balance.get(quote_currency, {}).get("free", 0.0)

            logger.info(f"Available balance: {available} {quote_currency}")
            return available
        except Exception as e:
            logger.error(f"Error getting available balance: {e}")
            return 0.0

    def can_trade(self, quote_currency="USDT"):
        """
        Check if trading is allowed based on available balance

        Args:
            quote_currency (str): Quote currency (e.g., USDT)

        Returns:
            bool: True if trading is allowed, False otherwise
        """
        available_balance = self.get_available_balance(quote_currency)
        can_trade = available_balance >= self.min_balance

        if not can_trade:
            logger.warning(f"Trading not allowed: Available balance ({available_balance} {quote_currency}) is below minimum ({self.min_balance} {quote_currency})")

        return can_trade

    def get_risk_percentage(self, market_data):
        """
        Get risk percentage based on market conditions

        Args:
            market_data (dict): Market data including indicators

        Returns:
            float: Risk percentage
        """
        try:
            # Extract ATR values from market data
            historical = market_data.get("historical", [])

            if not historical:
                logger.warning("No historical data available for volatility check")
                return self.volatile_risk_pct  # Conservative approach

            # Extract high, low, close prices
            high_prices = [candle.get("high", 0) for candle in historical]
            low_prices = [candle.get("low", 0) for candle in historical]
            close_prices = [candle.get("close", 0) for candle in historical]

            # Calculate ATR values
            from analysis.indicators import calculate_atr
            atr_values = calculate_atr(high_prices, low_prices, close_prices)

            # Check if market is volatile
            volatile = is_market_volatile(atr_values)

            if volatile:
                logger.info("Market is volatile, using reduced risk percentage")
                return self.volatile_risk_pct
            else:
                logger.info("Market is normal, using standard risk percentage")
                return self.normal_risk_pct
        except Exception as e:
            logger.error(f"Error determining risk percentage: {e}")
            return self.volatile_risk_pct  # Conservative approach

    def calculate_position_size(self, entry_price, stop_loss_price, market_data, open_positions=None, quote_currency="USDT"):
        """
        Calculate position size based on risk management rules

        Args:
            entry_price (float): Entry price
            stop_loss_price (float): Stop loss price
            market_data (dict): Market data including indicators
            open_positions (list): List of open positions
            quote_currency (str): Quote currency (e.g., USDT)

        Returns:
            float: Position size in base currency
        """
        try:
            # Check if trading is allowed
            if not self.can_trade(quote_currency):
                return 0.0

            # Get available balance
            available_balance = self.get_available_balance(quote_currency)

            # Calculate total risk from open positions
            if open_positions is None:
                open_positions = []

            total_risk_pct = calculate_total_risk(open_positions, available_balance)

            # Check if total risk exceeds maximum
            if total_risk_pct >= self.max_risk_pct:
                logger.warning(f"Total risk ({total_risk_pct:.2f}%) exceeds maximum ({self.max_risk_pct}%)")
                return 0.0

            # Calculate remaining risk capacity
            remaining_risk_pct = self.max_risk_pct - total_risk_pct

            # Get risk percentage based on market conditions
            risk_pct = self.get_risk_percentage(market_data)

            # Adjust risk percentage if it exceeds remaining capacity
            risk_pct = min(risk_pct, remaining_risk_pct)

            # Calculate position size
            position_size = calculate_position_size(available_balance, risk_pct, entry_price, stop_loss_price)

            logger.info(f"Calculated position size: {position_size:.8f} at entry price: ${entry_price:.2f}, stop loss: ${stop_loss_price:.2f}, risk: {risk_pct:.2f}%")
            return position_size
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0
