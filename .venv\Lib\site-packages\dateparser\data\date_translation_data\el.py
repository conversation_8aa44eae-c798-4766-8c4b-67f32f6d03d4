info = {
    "name": "el",
    "date_order": "DM<PERSON>",
    "january": [
        "ιαν",
        "ιανουάριος",
        "ιανουαρίου"
    ],
    "february": [
        "φεβ",
        "φεβρουάριος",
        "φεβρουαρίου"
    ],
    "march": [
        "μάρ",
        "μάρτιος",
        "μαρ",
        "μαρτίου"
    ],
    "april": [
        "απρ",
        "απρίλιος",
        "απριλίου"
    ],
    "may": [
        "μάι",
        "μάιος",
        "μαΐ",
        "μαΐου"
    ],
    "june": [
        "ιουν",
        "ιουνίου",
        "ιούν",
        "ιούνιος"
    ],
    "july": [
        "ιουλ",
        "ιουλίου",
        "ιούλ",
        "ιούλιος"
    ],
    "august": [
        "αυγ",
        "αυγούστου",
        "αύγ",
        "αύγουστος"
    ],
    "september": [
        "σεπ",
        "σεπτέμβριος",
        "σεπτεμβρίου"
    ],
    "october": [
        "οκτ",
        "οκτωβρίου",
        "οκτώβριος"
    ],
    "november": [
        "νοέ",
        "νοέμβριος",
        "νοε",
        "νοεμβρίου"
    ],
    "december": [
        "δεκ",
        "δεκέμβριος",
        "δεκεμβρίου"
    ],
    "monday": [
        "δευ",
        "δευτέρα"
    ],
    "tuesday": [
        "τρί",
        "τρίτη"
    ],
    "wednesday": [
        "τετ",
        "τετάρτη"
    ],
    "thursday": [
        "πέμ",
        "πέμπτη"
    ],
    "friday": [
        "παρ",
        "παρασκευή"
    ],
    "saturday": [
        "σάβ",
        "σάββατο"
    ],
    "sunday": [
        "κυρ",
        "κυριακή"
    ],
    "am": [
        "πμ"
    ],
    "pm": [
        "μμ"
    ],
    "year": [
        "έτ",
        "έτος"
    ],
    "month": [
        "μήν",
        "μήνας"
    ],
    "week": [
        "εβδ",
        "εβδομάδα"
    ],
    "day": [
        "ημέρα"
    ],
    "hour": [
        "ώ",
        "ώρ",
        "ώρα"
    ],
    "minute": [
        "λ",
        "λεπ",
        "λεπτό"
    ],
    "second": [
        "δ",
        "δευτ",
        "δευτερόλεπτο"
    ],
    "relative-type": {
        "0 day ago": [
            "σήμερα"
        ],
        "0 hour ago": [
            "αυτήν την ώρα"
        ],
        "0 minute ago": [
            "αυτό το λεπτό"
        ],
        "0 month ago": [
            "τρέχων μήνας"
        ],
        "0 second ago": [
            "τώρα"
        ],
        "0 week ago": [
            "αυτήν την εβδομάδα"
        ],
        "0 year ago": [
            "φέτος"
        ],
        "1 day ago": [
            "χθες"
        ],
        "1 month ago": [
            "προηγούμενος μήνας"
        ],
        "1 week ago": [
            "προηγούμενη εβδομάδα"
        ],
        "1 year ago": [
            "πέρσι"
        ],
        "in 1 day": [
            "αύριο"
        ],
        "in 1 month": [
            "επόμενος μήνας"
        ],
        "in 1 week": [
            "επόμενη εβδομάδα"
        ],
        "in 1 year": [
            "επόμενο έτος"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) ημ πριν",
            "πριν από (\\d+[.,]?\\d*) ημέρα",
            "πριν από (\\d+[.,]?\\d*) ημέρες"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) ώ πριν",
            "πριν από (\\d+[.,]?\\d*) ώρ",
            "πριν από (\\d+[.,]?\\d*) ώρα",
            "πριν από (\\d+[.,]?\\d*) ώρες"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) λ πριν",
            "πριν από (\\d+[.,]?\\d*) λεπ",
            "πριν από (\\d+[.,]?\\d*) λεπτά",
            "πριν από (\\d+[.,]?\\d*) λεπτό"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) μ πριν",
            "πριν από (\\d+[.,]?\\d*) μήνα",
            "πριν από (\\d+[.,]?\\d*) μήνες"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) δ πριν",
            "πριν από (\\d+[.,]?\\d*) δευτ",
            "πριν από (\\d+[.,]?\\d*) δευτερόλεπτα",
            "πριν από (\\d+[.,]?\\d*) δευτερόλεπτο"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) εβδ πριν",
            "πριν από (\\d+[.,]?\\d*) εβδ",
            "πριν από (\\d+[.,]?\\d*) εβδομάδα",
            "πριν από (\\d+[.,]?\\d*) εβδομάδες"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) έτη πριν",
            "(\\d+[.,]?\\d*) έτος πριν",
            "πριν από (\\d+[.,]?\\d*) έτη",
            "πριν από (\\d+[.,]?\\d*) έτος"
        ],
        "in \\1 day": [
            "σε (\\d+[.,]?\\d*) ημ",
            "σε (\\d+[.,]?\\d*) ημέρα",
            "σε (\\d+[.,]?\\d*) ημέρες"
        ],
        "in \\1 hour": [
            "σε (\\d+[.,]?\\d*) ώ",
            "σε (\\d+[.,]?\\d*) ώρ",
            "σε (\\d+[.,]?\\d*) ώρα",
            "σε (\\d+[.,]?\\d*) ώρες"
        ],
        "in \\1 minute": [
            "σε (\\d+[.,]?\\d*) λ",
            "σε (\\d+[.,]?\\d*) λεπ",
            "σε (\\d+[.,]?\\d*) λεπτά",
            "σε (\\d+[.,]?\\d*) λεπτό"
        ],
        "in \\1 month": [
            "σε (\\d+[.,]?\\d*) μ",
            "σε (\\d+[.,]?\\d*) μήνα",
            "σε (\\d+[.,]?\\d*) μήνες"
        ],
        "in \\1 second": [
            "σε (\\d+[.,]?\\d*) δ",
            "σε (\\d+[.,]?\\d*) δευτ",
            "σε (\\d+[.,]?\\d*) δευτερόλεπτα",
            "σε (\\d+[.,]?\\d*) δευτερόλεπτο"
        ],
        "in \\1 week": [
            "σε (\\d+[.,]?\\d*) εβδ",
            "σε (\\d+[.,]?\\d*) εβδομάδα",
            "σε (\\d+[.,]?\\d*) εβδομάδες"
        ],
        "in \\1 year": [
            "σε (\\d+[.,]?\\d*) έτη",
            "σε (\\d+[.,]?\\d*) έτος"
        ]
    },
    "locale_specific": {
        "el-CY": {
            "name": "el-CY"
        }
    },
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
