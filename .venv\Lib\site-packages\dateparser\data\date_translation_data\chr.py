info = {
    "name": "chr",
    "date_order": "MDY",
    "january": [
        "ꭴꮓ",
        "ꭴꮓꮈꮤꮕ"
    ],
    "february": [
        "ꭷꭶ",
        "ꭷꭶꮅ"
    ],
    "march": [
        "ꭰꮕ",
        "ꭰꮕᏹ"
    ],
    "april": [
        "ꭷꮼ",
        "ꭷꮼꮒ"
    ],
    "may": [
        "ꭰꮒ",
        "ꭰꮒꮝꭼꮨ"
    ],
    "june": [
        "ꮥꭽ",
        "ꮥꭽꮇᏹ"
    ],
    "july": [
        "ꭻᏸ",
        "ꭻᏸꮙꮒ"
    ],
    "august": [
        "ꭶꮆ",
        "ꭶꮆꮒ"
    ],
    "september": [
        "ꮪꮅ",
        "ꮪꮅꮝꮧ"
    ],
    "october": [
        "ꮪꮒ",
        "ꮪꮒꮕꮧ"
    ],
    "november": [
        "ꮕꮣ",
        "ꮕꮣꮥꮖ"
    ],
    "december": [
        "ꭵꮝ",
        "ꭵꮝꭹᏹ"
    ],
    "monday": [
        "ꭴꮎꮩꮣꮙꮕꭿ",
        "ꮙꮕꭿ"
    ],
    "tuesday": [
        "ꮤꮅꮑ",
        "ꮤꮅꮑꭲꭶ"
    ],
    "wednesday": [
        "ꮶꭲꮑ",
        "ꮶꭲꮑꭲꭶ"
    ],
    "thursday": [
        "ꮕꭹꮑ",
        "ꮕꭹꮑꭲꭶ"
    ],
    "friday": [
        "ꮷꮎꭹ",
        "ꮷꮎꭹꮆꮝꮧ"
    ],
    "saturday": [
        "ꭴꮎꮩꮣꮘꮥꮎ",
        "ꮘꮥꮎ"
    ],
    "sunday": [
        "ꭴꮎꮩꮣꮖꮝꭼ",
        "ꮖꮝꭼ"
    ],
    "am": [
        "ꮜꮎꮄ"
    ],
    "pm": [
        "ꮢꭿᏹꭲꮧꮲ"
    ],
    "year": [
        "ꭴꮥ",
        "ꭴꮥꮨᏼꮜꮧꮢꭲ"
    ],
    "month": [
        "ꭷꮈ",
        "ꭷꮈꭲ"
    ],
    "week": [
        "ꮢꮎ",
        "ꮢꮎꮩꮣꮖꮝꮧ"
    ],
    "day": [
        "ꭲꭶ"
    ],
    "hour": [
        "ꮡꮯ",
        "ꮡꮯꮆꮣ"
    ],
    "minute": [
        "ꭲꮿꮤ",
        "ꭲꮿꮤꮼꮝꮤꮕ"
    ],
    "second": [
        "ꭰꮞꮲ"
    ],
    "relative-type": {
        "0 day ago": [
            "ꭺꭿ ꭲꭶ"
        ],
        "0 hour ago": [
            "ꭿꭰ ꮡꮯꮆꮣ"
        ],
        "0 minute ago": [
            "ꭿꭰ ꭲꮿꮤꮼꮝꮤꮕ"
        ],
        "0 month ago": [
            "ꭿꭰ ꭷꮈꭲ"
        ],
        "0 second ago": [
            "ꮓꮚ"
        ],
        "0 week ago": [
            "ꭿꭰ ꭰꮅꮅꮜ"
        ],
        "0 year ago": [
            "ꭿꭰ ꮷꮥꮨᏼꮢꮨ"
        ],
        "1 day ago": [
            "ꮢꭿ"
        ],
        "1 month ago": [
            "ꭷꮈꭲ ꮵꭸꮢ"
        ],
        "1 week ago": [
            "ꮵꮫꮅᏹꮅꮢꭲ"
        ],
        "1 year ago": [
            "ꭱꮨ ꮵꭸꮢ"
        ],
        "in 1 day": [
            "ꮜꮎꮄꭲ"
        ],
        "in 1 month": [
            "ꮤꮅꮑ ꭷꮈꭲ"
        ],
        "in 1 week": [
            "ꮠꮖꮄꮕꮂ"
        ],
        "in 1 year": [
            "ꭱꮨᏼꭲ"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) ꭲꭶ ꮵꭸꮢ",
            "(\\d+[.,]?\\d*) ꭿꮈꮝꭹ ꮷꮢꭿꮫ ꮵꭸꮢ"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) ꭲᏻꮯꮆꮣ ꮵꭸꮢ",
            "(\\d+[.,]?\\d*) ꮡꮯꮆꮣ ꮵꭸꮢ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮡꮯ ꮵꭸꮢ"
        ],
        "\\1 minute ago": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭲꮿꮤ ꮵꭸꮢ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꭲꮿꮤꮼꮝꮤꮕ ꮵꭸꮢ"
        ],
        "\\1 month ago": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭷꮈ ꮵꭸꮢ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꭷꮈꭲ ꮵꭸꮢ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮧꭷꮈꭲ ꮵꭸꮢ"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) ꭰꮞꮲ ꮵꭸꮢ",
            "(\\d+[.,]?\\d*) ꮣꮣꮎꮹꮝꭼ ꮵꭸꮢ"
        ],
        "\\1 week ago": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭲᏻꮎꮩꮣꮖꮝꮧ ꮵꭸꮢ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮢꮎ ꮵꭸꮢ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮢꮎꮩꮣꮖꮝꮧ ꮵꭸꮢ"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) ꭲꮷꮥꮨᏼꮜꮧꮢꭲ ꮵꭸꮢ",
            "(\\d+[.,]?\\d*) ꭴꮥꮨᏼꮜꮧꮢꭲ ꮵꭸꮢ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꭴꮥ ꮵꭸꮢ"
        ],
        "in \\1 day": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭲꭶ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꭿꮈꮝꭹ ꮷꮢꭿꮫ"
        ],
        "in \\1 hour": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭲᏻꮯꮆꮣ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮡꮯ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮡꮯꮆꮣ"
        ],
        "in \\1 minute": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭲꮿꮤ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꭲꮿꮤꮼꮝꮤꮕ"
        ],
        "in \\1 month": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭷꮈ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꭷꮈꭲ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮧꭷꮈꭲ"
        ],
        "in \\1 second": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭰꮞꮲ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮣꮣꮎꮹꮝꭼ ꮵꭸꮢ"
        ],
        "in \\1 week": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭲᏻꮎꮩꮣꮖꮝꮧ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮢꮎ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꮢꮎꮩꮣꮖꮝꮧ"
        ],
        "in \\1 year": [
            "ꮎꮏ (\\d+[.,]?\\d*) ꭲꮷꮥꮨᏼꮜꮧꮢꭲ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꭴꮥ",
            "ꮎꮏ (\\d+[.,]?\\d*) ꭴꮥꮨᏼꮜꮧꮢꭲ"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
