"""
Volatility Detector Module
Detects sudden volatility in the market
"""

import os
import logging
import datetime
from analysis.indicators import calculate_atr, calculate_roc, is_atr_rising_rapidly

# Set up logging
logger = logging.getLogger("volatility_detector")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/volatility_detector.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class VolatilityDetector:
    """
    Detects sudden volatility in the market
    """

    def __init__(self, atr_threshold=1.5, roc_threshold=2.0, instant_atr_threshold=50.0, instant_roc_threshold=2.0):
        """
        Initialize the volatility detector

        Args:
            atr_threshold (float): Threshold for ATR increase (percentage)
            roc_threshold (float): Threshold for ROC (percentage)
            instant_atr_threshold (float): Threshold for instant ATR increase (percentage) within 5 minutes
            instant_roc_threshold (float): Threshold for instant ROC (percentage) within a minute
        """
        self.atr_threshold = atr_threshold
        self.roc_threshold = roc_threshold
        self.instant_atr_threshold = instant_atr_threshold  # 50% increase in ATR within 5 minutes
        self.instant_roc_threshold = instant_roc_threshold  # ±2% price change within a minute
        self.last_check_time = None
        self.last_atr_values = {}
        self.last_prices = {}
        self.atr_history = {}  # Store ATR values with timestamps for 5-minute checks
        self.price_history = {}  # Store price values with timestamps for 1-minute checks
        self.trading_suspended_until = {}  # Store timestamp until which trading is suspended

        logger.info(f"VolatilityDetector initialized with ATR threshold: {atr_threshold}%, ROC threshold: {roc_threshold}%, "
                   f"Instant ATR threshold: {instant_atr_threshold}%, Instant ROC threshold: {instant_roc_threshold}%")

    def check_volatility(self, symbol, market_data):
        """
        Check for sudden volatility

        Args:
            symbol (str): Trading pair symbol
            market_data (dict): Market data

        Returns:
            dict: Volatility information
        """
        try:
            current_time = datetime.datetime.now()

            # Check if trading is suspended for this symbol
            if symbol in self.trading_suspended_until and current_time < self.trading_suspended_until[symbol]:
                suspension_remaining = (self.trading_suspended_until[symbol] - current_time).total_seconds() / 60
                logger.warning(f"Trading for {symbol} is suspended for {suspension_remaining:.1f} more minutes due to high volatility")
                return {
                    'symbol': symbol,
                    'is_volatile': True,
                    'reason': f"Trading suspended for {suspension_remaining:.1f} more minutes",
                    'atr_change': 0,
                    'roc': 0,
                    'timestamp': current_time.isoformat(),
                    'trading_suspended': True,
                    'suspension_end': self.trading_suspended_until[symbol].isoformat()
                }

            # Initialize result
            result = {
                'symbol': symbol,
                'is_volatile': False,
                'reason': None,
                'atr_change': 0,
                'roc': 0,
                'timestamp': current_time.isoformat(),
                'trading_suspended': False,
                'suspension_end': None
            }

            # Extract data
            high_prices = market_data.get('high_prices', [])
            low_prices = market_data.get('low_prices', [])
            close_prices = market_data.get('close_prices', [])

            if not high_prices or not low_prices or not close_prices:
                logger.warning(f"Insufficient price data for {symbol}")
                return result

            # Calculate ATR
            atr_values = calculate_atr(high_prices, low_prices, close_prices)

            # Calculate ROC
            roc_values = calculate_roc(close_prices)

            # Get current values
            current_atr = atr_values[-1] if atr_values and len(atr_values) > 0 else 0
            current_price = close_prices[-1] if close_prices and len(close_prices) > 0 else 0

            # Store current ATR and price with timestamp
            if symbol not in self.atr_history:
                self.atr_history[symbol] = []
            if symbol not in self.price_history:
                self.price_history[symbol] = []

            self.atr_history[symbol].append((current_time, current_atr))
            self.price_history[symbol].append((current_time, current_price))

            # Keep only the last 10 minutes of data
            ten_minutes_ago = current_time - datetime.timedelta(minutes=10)
            self.atr_history[symbol] = [item for item in self.atr_history[symbol] if item[0] >= ten_minutes_ago]
            self.price_history[symbol] = [item for item in self.price_history[symbol] if item[0] >= ten_minutes_ago]

            # Check for instant ATR rise (50% within 5 minutes)
            five_minutes_ago = current_time - datetime.timedelta(minutes=5)
            atr_five_min_ago = next((atr for ts, atr in self.atr_history[symbol] if ts <= five_minutes_ago), None)

            if atr_five_min_ago and atr_five_min_ago > 0:
                instant_atr_change = ((current_atr / atr_five_min_ago) - 1) * 100
                result['instant_atr_change'] = instant_atr_change

                if instant_atr_change > self.instant_atr_threshold:
                    result['is_volatile'] = True
                    result['reason'] = f"ATR increased by {instant_atr_change:.2f}% within 5 minutes (threshold: {self.instant_atr_threshold}%)"
                    logger.warning(f"Sudden volatility detected for {symbol}: ATR increased by {instant_atr_change:.2f}% within 5 minutes")

                    # Suspend trading for 5 minutes
                    self.trading_suspended_until[symbol] = current_time + datetime.timedelta(minutes=5)
                    result['trading_suspended'] = True
                    result['suspension_end'] = self.trading_suspended_until[symbol].isoformat()

                    return result

            # Check for instant ROC (±2% within a minute)
            one_minute_ago = current_time - datetime.timedelta(minutes=1)
            price_one_min_ago = next((price for ts, price in self.price_history[symbol] if ts <= one_minute_ago), None)

            if price_one_min_ago and price_one_min_ago > 0:
                instant_roc = ((current_price / price_one_min_ago) - 1) * 100
                result['instant_roc'] = instant_roc

                if abs(instant_roc) > self.instant_roc_threshold:
                    result['is_volatile'] = True
                    result['reason'] = f"Price changed by {instant_roc:.2f}% within a minute (threshold: ±{self.instant_roc_threshold}%)"
                    logger.warning(f"Sudden volatility detected for {symbol}: Price changed by {instant_roc:.2f}% within a minute")

                    # Suspend trading for 5 minutes
                    self.trading_suspended_until[symbol] = current_time + datetime.timedelta(minutes=5)
                    result['trading_suspended'] = True
                    result['suspension_end'] = self.trading_suspended_until[symbol].isoformat()

                    return result

            # Check if we have previous values for regular volatility checks
            if symbol in self.last_atr_values and symbol in self.last_prices:
                # Check ATR change
                last_atr = self.last_atr_values[symbol]

                if last_atr > 0:
                    atr_change_percent = ((current_atr / last_atr) - 1) * 100
                    result['atr_change'] = atr_change_percent

                    if atr_change_percent > self.atr_threshold:
                        result['is_volatile'] = True
                        result['reason'] = f"ATR increased by {atr_change_percent:.2f}% (threshold: {self.atr_threshold}%)"
                        logger.warning(f"High volatility detected for {symbol}: ATR increased by {atr_change_percent:.2f}%")

                # Check ROC
                last_price = self.last_prices[symbol]

                if last_price > 0:
                    roc_percent = ((current_price / last_price) - 1) * 100
                    result['roc'] = roc_percent

                    if abs(roc_percent) > self.roc_threshold:
                        result['is_volatile'] = True
                        result['reason'] = f"Price changed by {roc_percent:.2f}% (threshold: {self.roc_threshold}%)"
                        logger.warning(f"High volatility detected for {symbol}: Price changed by {roc_percent:.2f}%")

                # Check if ATR is rising rapidly
                if len(atr_values) >= 5:
                    if is_atr_rising_rapidly(atr_values):
                        result['is_volatile'] = True
                        result['reason'] = "ATR rising rapidly"
                        logger.warning(f"High volatility detected for {symbol}: ATR rising rapidly")

            # Update last values
            self.last_atr_values[symbol] = current_atr
            self.last_prices[symbol] = current_price
            self.last_check_time = current_time

            return result
        except Exception as e:
            logger.error(f"Error checking volatility: {e}")
            current_time = datetime.datetime.now()
            return {
                'symbol': symbol,
                'is_volatile': True,  # Conservative approach: assume volatile if error
                'reason': f"Error: {str(e)}",
                'atr_change': 0,
                'roc': 0,
                'instant_atr_change': 0,
                'instant_roc': 0,
                'timestamp': current_time.isoformat(),
                'trading_suspended': True,
                'suspension_end': (current_time + datetime.timedelta(minutes=5)).isoformat()
            }
