import os
import sys
import time
import json
import logging
import datetime
import random
import numpy as np
import pandas as pd

# Import the indicators module
from analysis.indicators import check_smart_entry_conditions

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("test_smart_entry")

def generate_mock_market_data(candles=100):
    """
    Generate mock market data for testing
    """
    # Generate random price data
    base_price = 60000
    historical = []
    
    for i in range(candles):
        # Generate random price movement
        price_change = random.uniform(-0.02, 0.02)
        price = base_price * (1 + price_change)
        
        # Generate candle data
        candle = {
            "timestamp": (datetime.datetime.now() - datetime.timedelta(hours=candles-i)).isoformat(),
            "open": price * (1 + random.uniform(-0.005, 0.005)),
            "high": price * (1 + random.uniform(0, 0.01)),
            "low": price * (1 - random.uniform(0, 0.01)),
            "close": price,
            "volume": random.uniform(100, 1000)
        }
        
        historical.append(candle)
        
        # Update base price for next candle
        base_price = price
    
    # Create market data
    market_data = {
        "symbol": "BTC/USDT",
        "price": historical[-1]["close"],
        "timestamp": datetime.datetime.now().isoformat(),
        "historical": historical
    }
    
    return market_data

def test_smart_entry_conditions():
    """
    Test the smart entry conditions
    """
    # Generate mock market data
    market_data = generate_mock_market_data(candles=250)
    
    # Check smart entry conditions
    entry_conditions = check_smart_entry_conditions(market_data)
    
    # Print results
    logger.info(f"Smart entry conditions: Buy score: {entry_conditions['buy_score']:.1f}%, Sell score: {entry_conditions['sell_score']:.1f}%")
    logger.info(f"Smart entry signals: Buy: {entry_conditions['buy_signal']}, Sell: {entry_conditions['sell_signal']}")
    
    # Print individual conditions
    logger.info("Buy conditions:")
    for condition, value in entry_conditions["buy_conditions"].items():
        logger.info(f"  {condition}: {value}")
    
    logger.info("Sell conditions:")
    for condition, value in entry_conditions["sell_conditions"].items():
        logger.info(f"  {condition}: {value}")
    
    return entry_conditions

if __name__ == "__main__":
    test_smart_entry_conditions()
