#!/usr/bin/env python3
"""
SP.Bot Enhanced v2.0.0 - API Key Validation Script

This script validates all provided API keys to ensure they work correctly.
"""

import os
import sys
import logging
import requests
import time
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_environment():
    """Load environment variables"""
    load_dotenv()
    logger.info("✅ Environment variables loaded")

def test_openai_keys():
    """Test OpenAI API keys"""
    logger.info("🧪 Testing OpenAI API keys...")
    
    working_keys = 0
    for i in range(1, 4):
        api_key = os.getenv(f'OPENAI_API_KEY_{i}')
        if not api_key:
            logger.warning(f"⚠️ OpenAI API key {i} not found")
            continue
        
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            # Test with a simple request
            response = requests.get(
                'https://api.openai.com/v1/models',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"✅ OpenAI API key {i} is valid")
                working_keys += 1
            else:
                logger.error(f"❌ OpenAI API key {i} failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ OpenAI API key {i} error: {e}")
        
        time.sleep(1)  # Rate limiting
    
    logger.info(f"📊 OpenAI working keys: {working_keys}/3")
    return working_keys

def test_deepseek_keys():
    """Test DeepSeek API keys"""
    logger.info("🧪 Testing DeepSeek API keys...")
    
    working_keys = 0
    for i in range(1, 4):
        api_key = os.getenv(f'DEEPSEEK_API_KEY_{i}')
        if not api_key:
            logger.warning(f"⚠️ DeepSeek API key {i} not found")
            continue
        
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            # Test with a simple chat completion
            data = {
                'model': 'deepseek-chat',
                'messages': [{'role': 'user', 'content': 'Hello'}],
                'max_tokens': 10
            }
            
            response = requests.post(
                'https://api.deepseek.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"✅ DeepSeek API key {i} is valid")
                working_keys += 1
            else:
                logger.error(f"❌ DeepSeek API key {i} failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ DeepSeek API key {i} error: {e}")
        
        time.sleep(1)  # Rate limiting
    
    logger.info(f"📊 DeepSeek working keys: {working_keys}/3")
    return working_keys

def test_qwen_keys():
    """Test Qwen AI API keys"""
    logger.info("🧪 Testing Qwen AI API keys...")
    
    working_keys = 0
    for i in range(1, 4):
        api_key = os.getenv(f'QWEN_API_KEY_{i}')
        if not api_key:
            logger.warning(f"⚠️ Qwen API key {i} not found")
            continue
        
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            # Test with a simple chat completion
            data = {
                'model': 'qwen-turbo',
                'messages': [{'role': 'user', 'content': 'Hello'}],
                'max_tokens': 10
            }
            
            response = requests.post(
                'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Qwen API key {i} is valid")
                working_keys += 1
            else:
                logger.error(f"❌ Qwen API key {i} failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ Qwen API key {i} error: {e}")
        
        time.sleep(1)  # Rate limiting
    
    logger.info(f"📊 Qwen working keys: {working_keys}/3")
    return working_keys

def test_binance_keys():
    """Test Binance API keys"""
    logger.info("🧪 Testing Binance API keys...")
    
    # Test live keys
    live_key = os.getenv('BINANCE_API_KEY')
    live_secret = os.getenv('BINANCE_SECRET')
    
    if live_key and live_secret:
        logger.info("✅ Binance LIVE API keys found")
        # Note: We won't test live keys to avoid unnecessary API calls
        # They will be tested when the bot starts
    else:
        logger.warning("⚠️ Binance LIVE API keys not found")
    
    # Test testnet keys
    test_key = os.getenv('BINANCE_TESTNET_API_KEY')
    test_secret = os.getenv('BINANCE_TESTNET_SECRET')
    
    if test_key and test_secret:
        logger.info("✅ Binance TESTNET API keys found")
        try:
            # Simple ping test for testnet
            response = requests.get('https://testnet.binance.vision/api/v3/ping', timeout=10)
            if response.status_code == 200:
                logger.info("✅ Binance testnet is accessible")
            else:
                logger.warning("⚠️ Binance testnet ping failed")
        except Exception as e:
            logger.error(f"❌ Binance testnet error: {e}")
    else:
        logger.warning("⚠️ Binance TESTNET API keys not found")

def main():
    """Main validation function"""
    logger.info("=" * 60)
    logger.info("🔍 SP.Bot Enhanced v2.0.0 - API Key Validation")
    logger.info("=" * 60)
    
    # Load environment
    load_environment()
    
    # Test all API keys
    openai_working = test_openai_keys()
    deepseek_working = test_deepseek_keys()
    qwen_working = test_qwen_keys()
    test_binance_keys()
    
    # Summary
    total_ai_keys = openai_working + deepseek_working + qwen_working
    
    logger.info("=" * 60)
    logger.info("📊 VALIDATION SUMMARY")
    logger.info("=" * 60)
    logger.info(f"OpenAI working keys: {openai_working}/3")
    logger.info(f"DeepSeek working keys: {deepseek_working}/3")
    logger.info(f"Qwen working keys: {qwen_working}/3")
    logger.info(f"Total AI keys working: {total_ai_keys}/9")
    
    if total_ai_keys >= 6:
        logger.info("🎉 EXCELLENT! Sufficient API keys for trading")
        logger.info("✅ Bot is ready to start with multiple AI models")
    elif total_ai_keys >= 3:
        logger.info("🟡 GOOD! Minimum API keys available")
        logger.info("⚠️ Consider adding more keys for better redundancy")
    else:
        logger.error("🔴 INSUFFICIENT API keys for reliable trading")
        logger.error("❌ Need at least 3 working AI API keys")
        return False
    
    logger.info("\n🚀 Ready to start SP.Bot Enhanced v2.0.0!")
    logger.info("Run: python start_bot_with_keys.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
