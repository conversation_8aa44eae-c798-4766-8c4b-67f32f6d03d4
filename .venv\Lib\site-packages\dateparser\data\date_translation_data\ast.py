info = {
    "name": "ast",
    "date_order": "DMY",
    "january": [
        "de xineru",
        "xin",
        "xineru"
    ],
    "february": [
        "de febreru",
        "feb",
        "febreru"
    ],
    "march": [
        "de marzu",
        "mar",
        "marzu"
    ],
    "april": [
        "abr",
        "abril",
        "d'abril"
    ],
    "may": [
        "de mayu",
        "may",
        "mayu"
    ],
    "june": [
        "de xunu",
        "xun",
        "xunu"
    ],
    "july": [
        "de xunetu",
        "xnt",
        "xunetu"
    ],
    "august": [
        "ago",
        "agostu",
        "d'agostu"
    ],
    "september": [
        "de setiembre",
        "set",
        "setiembre"
    ],
    "october": [
        "d'ochobre",
        "och",
        "ochobre"
    ],
    "november": [
        "de payares",
        "pay",
        "payares"
    ],
    "december": [
        "avi",
        "avientu",
        "d'avientu"
    ],
    "monday": [
        "llu",
        "llunes"
    ],
    "tuesday": [
        "mar",
        "martes"
    ],
    "wednesday": [
        "mié",
        "miércoles"
    ],
    "thursday": [
        "xue",
        "xueves"
    ],
    "friday": [
        "vie",
        "vienres"
    ],
    "saturday": [
        "sáb",
        "sábadu"
    ],
    "sunday": [
        "dom",
        "domingu"
    ],
    "am": [
        "am",
        "de la mañana",
        "mañana"
    ],
    "pm": [
        "de la tarde",
        "pm",
        "tarde"
    ],
    "year": [
        "añu"
    ],
    "month": [
        "mes"
    ],
    "week": [
        "sel",
        "selmana"
    ],
    "day": [
        "día"
    ],
    "hour": [
        "h",
        "hora"
    ],
    "minute": [
        "m",
        "min",
        "minutu"
    ],
    "second": [
        "s",
        "segundu"
    ],
    "relative-type": {
        "0 day ago": [
            "güei"
        ],
        "0 hour ago": [
            "esta h",
            "esta hora"
        ],
        "0 minute ago": [
            "esti min",
            "esti minutu"
        ],
        "0 month ago": [
            "esti mes"
        ],
        "0 second ago": [
            "agora"
        ],
        "0 week ago": [
            "esta selm",
            "esta selmana"
        ],
        "0 year ago": [
            "esti añu"
        ],
        "1 day ago": [
            "ayeri"
        ],
        "1 month ago": [
            "el mes pasáu",
            "mes pas"
        ],
        "1 week ago": [
            "la selmana pasada",
            "selm pas",
            "selm pasada"
        ],
        "1 year ago": [
            "añu pas",
            "l'añu pas",
            "l'añu pasáu"
        ],
        "in 1 day": [
            "mañ",
            "mañana"
        ],
        "in 1 month": [
            "el mes viniente",
            "mes vin"
        ],
        "in 1 week": [
            "la selmana viniente",
            "selm vin",
            "selm viniente"
        ],
        "in 1 year": [
            "añu vin",
            "l'añu vin",
            "l'añu viniente"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "hai (\\d+[.,]?\\d*) d",
            "hai (\\d+[.,]?\\d*) día",
            "hai (\\d+[.,]?\\d*) díes"
        ],
        "\\1 hour ago": [
            "hai (\\d+[.,]?\\d*) h",
            "hai (\\d+[.,]?\\d*) hora",
            "hai (\\d+[.,]?\\d*) hores"
        ],
        "\\1 minute ago": [
            "hai (\\d+[.,]?\\d*) min",
            "hai (\\d+[.,]?\\d*) minutos",
            "hai (\\d+[.,]?\\d*) minutu"
        ],
        "\\1 month ago": [
            "hai (\\d+[.,]?\\d*) m",
            "hai (\\d+[.,]?\\d*) mes",
            "hai (\\d+[.,]?\\d*) meses"
        ],
        "\\1 second ago": [
            "hai (\\d+[.,]?\\d*) s",
            "hai (\\d+[.,]?\\d*) seg",
            "hai (\\d+[.,]?\\d*) segundos",
            "hai (\\d+[.,]?\\d*) segundu"
        ],
        "\\1 week ago": [
            "hai (\\d+[.,]?\\d*) se",
            "hai (\\d+[.,]?\\d*) selm",
            "hai (\\d+[.,]?\\d*) selmana",
            "hai (\\d+[.,]?\\d*) selmanes"
        ],
        "\\1 year ago": [
            "hai (\\d+[.,]?\\d*) a",
            "hai (\\d+[.,]?\\d*) años",
            "hai (\\d+[.,]?\\d*) añu"
        ],
        "in \\1 day": [
            "en (\\d+[.,]?\\d*) d",
            "en (\\d+[.,]?\\d*) día",
            "en (\\d+[.,]?\\d*) díes"
        ],
        "in \\1 hour": [
            "en (\\d+[.,]?\\d*) h",
            "en (\\d+[.,]?\\d*) hora",
            "en (\\d+[.,]?\\d*) hores"
        ],
        "in \\1 minute": [
            "en (\\d+[.,]?\\d*) min",
            "en (\\d+[.,]?\\d*) minutos",
            "en (\\d+[.,]?\\d*) minutu"
        ],
        "in \\1 month": [
            "en (\\d+[.,]?\\d*) m",
            "en (\\d+[.,]?\\d*) mes",
            "en (\\d+[.,]?\\d*) meses"
        ],
        "in \\1 second": [
            "en (\\d+[.,]?\\d*) s",
            "en (\\d+[.,]?\\d*) seg",
            "en (\\d+[.,]?\\d*) segundos",
            "en (\\d+[.,]?\\d*) segundu"
        ],
        "in \\1 week": [
            "en (\\d+[.,]?\\d*) se",
            "en (\\d+[.,]?\\d*) selm",
            "en (\\d+[.,]?\\d*) selmana",
            "en (\\d+[.,]?\\d*) selmanes"
        ],
        "in \\1 year": [
            "en (\\d+[.,]?\\d*) a",
            "en (\\d+[.,]?\\d*) años",
            "en (\\d+[.,]?\\d*) añu"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
