"""
Initialize Key Manager with API Keys from .env file
"""

import os
import logging
from dotenv import load_dotenv
from security.key_manager import KeyManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/security.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("initialize_keys")

def initialize_keys():
    """
    Initialize the key manager with API keys from the .env file
    """
    # Load environment variables
    load_dotenv()

    # Get master password
    master_password = os.getenv("MASTER_PASSWORD")

    # Initialize key manager
    key_manager = KeyManager(master_password=master_password)

    # Store Binance API keys
    binance_api_key = os.getenv("BINANCE_API_KEY")
    binance_api_secret = os.getenv("BINANCE_API_SECRET")

    if binance_api_key and binance_api_secret:
        key_manager.store_key(
            service="binance",
            key_id="main",
            key_value=binance_api_key,
            secret=binance_api_secret
        )
        logger.info("Stored Binance API keys")
    else:
        logger.warning("Binance API keys not found in .env file")

    # Store Binance Testnet API keys
    binance_testnet_api_key = os.getenv("BINANCE_TESTNET_API_KEY")
    binance_testnet_api_secret = os.getenv("BINANCE_TESTNET_API_SECRET")

    if binance_testnet_api_key and binance_testnet_api_secret:
        key_manager.store_key(
            service="binance_testnet",
            key_id="main",
            key_value=binance_testnet_api_key,
            secret=binance_testnet_api_secret
        )
        logger.info("Stored Binance Testnet API keys")
    else:
        logger.warning("Binance Testnet API keys not found in .env file")

    # Store OpenAI API keys
    for i in range(1, 4):
        openai_api_key = os.getenv(f"OPENAI_API_KEY_{i}")
        if openai_api_key:
            key_manager.store_key(
                service="openai",
                key_id=f"key{i}",
                key_value=openai_api_key
            )
            logger.info(f"Stored OpenAI API key {i}")
        else:
            logger.warning(f"OpenAI API key {i} not found in .env file")

    # Store DeepSeek API keys
    for i in range(1, 4):
        deepseek_api_key = os.getenv(f"DEEPSEEK_API_KEY_{i}")
        if deepseek_api_key:
            key_manager.store_key(
                service="deepseek",
                key_id=f"key{i}",
                key_value=deepseek_api_key
            )
            logger.info(f"Stored DeepSeek API key {i}")
        else:
            logger.warning(f"DeepSeek API key {i} not found in .env file")

    # Store Qwen API keys
    for i in range(1, 4):
        qwen_api_key = os.getenv(f"QWEN_API_KEY_{i}")
        if qwen_api_key:
            key_manager.store_key(
                service="qwen",
                key_id=f"key{i}",
                key_value=qwen_api_key
            )
            logger.info(f"Stored Qwen API key {i}")
        else:
            logger.warning(f"Qwen API key {i} not found in .env file")

    # Store Google News API key
    google_news_api_key = os.getenv("GOOGLE_NEWS_API_KEY")
    if google_news_api_key:
        key_manager.store_key(
            service="google_news",
            key_id="main",
            key_value=google_news_api_key
        )
        logger.info("Stored Google News API key")
    else:
        logger.warning("Google News API key not found in .env file")

    logger.info("Key initialization complete")

if __name__ == "__main__":
    initialize_keys()
