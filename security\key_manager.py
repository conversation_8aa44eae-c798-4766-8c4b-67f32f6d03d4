"""
Key Manager Module for Secure API Key Management
Handles encryption, decryption, and rotation of API keys
"""

import os
import json
import base64
import time
import logging
import datetime
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Set up logging
logger = logging.getLogger("key_manager")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/security.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class KeyManager:
    """
    Manages API keys with encryption, decryption, and automatic rotation
    """

    def __init__(self, master_password=None, key_file="security/keys.enc", rotation_hours=24):
        """
        Initialize the KeyManager with a master password and key file

        Args:
            master_password (str): Master password for encryption/decryption
            key_file (str): Path to the encrypted key file
            rotation_hours (int): Hours between key rotations
        """
        self.key_file = key_file
        self.rotation_hours = rotation_hours

        # Create directories if they don't exist
        os.makedirs(os.path.dirname(key_file), exist_ok=True)

        # Get or create master password
        if master_password is None:
            master_password = os.getenv("MASTER_PASSWORD")
            if master_password is None:
                # Generate a random master password if none is provided
                master_password = base64.urlsafe_b64encode(os.urandom(32)).decode()
                logger.warning("No master password provided. Generated a random one.")
                logger.warning("Please set the MASTER_PASSWORD environment variable for future use.")
                print(f"IMPORTANT: Your generated master password is: {master_password}")
                print("Please save this password securely and set it as MASTER_PASSWORD in your .env file.")

        # Generate encryption key from master password
        self.encryption_key = self._generate_key(master_password)
        self.cipher = Fernet(self.encryption_key)

        # Initialize or load keys
        self.keys = self._load_keys()

    def _generate_key(self, password):
        """
        Generate an encryption key from a password

        Args:
            password (str): Password to derive key from

        Returns:
            bytes: Encryption key
        """
        password = password.encode()
        salt = b'sp_bot_salt_value'  # In production, use a secure random salt

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )

        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key

    def _load_keys(self):
        """
        Load keys from the encrypted key file

        Returns:
            dict: Dictionary of API keys and metadata
        """
        if not os.path.exists(self.key_file):
            logger.info(f"Key file {self.key_file} not found. Creating new key store.")
            return {
                "last_rotation": datetime.datetime.now().isoformat(),
                "keys": {}
            }

        try:
            with open(self.key_file, 'rb') as f:
                encrypted_data = f.read()

            decrypted_data = self.cipher.decrypt(encrypted_data)
            keys = json.loads(decrypted_data.decode())

            # Check if keys need rotation
            last_rotation = datetime.datetime.fromisoformat(keys["last_rotation"])
            if datetime.datetime.now() - last_rotation > datetime.timedelta(hours=self.rotation_hours):
                logger.info("Keys are due for rotation.")
                self._rotate_keys(keys)

            return keys
        except Exception as e:
            logger.error(f"Error loading keys: {e}")
            return {
                "last_rotation": datetime.datetime.now().isoformat(),
                "keys": {}
            }

    def _save_keys(self):
        """
        Save keys to the encrypted key file
        """
        try:
            # Update last rotation timestamp if it doesn't exist
            if "last_rotation" not in self.keys:
                self.keys["last_rotation"] = datetime.datetime.now().isoformat()

            # Encrypt and save
            encrypted_data = self.cipher.encrypt(json.dumps(self.keys).encode())

            with open(self.key_file, 'wb') as f:
                f.write(encrypted_data)

            logger.info(f"Keys saved to {self.key_file}")
        except Exception as e:
            logger.error(f"Error saving keys: {e}")

    def _rotate_keys(self, keys=None):
        """
        Rotate API keys by updating the rotation timestamp

        Args:
            keys (dict, optional): Keys dictionary to update. If None, uses self.keys
        """
        if keys is None:
            keys = self.keys

        # Update rotation timestamp
        keys["last_rotation"] = datetime.datetime.now().isoformat()

        # In a real implementation, you would call the API to rotate the keys
        # For now, we'll just log that rotation is needed
        logger.info("API keys need rotation. Please update them manually.")

        # Save the updated keys
        self.keys = keys
        self._save_keys()

    def store_key(self, service, key_id, key_value, secret=None, additional_data=None):
        """
        Store an API key securely

        Args:
            service (str): Service name (e.g., 'binance', 'openai')
            key_id (str): Key identifier or name
            key_value (str): API key value
            secret (str, optional): API secret for services that use key+secret
            additional_data (dict, optional): Additional data to store with the key
        """
        if "keys" not in self.keys:
            self.keys["keys"] = {}

        if service not in self.keys["keys"]:
            self.keys["keys"][service] = {}

        # Store the key with metadata
        self.keys["keys"][service][key_id] = {
            "key": key_value,
            "secret": secret,
            "created": datetime.datetime.now().isoformat(),
            "last_used": None,
            "additional_data": additional_data or {}
        }

        self._save_keys()
        logger.info(f"Stored key {key_id} for service {service}")

    def get_key(self, service, key_id=None):
        """
        Get an API key by service and optional key_id

        Args:
            service (str): Service name
            key_id (str, optional): Key identifier. If None, returns the first key for the service

        Returns:
            dict: Key data including key, secret, and metadata
        """
        try:
            if "keys" not in self.keys or service not in self.keys["keys"]:
                logger.warning(f"No keys found for service {service}")
                return None

            service_keys = self.keys["keys"][service]

            if key_id is not None:
                if key_id not in service_keys:
                    logger.warning(f"Key {key_id} not found for service {service}")
                    return None

                # Update last used timestamp
                service_keys[key_id]["last_used"] = datetime.datetime.now().isoformat()
                self._save_keys()

                return service_keys[key_id]
            else:
                # Return the first key if no specific key_id is provided
                first_key_id = next(iter(service_keys))

                # Update last used timestamp
                service_keys[first_key_id]["last_used"] = datetime.datetime.now().isoformat()
                self._save_keys()

                return service_keys[first_key_id]
        except Exception as e:
            logger.error(f"Error getting key: {e}")
            return None

    def get_all_keys(self, service):
        """
        Get all keys for a service

        Args:
            service (str): Service name

        Returns:
            dict: Dictionary of all keys for the service
        """
        if "keys" not in self.keys or service not in self.keys["keys"]:
            logger.warning(f"No keys found for service {service}")
            return {}

        return self.keys["keys"][service]

    def delete_key(self, service, key_id):
        """
        Delete a key

        Args:
            service (str): Service name
            key_id (str): Key identifier

        Returns:
            bool: True if key was deleted, False otherwise
        """
        try:
            if "keys" not in self.keys or service not in self.keys["keys"] or key_id not in self.keys["keys"][service]:
                logger.warning(f"Key {key_id} for service {service} not found")
                return False

            del self.keys["keys"][service][key_id]
            self._save_keys()
            logger.info(f"Deleted key {key_id} for service {service}")
            return True
        except Exception as e:
            logger.error(f"Error deleting key: {e}")
            return False

    def rotate_key(self, service, key_id):
        """
        Mark a key for rotation

        Args:
            service (str): Service name
            key_id (str): Key identifier

        Returns:
            bool: True if key was marked for rotation, False otherwise
        """
        try:
            if "keys" not in self.keys or service not in self.keys["keys"] or key_id not in self.keys["keys"][service]:
                logger.warning(f"Key {key_id} for service {service} not found")
                return False

            # Mark the key for rotation
            self.keys["keys"][service][key_id]["needs_rotation"] = True
            self._save_keys()
            logger.info(f"Marked key {key_id} for service {service} for rotation")
            return True
        except Exception as e:
            logger.error(f"Error marking key for rotation: {e}")
            return False

    def get_next_key(self, service):
        """
        Get the next available key for a service using round-robin

        Args:
            service (str): Service name

        Returns:
            dict: Key data including key, secret, and metadata
        """
        try:
            if "keys" not in self.keys or service not in self.keys["keys"]:
                logger.warning(f"No keys found for service {service}")
                return None

            service_keys = self.keys["keys"][service]
            if not service_keys:
                return None

            # Find the least recently used key
            least_recent_key_id = None
            least_recent_time = None

            for key_id, key_data in service_keys.items():
                last_used = key_data.get("last_used")

                # If a key has never been used, use it immediately
                if last_used is None:
                    least_recent_key_id = key_id
                    break

                last_used_time = datetime.datetime.fromisoformat(last_used)
                if least_recent_time is None or last_used_time < least_recent_time:
                    least_recent_time = last_used_time
                    least_recent_key_id = key_id

            if least_recent_key_id is not None:
                # Update last used timestamp
                service_keys[least_recent_key_id]["last_used"] = datetime.datetime.now().isoformat()
                self._save_keys()

                return service_keys[least_recent_key_id]

            return None
        except Exception as e:
            logger.error(f"Error getting next key: {e}")
            return None

    def load_keys_from_env(self):
        """
        Load API keys from environment variables

        Returns:
            int: Number of keys loaded
        """
        count = 0

        # Load Binance API keys
        binance_api_key = os.getenv("BINANCE_API_KEY")
        binance_api_secret = os.getenv("BINANCE_API_SECRET")

        if binance_api_key and binance_api_secret:
            self.store_key("binance", "api_key", binance_api_key, binance_api_secret)
            count += 1
            logger.info("Loaded Binance API keys from environment variables")

        # Load Binance Testnet API keys
        binance_testnet_api_key = os.getenv("BINANCE_TESTNET_API_KEY")
        binance_testnet_api_secret = os.getenv("BINANCE_TESTNET_API_SECRET")

        if binance_testnet_api_key and binance_testnet_api_secret:
            self.store_key("binance_testnet", "api_key", binance_testnet_api_key, binance_testnet_api_secret)
            count += 1
            logger.info("Loaded Binance Testnet API keys from environment variables")

        # Load OpenAI API keys
        for i in range(1, 4):
            openai_api_key = os.getenv(f"OPENAI_API_KEY_{i}")
            openai_model = os.getenv(f"OPENAI_MODEL_{i}", "gpt-4")

            if openai_api_key:
                self.store_key("openai", f"api_key_{i}", openai_api_key, additional_data={"model": openai_model})
                count += 1
                logger.info(f"Loaded OpenAI API key {i} from environment variables")

        # Load DeepSeek API keys
        for i in range(1, 4):
            deepseek_api_key = os.getenv(f"DEEPSEEK_API_KEY_{i}")
            deepseek_api_base = os.getenv(f"DEEPSEEK_API_BASE_{i}", "https://api.deepseek.com/v1")
            deepseek_model = os.getenv(f"DEEPSEEK_MODEL_{i}", "deepseek-chat")

            if deepseek_api_key:
                self.store_key("deepseek", f"api_key_{i}", deepseek_api_key,
                              additional_data={"api_base": deepseek_api_base, "model": deepseek_model})
                count += 1
                logger.info(f"Loaded DeepSeek API key {i} from environment variables")

        # Load Qwen API keys
        for i in range(1, 4):
            qwen_api_key = os.getenv(f"QWEN_API_KEY_{i}")
            qwen_api_base = os.getenv(f"QWEN_API_BASE_{i}", "https://api.qwen.ai/v1")
            qwen_model = os.getenv(f"QWEN_MODEL_{i}", "qwen-max")

            if qwen_api_key:
                self.store_key("qwen", f"api_key_{i}", qwen_api_key,
                              additional_data={"api_base": qwen_api_base, "model": qwen_model})
                count += 1
                logger.info(f"Loaded Qwen API key {i} from environment variables")

        logger.info(f"Loaded {count} API keys from environment variables")
        return count
