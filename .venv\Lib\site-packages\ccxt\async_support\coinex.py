# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.async_support.base.exchange import Exchange
from ccxt.abstract.coinex import ImplicitAPI
import asyncio
from ccxt.base.types import Any, Balances, BorrowInterest, Currencies, Currency, DepositAddress, Int, IsolatedBorrowRate, Leverage, LeverageTier, LeverageTiers, MarginModification, Market, Num, Order, OrderRequest, OrderSide, OrderType, Position, Str, Strings, Ticker, Tickers, FundingRate, FundingRates, Trade, TradingFeeInterface, TradingFees, Transaction, TransferEntry
from typing import List
from ccxt.base.errors import ExchangeError
from ccxt.base.errors import AuthenticationError
from ccxt.base.errors import PermissionDenied
from ccxt.base.errors import AccountSuspended
from ccxt.base.errors import ArgumentsRequired
from ccxt.base.errors import BadRequest
from ccxt.base.errors import BadSymbol
from ccxt.base.errors import InsufficientFunds
from ccxt.base.errors import InvalidOrder
from ccxt.base.errors import OrderNotFound
from ccxt.base.errors import NotSupported
from ccxt.base.errors import RateLimitExceeded
from ccxt.base.errors import ExchangeNotAvailable
from ccxt.base.errors import RequestTimeout
from ccxt.base.decimal_to_precision import TICK_SIZE
from ccxt.base.precise import Precise


class coinex(Exchange, ImplicitAPI):

    def describe(self) -> Any:
        return self.deep_extend(super(coinex, self).describe(), {
            'id': 'coinex',
            'name': 'CoinEx',
            'version': 'v2',
            'countries': ['CN'],
            # IP ratelimit is 400 requests per second
            # rateLimit = 1000ms / 400 = 2.5
            # 200 per 2 seconds => 100 per second => weight = 4
            # 120 per 2 seconds => 60 per second => weight = 6.667
            # 80 per 2 seconds => 40 per second => weight = 10
            # 60 per 2 seconds => 30 per second => weight = 13.334
            # 40 per 2 seconds => 20 per second => weight = 20
            # 20 per 2 seconds => 10 per second => weight = 40
            # v1 is per 2 seconds and v2 is per 1 second
            'rateLimit': 2.5,
            'pro': True,
            'certified': True,
            'has': {
                'CORS': None,
                'spot': True,
                'margin': True,
                'swap': True,
                'future': False,
                'option': False,
                'addMargin': True,
                'borrowCrossMargin': False,
                'borrowIsolatedMargin': True,
                'cancelAllOrders': True,
                'cancelOrder': True,
                'cancelOrders': True,
                'closeAllPositions': False,
                'closePosition': True,
                'createDepositAddress': True,
                'createMarketBuyOrderWithCost': True,
                'createMarketOrderWithCost': False,
                'createMarketSellOrderWithCost': False,
                'createOrder': True,
                'createOrders': True,
                'createReduceOnlyOrder': True,
                'createStopLossOrder': True,
                'createStopOrder': True,
                'createTakeProfitOrder': True,
                'createTriggerOrder': True,
                'editOrder': True,
                'fetchBalance': True,
                'fetchBorrowInterest': True,
                'fetchBorrowRateHistories': False,
                'fetchBorrowRateHistory': False,
                'fetchClosedOrders': True,
                'fetchCrossBorrowRate': False,
                'fetchCrossBorrowRates': False,
                'fetchCurrencies': True,
                'fetchDepositAddress': True,
                'fetchDepositAddresses': False,
                'fetchDepositAddressesByNetwork': False,
                'fetchDeposits': True,
                'fetchDepositWithdrawFee': True,
                'fetchDepositWithdrawFees': False,
                'fetchFundingHistory': True,
                'fetchFundingInterval': True,
                'fetchFundingIntervals': False,
                'fetchFundingRate': True,
                'fetchFundingRateHistory': True,
                'fetchFundingRates': True,
                'fetchIndexOHLCV': False,
                'fetchIsolatedBorrowRate': True,
                'fetchIsolatedBorrowRates': False,
                'fetchLeverage': True,
                'fetchLeverages': False,
                'fetchLeverageTiers': True,
                'fetchMarginAdjustmentHistory': True,
                'fetchMarketLeverageTiers': 'emulated',
                'fetchMarkets': True,
                'fetchMarkOHLCV': False,
                'fetchMyTrades': True,
                'fetchOHLCV': True,
                'fetchOpenOrders': True,
                'fetchOrder': True,
                'fetchOrderBook': True,
                'fetchPosition': True,
                'fetchPositionHistory': True,
                'fetchPositions': True,
                'fetchPositionsHistory': False,
                'fetchPositionsRisk': False,
                'fetchPremiumIndexOHLCV': False,
                'fetchTicker': True,
                'fetchTickers': True,
                'fetchTime': True,
                'fetchTrades': True,
                'fetchTradingFee': True,
                'fetchTradingFees': True,
                'fetchTransfer': False,
                'fetchTransfers': True,
                'fetchWithdrawal': False,
                'fetchWithdrawals': True,
                'reduceMargin': True,
                'repayCrossMargin': False,
                'repayIsolatedMargin': True,
                'setLeverage': True,
                'setMarginMode': True,
                'setPositionMode': False,
                'transfer': True,
                'withdraw': True,
            },
            'timeframes': {
                '1m': '1min',
                '3m': '3min',
                '5m': '5min',
                '15m': '15min',
                '30m': '30min',
                '1h': '1hour',
                '2h': '2hour',
                '4h': '4hour',
                '6h': '6hour',
                '12h': '12hour',
                '1d': '1day',
                '3d': '3day',
                '1w': '1week',
            },
            'urls': {
                'logo': 'https://user-images.githubusercontent.com/51840849/87182089-1e05fa00-c2ec-11ea-8da9-cc73b45abbbc.jpg',
                'api': {
                    'public': 'https://api.coinex.com',
                    'private': 'https://api.coinex.com',
                    'perpetualPublic': 'https://api.coinex.com/perpetual',
                    'perpetualPrivate': 'https://api.coinex.com/perpetual',
                },
                'www': 'https://www.coinex.com',
                'doc': 'https://docs.coinex.com/api/v2',
                'fees': 'https://www.coinex.com/fees',
                'referral': 'https://www.coinex.com/register?refer_code=yw5fz',
            },
            'api': {
                'v1': {
                    'public': {
                        'get': {
                            'amm/market': 1,
                            'common/currency/rate': 1,
                            'common/asset/config': 1,
                            'common/maintain/info': 1,
                            'common/temp-maintain/info': 1,
                            'margin/market': 1,
                            'market/info': 1,
                            'market/list': 1,
                            'market/ticker': 1,
                            'market/ticker/all': 1,
                            'market/depth': 1,
                            'market/deals': 1,
                            'market/kline': 1,
                            'market/detail': 1,
                        },
                    },
                    'private': {
                        'get': {
                            'account/amm/balance': 40,
                            'account/investment/balance': 40,
                            'account/balance/history': 40,
                            'account/market/fee': 40,
                            'balance/coin/deposit': 40,
                            'balance/coin/withdraw': 40,
                            'balance/info': 40,
                            'balance/deposit/address/{coin_type}': 40,
                            'contract/transfer/history': 40,
                            'credit/info': 40,
                            'credit/balance': 40,
                            'investment/transfer/history': 40,
                            'margin/account': 1,
                            'margin/config': 1,
                            'margin/loan/history': 40,
                            'margin/transfer/history': 40,
                            'order/deals': 40,
                            'order/finished': 40,
                            'order/pending': 8,
                            'order/status': 8,
                            'order/status/batch': 8,
                            'order/user/deals': 40,
                            'order/stop/finished': 40,
                            'order/stop/pending': 8,
                            'order/user/trade/fee': 1,
                            'order/market/trade/info': 1,
                            'sub_account/balance': 1,
                            'sub_account/transfer/history': 40,
                            'sub_account/auth/api': 40,
                            'sub_account/auth/api/{user_auth_id}': 40,
                        },
                        'post': {
                            'balance/coin/withdraw': 40,
                            'contract/balance/transfer': 40,
                            'margin/flat': 40,
                            'margin/loan': 40,
                            'margin/transfer': 40,
                            'order/limit/batch': 40,
                            'order/ioc': 13.334,
                            'order/limit': 13.334,
                            'order/market': 13.334,
                            'order/modify': 13.334,
                            'order/stop/limit': 13.334,
                            'order/stop/market': 13.334,
                            'order/stop/modify': 13.334,
                            'sub_account/transfer': 40,
                            'sub_account/register': 1,
                            'sub_account/unfrozen': 40,
                            'sub_account/frozen': 40,
                            'sub_account/auth/api': 40,
                        },
                        'put': {
                            'balance/deposit/address/{coin_type}': 40,
                            'sub_account/unfrozen': 40,
                            'sub_account/frozen': 40,
                            'sub_account/auth/api/{user_auth_id}': 40,
                            'v1/account/settings': 40,
                        },
                        'delete': {
                            'balance/coin/withdraw': 40,
                            'order/pending/batch': 40,
                            'order/pending': 13.334,
                            'order/stop/pending': 40,
                            'order/stop/pending/{id}': 13.334,
                            'order/pending/by_client_id': 40,
                            'order/stop/pending/by_client_id': 40,
                            'sub_account/auth/api/{user_auth_id}': 40,
                            'sub_account/authorize/{id}': 40,
                        },
                    },
                    'perpetualPublic': {
                        'get': {
                            'ping': 1,
                            'time': 1,
                            'market/list': 1,
                            'market/limit_config': 1,
                            'market/ticker': 1,
                            'market/ticker/all': 1,
                            'market/depth': 1,
                            'market/deals': 1,
                            'market/funding_history': 1,
                            'market/kline': 1,
                        },
                    },
                    'perpetualPrivate': {
                        'get': {
                            'market/user_deals': 1,
                            'asset/query': 40,
                            'order/pending': 8,
                            'order/finished': 40,
                            'order/stop_finished': 40,
                            'order/stop_pending': 8,
                            'order/status': 8,
                            'order/stop_status': 8,
                            'position/finished': 40,
                            'position/pending': 40,
                            'position/funding': 40,
                            'position/adl_history': 40,
                            'market/preference': 40,
                            'position/margin_history': 40,
                            'position/settle_history': 40,
                        },
                        'post': {
                            'market/adjust_leverage': 1,
                            'market/position_expect': 1,
                            'order/put_limit': 20,
                            'order/put_market': 20,
                            'order/put_stop_limit': 20,
                            'order/put_stop_market': 20,
                            'order/modify': 20,
                            'order/modify_stop': 20,
                            'order/cancel': 20,
                            'order/cancel_all': 40,
                            'order/cancel_batch': 40,
                            'order/cancel_stop': 20,
                            'order/cancel_stop_all': 40,
                            'order/close_limit': 20,
                            'order/close_market': 20,
                            'position/adjust_margin': 20,
                            'position/stop_loss': 20,
                            'position/take_profit': 20,
                            'position/market_close': 20,
                            'order/cancel/by_client_id': 20,
                            'order/cancel_stop/by_client_id': 20,
                            'market/preference': 20,
                        },
                    },
                },
                'v2': {
                    'public': {
                        'get': {
                            'maintain/info': 1,
                            'ping': 1,
                            'time': 1,
                            'spot/market': 1,
                            'spot/ticker': 1,
                            'spot/depth': 1,
                            'spot/deals': 1,
                            'spot/kline': 1,
                            'spot/index': 1,
                            'futures/market': 1,
                            'futures/ticker': 1,
                            'futures/depth': 1,
                            'futures/deals': 1,
                            'futures/kline': 1,
                            'futures/index': 1,
                            'futures/funding-rate': 1,
                            'futures/funding-rate-history': 1,
                            'futures/position-level': 1,
                            'futures/liquidation-history': 1,
                            'futures/basis-history': 1,
                            'assets/deposit-withdraw-config': 1,
                            'assets/all-deposit-withdraw-config': 1,
                        },
                    },
                    'private': {
                        'get': {
                            'account/subs': 1,
                            'account/subs/api-detail': 40,
                            'account/subs/info': 1,
                            'account/subs/api': 40,
                            'account/subs/transfer-history': 40,
                            'account/subs/spot-balance': 1,
                            'account/trade-fee-rate': 40,
                            'assets/spot/balance': 40,
                            'assets/futures/balance': 40,
                            'assets/margin/balance': 1,
                            'assets/financial/balance': 40,
                            'assets/amm/liquidity': 40,
                            'assets/credit/info': 40,
                            'assets/margin/borrow-history': 40,
                            'assets/margin/interest-limit': 1,
                            'assets/deposit-address': 40,
                            'assets/deposit-history': 40,
                            'assets/withdraw': 40,
                            'assets/transfer-history': 40,
                            'spot/order-status': 8,
                            'spot/batch-order-status': 8,
                            'spot/pending-order': 8,
                            'spot/finished-order': 40,
                            'spot/pending-stop-order': 8,
                            'spot/finished-stop-order': 40,
                            'spot/user-deals': 40,
                            'spot/order-deals': 40,
                            'futures/order-status': 8,
                            'futures/batch-order-status': 1,
                            'futures/pending-order': 8,
                            'futures/finished-order': 40,
                            'futures/pending-stop-order': 8,
                            'futures/finished-stop-order': 40,
                            'futures/user-deals': 1,
                            'futures/order-deals': 1,
                            'futures/pending-position': 40,
                            'futures/finished-position': 1,
                            'futures/position-margin-history': 1,
                            'futures/position-funding-history': 40,
                            'futures/position-adl-history': 1,
                            'futures/position-settle-history': 1,
                        },
                        'post': {
                            'account/subs': 40,
                            'account/subs/frozen': 40,
                            'account/subs/unfrozen': 40,
                            'account/subs/api': 40,
                            'account/subs/edit-api': 40,
                            'account/subs/delete-api': 40,
                            'account/subs/transfer': 40,
                            'account/settings': 40,
                            'assets/margin/borrow': 40,
                            'assets/margin/repay': 40,
                            'assets/renewal-deposit-address': 40,
                            'assets/withdraw': 40,
                            'assets/cancel-withdraw': 40,
                            'assets/transfer': 40,
                            'assets/amm/add-liquidity': 1,
                            'assets/amm/remove-liquidity': 1,
                            'spot/order': 13.334,
                            'spot/stop-order': 13.334,
                            'spot/batch-order': 40,
                            'spot/batch-stop-order': 1,
                            'spot/modify-order': 13.334,
                            'spot/modify-stop-order': 13.334,
                            'spot/cancel-all-order': 1,
                            'spot/cancel-order': 6.667,
                            'spot/cancel-stop-order': 6.667,
                            'spot/cancel-batch-order': 10,
                            'spot/cancel-batch-stop-order': 10,
                            'spot/cancel-order-by-client-id': 1,
                            'spot/cancel-stop-order-by-client-id': 1,
                            'futures/order': 20,
                            'futures/stop-order': 20,
                            'futures/batch-order': 1,
                            'futures/batch-stop-order': 1,
                            'futures/modify-order': 20,
                            'futures/modify-stop-order': 20,
                            'futures/cancel-all-order': 1,
                            'futures/cancel-order': 10,
                            'futures/cancel-stop-order': 10,
                            'futures/cancel-batch-order': 20,
                            'futures/cancel-batch-stop-order': 20,
                            'futures/cancel-order-by-client-id': 1,
                            'futures/cancel-stop-order-by-client-id': 1,
                            'futures/close-position': 20,
                            'futures/adjust-position-margin': 20,
                            'futures/adjust-position-leverage': 20,
                            'futures/set-position-stop-loss': 20,
                            'futures/set-position-take-profit': 20,
                        },
                    },
                },
            },
            'fees': {
                'trading': {
                    'maker': 0.001,
                    'taker': 0.001,
                },
                'funding': {
                    'withdraw': {
                        'BCH': 0.0,
                        'BTC': 0.001,
                        'LTC': 0.001,
                        'ETH': 0.001,
                        'ZEC': 0.0001,
                        'DASH': 0.0001,
                    },
                },
            },
            'limits': {
                'amount': {
                    'min': 0.001,
                    'max': None,
                },
            },
            'options': {
                'brokerId': 'x-*********',
                'createMarketBuyOrderRequiresPrice': True,
                'defaultType': 'spot',  # spot, swap, margin
                'defaultSubType': 'linear',  # linear, inverse
                'fetchDepositAddress': {
                    'fillResponseFromRequest': True,
                },
                'accountsByType': {
                    'spot': 'SPOT',
                    'margin': 'MARGIN',
                    'swap': 'FUTURES',
                },
                'accountsById': {
                    'SPOT': 'spot',
                    'MARGIN': 'margin',
                    'FUTURES': 'swap',
                },
                'networks': {
                    'BTC': 'BTC',
                    'BEP20': 'BSC',
                    'TRC20': 'TRC20',
                    'ERC20': 'ERC20',
                    'BRC20': 'BRC20',
                    'SOL': 'SOL',
                    'TON': 'TON',
                    'BSV': 'BSV',
                    'AVAXC': 'AVA_C',
                    'AVAXX': 'AVA',
                    'SUI': 'SUI',
                    'ACA': 'ACA',
                    'CHZ': 'CHILIZ',
                    'ADA': 'ADA',
                    'ARB': 'ARBITRUM',
                    'ARBNOVA': 'ARBITRUM_NOVA',
                    'OP': 'OPTIMISM',
                    'APT': 'APTOS',
                    'ATOM': 'ATOM',
                    'FTM': 'FTM',
                    'BCH': 'BCH',
                    'ASTR': 'ASTR',
                    'LTC': 'LTC',
                    'MATIC': 'MATIC',
                    'CRONOS': 'CRONOS',
                    'DASH': 'DASH',
                    'DOT': 'DOT',
                    'ETC': 'ETC',
                    'ETHW': 'ETHPOW',
                    'FIL': 'FIL',
                    'ZIL': 'ZIL',
                    'DOGE': 'DOGE',
                    'TIA': 'CELESTIA',
                    'SEI': 'SEI',
                    'XRP': 'XRP',
                    'XMR': 'XMR',
                    # CSC, AE, BASE, AIPG, AKASH, POLKADOTASSETHUB ?, ALEO, STX, ALGO, ALPH, BLAST, AR, ARCH, ARDR, ARK, ARRR, MANTA, NTRN, LUNA, AURORA, AVAIL, ASC20, AVA, AYA, AZERO, BAN, BAND, BB, RUNES, BEAM, BELLSCOIN, BITCI, NEAR, AGORIC, BLOCX, BNC, BOBA, BRISE, KRC20, CANTO, CAPS, CCD, CELO, CFX, CHI, CKB, CLORE, CLV, CORE, CSPR, CTXC, DAG, DCR, DERO, DESO, DEFI, DGB, DNX, DOCK, DOGECHAIN, DYDX, DYMENSION, EGLD, ELA, ELF, ENJIN, EOSIO, ERG, ETN_SC, EVMOS, EWC, SGB, FACT, FB, FET, FIO, FIRO, NEO3, FLOW, FLARE, FLUX, LINEA, FREN, FSN, FB_BRC20, GLMR, GRIN, GRS, HACASH, HBAR, HERB, HIVE, MAPO, HMND, HNS, ZKSYNC, HTR, HUAHUA, MERLIN, ICP, ICX, INJ, IOST, IOTA, IOTX, IRIS, IRON, ONE, JOYSTREAM, KAI, KAR, KAS, KAVA, KCN, KDA, KLAY, KLY, KMD, KSM, KUB, KUJIRA, LAT, LBC, LUNC, LUKSO, MARS, METIS, MINA, MANTLE, MOB, MODE, MONA, MOVR, MTL, NEOX, NEXA, NIBI, NIMIQ, NMC, ONOMY, NRG, WAVES, NULS, OAS, OCTA, OLT, ONT, OORT, ORAI, OSMO, P3D, COMPOSABLE, PIVX, RON, POKT, POLYMESH, PRE_MARKET, PYI, QKC, QTUM, QUBIC, RSK, ROSE, ROUTE, RTM, THORCHAIN, RVN, RADIANT, SAGA, SALVIUM, SATOX, SC, SCP, _NULL, SCRT, SDN, RGBPP, SELF, SMH, SPACE, STARGAZE, STC, STEEM, STRATISEVM, STRD, STARKNET, SXP, SYS, TAIKO, TAO, TARA, TENET, THETA, TT, VENOM, VECHAIN, TOMO, VITE, VLX, VSYS, VTC, WAN, WAXP, WEMIX, XCH, XDC, XEC, XELIS, NEM, XHV, XLM, XNA, NANO, XPLA, XPR, XPRT, XRD, XTZ, XVG, XYM, ZANO, ZEC, ZEN, ZEPH, ZETA
                },
            },
            'features': {
                'spot': {
                    'sandbox': False,
                    'createOrder': {
                        'marginMode': True,
                        'triggerPrice': True,
                        'triggerPriceType': None,
                        'triggerDirection': False,
                        'stopLossPrice': False,  # todo
                        'takeProfitPrice': False,  # todo
                        'attachedStopLossTakeProfit': None,
                        'timeInForce': {
                            'IOC': True,
                            'FOK': True,
                            'PO': True,
                            'GTD': False,
                        },
                        'hedged': False,
                        'trailing': False,
                        'leverage': False,
                        'marketBuyByCost': True,
                        'marketBuyRequiresPrice': True,
                        'selfTradePrevention': True,  # todo: implement
                        'iceberg': True,  # todo implement
                    },
                    'createOrders': {
                        'max': 5,
                    },
                    'fetchMyTrades': {
                        'marginMode': True,
                        'limit': 1000,
                        'daysBack': None,
                        'untilDays': 100000,
                        'symbolRequired': True,
                    },
                    'fetchOrder': {
                        'marginMode': False,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': True,
                    },
                    'fetchOpenOrders': {
                        'marginMode': True,
                        'limit': 1000,
                        'trigger': True,
                        'trailing': False,
                        'symbolRequired': False,
                    },
                    'fetchOrders': None,
                    'fetchClosedOrders': {
                        'marginMode': True,
                        'limit': 1000,
                        'daysBack': None,
                        'daysBackCanceled': None,
                        'untilDays': None,
                        'trigger': True,
                        'trailing': False,
                        'symbolRequired': False,
                    },
                    'fetchOHLCV': {
                        'limit': 1000,
                    },
                },
                'forDerivatives': {
                    'extends': 'spot',
                    'createOrder': {
                        'marginMode': True,
                        'stopLossPrice': True,
                        'takeProfitPrice': True,
                    },
                    'fetchOpenOrders': {
                        'marginMode': False,
                    },
                    'fetchClosedOrders': {
                        'marginMode': False,
                    },
                },
                'swap': {
                    'linear': {
                        'extends': 'forDerivatives',
                    },
                    'inverse': {
                        'extends': 'forDerivatives',
                    },
                },
                'future': {
                    'linear': None,
                    'inverse': None,
                },
            },
            'commonCurrencies': {
                'ACM': 'Actinium',
            },
            'precisionMode': TICK_SIZE,
            'exceptions': {
                'exact': {
                    # https://github.com/coinexcom/coinex_exchange_api/wiki/013error_code
                    '23': PermissionDenied,  # IP Prohibited
                    '24': AuthenticationError,
                    '25': AuthenticationError,
                    '34': AuthenticationError,  # Access id is expires
                    '35': ExchangeNotAvailable,  # Service unavailable
                    '36': RequestTimeout,  # Service timeout
                    '213': RateLimitExceeded,  # Too many requests
                    '107': InsufficientFunds,
                    '158': PermissionDenied,  # {"code":158,"data":{},"message":"API permission is not allowed"}
                    '600': OrderNotFound,
                    '601': InvalidOrder,
                    '602': InvalidOrder,
                    '606': InvalidOrder,
                    '3008': RequestTimeout,  # Service busy, please try again later.
                    '3109': InsufficientFunds,  # {"code":3109,"data":{},"message":"balance not enough"}
                    '3127': InvalidOrder,  # The order quantity is below the minimum requirement. Please adjust the order quantity.
                    '3600': OrderNotFound,  # {"code":3600,"data":{},"message":"Order not found"}
                    '3606': InvalidOrder,  # The price difference between the order price and the latest price is too large. Please adjust the order amount accordingly.
                    '3610': ExchangeError,  # Order cancellation prohibited during the Call Auction period.
                    '3612': InvalidOrder,  # The est. ask price is lower than the current bottom ask price. Please reduce the amount.
                    '3613': InvalidOrder,  # The est. bid price is higher than the current top bid price. Please reduce the amount.
                    '3614': InvalidOrder,  # The deviation between your est. filled price and the index price. Please reduce the amount.
                    '3615': InvalidOrder,  # The deviation between your order price and the index price is too high. Please adjust your order price and try again.
                    '3616': InvalidOrder,  # The order price exceeds the current top bid price. Please adjust the order price and try again.
                    '3617': InvalidOrder,  # The order price exceeds the current bottom ask price. Please adjust the order price and try again.
                    '3618': InvalidOrder,  # The deviation between your order price and the index price is too high. Please adjust your order price and try again.
                    '3619': InvalidOrder,  # The deviation between your order price and the trigger price is too high. Please adjust your order price and try again.
                    '3620': InvalidOrder,  # Market order submission is temporarily unavailable due to insufficient depth in the current market
                    '3621': InvalidOrder,  # This order can't be completely executed and has been canceled.
                    '3622': InvalidOrder,  # This order can't be set Only and has been canceled.
                    '3627': InvalidOrder,  # The current market depth is low, please reduce your order amount and try again.
                    '3628': InvalidOrder,  # The current market depth is low, please reduce your order amount and try again.
                    '3629': InvalidOrder,  # The current market depth is low, please reduce your order amount and try again.
                    '3632': InvalidOrder,  # The order price exceeds the current top bid price. Please adjust the order price and try again.
                    '3633': InvalidOrder,  # The order price exceeds the current bottom ask price. Please adjust the order price and try again.
                    '3634': InvalidOrder,  # The deviation between your est. filled price and the index price is too high. Please reduce the amount and try again.
                    '3635': InvalidOrder,  # The deviation between your est. filled price and the index price is too high. Please reduce the amount and try again.
                    '4001': ExchangeNotAvailable,  # Service unavailable, please try again later.
                    '4002': RequestTimeout,  # Service request timed out, please try again later.
                    '4003': ExchangeError,  # Internal error, please contact customer service for help.
                    '4004': BadRequest,  # Parameter error, please check whether the request parameters are abnormal.
                    '4005': AuthenticationError,  # Abnormal access_id, please check whether the value passed by X-COINEX-KEY is normal.
                    '4006': AuthenticationError,  # Signature verification failed, please check the signature according to the documentation instructions.
                    '4007': PermissionDenied,  # IP address prohibited, please check whether the whitelist or export IP is normal.
                    '4008': AuthenticationError,  # Abnormal X-COIN-SIGN value, please check.
                    '4009': ExchangeError,  # Abnormal request method, please check.
                    '4010': ExchangeError,  # Expired request, please try again later.
                    '4011': PermissionDenied,  # User prohibited from accessing, please contact customer service for help.
                    '4017': ExchangeError,  # Signature expired, please try again later.
                    '4115': AccountSuspended,  # User prohibited from trading, please contact customer service for help.
                    '4117': BadSymbol,  # Trading hasattr(self, prohibited) market, please try again later.
                    '4123': RateLimitExceeded,  # Rate limit triggered. Please adjust your strategy and reduce the request rate.
                    '4130': ExchangeError,  # Futures trading prohibited, please try again later.
                    '4158': ExchangeError,  # Trading prohibited, please try again later.
                    '4213': RateLimitExceeded,  # The request is too frequent, please try again later.
                    '4512': PermissionDenied,  # Insufficient sub-account permissions, please check.
                },
                'broad': {
                    'ip not allow visit': PermissionDenied,
                    'service too busy': ExchangeNotAvailable,
                },
            },
        })

    async def fetch_currencies(self, params={}) -> Currencies:
        """
        fetches all available currencies on an exchange

        https://docs.coinex.com/api/v2/assets/deposit-withdrawal/http/list-all-deposit-withdrawal-config

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an associative dictionary of currencies
        """
        response = await self.v2PublicGetAssetsAllDepositWithdrawConfig(params)
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "asset": {
        #                     "ccy": "CET",
        #                     "deposit_enabled": True,
        #                     "withdraw_enabled": True,
        #                     "inter_transfer_enabled": True,
        #                     "is_st": False
        #                 },
        #                 "chains": [
        #                     {
        #                         "chain": "CSC",
        #                         "min_deposit_amount": "0.8",
        #                         "min_withdraw_amount": "8",
        #                         "deposit_enabled": True,
        #                         "withdraw_enabled": True,
        #                         "deposit_delay_minutes": 0,
        #                         "safe_confirmations": 10,
        #                         "irreversible_confirmations": 20,
        #                         "deflation_rate": "0",
        #                         "withdrawal_fee": "0.026",
        #                         "withdrawal_precision": 8,
        #                         "memo": "",
        #                         "is_memo_required_for_deposit": False,
        #                         "explorer_asset_url": ""
        #                     },
        #                 ]
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        result: dict = {}
        for i in range(0, len(data)):
            coin = data[i]
            asset = self.safe_dict(coin, 'asset', {})
            chains = self.safe_list(coin, 'chains', [])
            currencyId = self.safe_string(asset, 'ccy')
            if currencyId is None:
                continue  # coinex returns empty structures for some reason
            code = self.safe_currency_code(currencyId)
            canDeposit = self.safe_bool(asset, 'deposit_enabled')
            canWithdraw = self.safe_bool(asset, 'withdraw_enabled')
            firstChain = self.safe_dict(chains, 0, {})
            firstPrecisionString = self.parse_precision(self.safe_string(firstChain, 'withdrawal_precision'))
            result[code] = {
                'id': currencyId,
                'code': code,
                'name': None,
                'active': canDeposit and canWithdraw,
                'deposit': canDeposit,
                'withdraw': canWithdraw,
                'fee': None,
                'precision': self.parse_number(firstPrecisionString),
                'limits': {
                    'amount': {
                        'min': None,
                        'max': None,
                    },
                    'deposit': {
                        'min': None,
                        'max': None,
                    },
                    'withdraw': {
                        'min': None,
                        'max': None,
                    },
                },
                'networks': {},
                'info': coin,
            }
            for j in range(0, len(chains)):
                chain = chains[j]
                networkId = self.safe_string(chain, 'chain')
                precisionString = self.parse_precision(self.safe_string(chain, 'withdrawal_precision'))
                feeString = self.safe_string(chain, 'withdrawal_fee')
                minNetworkDepositString = self.safe_string(chain, 'min_deposit_amount')
                minNetworkWithdrawString = self.safe_string(chain, 'min_withdraw_amount')
                canDepositChain = self.safe_bool(chain, 'deposit_enabled')
                canWithdrawChain = self.safe_bool(chain, 'withdraw_enabled')
                network: dict = {
                    'id': networkId,
                    'network': networkId,
                    'name': None,
                    'active': canDepositChain and canWithdrawChain,
                    'deposit': canDepositChain,
                    'withdraw': canWithdrawChain,
                    'fee': self.parse_number(feeString),
                    'precision': self.parse_number(precisionString),
                    'limits': {
                        'amount': {
                            'min': None,
                            'max': None,
                        },
                        'deposit': {
                            'min': self.parse_number(minNetworkDepositString),
                            'max': None,
                        },
                        'withdraw': {
                            'min': self.parse_number(minNetworkWithdrawString),
                            'max': None,
                        },
                    },
                    'info': chain,
                }
                networks = self.safe_dict(result[code], 'networks', {})
                networks[networkId] = network
                result[code]['networks'] = networks
        return result

    async def fetch_markets(self, params={}) -> List[Market]:
        """
        retrieves data on all markets for coinex

        https://docs.coinex.com/api/v2/spot/market/http/list-market
        https://docs.coinex.com/api/v2/futures/market/http/list-market

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: an array of objects representing market data
        """
        promisesUnresolved = [
            self.fetch_spot_markets(params),
            self.fetch_contract_markets(params),
        ]
        promises = await asyncio.gather(*promisesUnresolved)
        spotMarkets = promises[0]
        swapMarkets = promises[1]
        return self.array_concat(spotMarkets, swapMarkets)

    async def fetch_spot_markets(self, params) -> List[Market]:
        response = await self.v2PublicGetSpotMarket(params)
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "base_ccy": "SORA",
        #                 "base_ccy_precision": 8,
        #                 "is_amm_available": True,
        #                 "is_margin_available": False,
        #                 "maker_fee_rate": "0.003",
        #                 "market": "SORAUSDT",
        #                 "min_amount": "500",
        #                 "quote_ccy": "USDT",
        #                 "quote_ccy_precision": 6,
        #                 "taker_fee_rate": "0.003"
        #             },
        #         ],
        #         "message": "OK"
        #     }
        #
        markets = self.safe_list(response, 'data', [])
        result = []
        for i in range(0, len(markets)):
            market = markets[i]
            id = self.safe_string(market, 'market')
            baseId = self.safe_string(market, 'base_ccy')
            quoteId = self.safe_string(market, 'quote_ccy')
            base = self.safe_currency_code(baseId)
            quote = self.safe_currency_code(quoteId)
            symbol = base + '/' + quote
            result.append({
                'id': id,
                'symbol': symbol,
                'base': base,
                'quote': quote,
                'settle': None,
                'baseId': baseId,
                'quoteId': quoteId,
                'settleId': None,
                'type': 'spot',
                'spot': True,
                'margin': None,
                'swap': False,
                'future': False,
                'option': False,
                'active': None,
                'contract': False,
                'linear': None,
                'inverse': None,
                'taker': self.safe_number(market, 'taker_fee_rate'),
                'maker': self.safe_number(market, 'maker_fee_rate'),
                'contractSize': None,
                'expiry': None,
                'expiryDatetime': None,
                'strike': None,
                'optionType': None,
                'precision': {
                    'amount': self.parse_number(self.parse_precision(self.safe_string(market, 'base_ccy_precision'))),
                    'price': self.parse_number(self.parse_precision(self.safe_string(market, 'quote_ccy_precision'))),
                },
                'limits': {
                    'leverage': {
                        'min': None,
                        'max': None,
                    },
                    'amount': {
                        'min': self.safe_number(market, 'min_amount'),
                        'max': None,
                    },
                    'price': {
                        'min': None,
                        'max': None,
                    },
                    'cost': {
                        'min': None,
                        'max': None,
                    },
                },
                'created': None,
                'info': market,
            })
        return result

    async def fetch_contract_markets(self, params):
        response = await self.v2PublicGetFuturesMarket(params)
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "base_ccy": "BTC",
        #                 "base_ccy_precision": 8,
        #                 "contract_type": "inverse",
        #                 "leverage": ["1","2","3","5","8","10","15","20","30","50","100"],
        #                 "maker_fee_rate": "0",
        #                 "market": "BTCUSD",
        #                 "min_amount": "10",
        #                 "open_interest_volume": "2566879",
        #                 "quote_ccy": "USD",
        #                 "quote_ccy_precision": 2,
        #                 "taker_fee_rate": "0"
        #             },
        #         ],
        #         "message": "OK"
        #     }
        #
        markets = self.safe_list(response, 'data', [])
        result = []
        for i in range(0, len(markets)):
            entry = markets[i]
            fees = self.fees
            leverages = self.safe_list(entry, 'leverage', [])
            subType = self.safe_string(entry, 'contract_type')
            linear = (subType == 'linear')
            inverse = (subType == 'inverse')
            id = self.safe_string(entry, 'market')
            baseId = self.safe_string(entry, 'base_ccy')
            quoteId = self.safe_string(entry, 'quote_ccy')
            base = self.safe_currency_code(baseId)
            quote = self.safe_currency_code(quoteId)
            settleId = 'USDT' if (subType == 'linear') else baseId
            settle = self.safe_currency_code(settleId)
            symbol = base + '/' + quote + ':' + settle
            leveragesLength = len(leverages)
            result.append({
                'id': id,
                'symbol': symbol,
                'base': base,
                'quote': quote,
                'settle': settle,
                'baseId': baseId,
                'quoteId': quoteId,
                'settleId': settleId,
                'type': 'swap',
                'spot': False,
                'margin': False,
                'swap': True,
                'future': False,
                'option': False,
                'active': None,
                'contract': True,
                'linear': linear,
                'inverse': inverse,
                'taker': fees['trading']['taker'],
                'maker': fees['trading']['maker'],
                'contractSize': self.parse_number('1'),
                'expiry': None,
                'expiryDatetime': None,
                'strike': None,
                'optionType': None,
                'precision': {
                    'amount': self.parse_number(self.parse_precision(self.safe_string(entry, 'base_ccy_precision'))),
                    'price': self.parse_number(self.parse_precision(self.safe_string(entry, 'quote_ccy_precision'))),
                },
                'limits': {
                    'leverage': {
                        'min': self.safe_number(leverages, 0),
                        'max': self.safe_number(leverages, leveragesLength - 1),
                    },
                    'amount': {
                        'min': self.safe_number(entry, 'min_amount'),
                        'max': None,
                    },
                    'price': {
                        'min': None,
                        'max': None,
                    },
                    'cost': {
                        'min': None,
                        'max': None,
                    },
                },
                'created': None,
                'info': entry,
            })
        return result

    def parse_ticker(self, ticker: dict, market: Market = None) -> Ticker:
        #
        # Spot fetchTicker, fetchTickers
        #
        #     {
        #         "close": "62393.47",
        #         "high": "64106.41",
        #         "last": "62393.47",
        #         "low": "59650.01",
        #         "market": "BTCUSDT",
        #         "open": "61616.15",
        #         "period": 86400,
        #         "value": "28711273.4065667262",
        #         "volume": "461.76557205",
        #         "volume_buy": "11.41506354",
        #         "volume_sell": "7.3240169"
        #     }
        #
        # Swap fetchTicker, fetchTickers
        #
        #     {
        #         "close": "62480.08",
        #         "high": "64100",
        #         "index_price": "62443.05",
        #         "last": "62480.08",
        #         "low": "59600",
        #         "mark_price": "62443.05",
        #         "market": "BTCUSDT",
        #         "open": "61679.98",
        #         "period": 86400,
        #         "value": "180226025.69791713065326633165",
        #         "volume": "2900.2218",
        #         "volume_buy": "7.3847",
        #         "volume_sell": "6.1249"
        #     }
        #
        marketType = 'swap' if ('mark_price' in ticker) else 'spot'
        marketId = self.safe_string(ticker, 'market')
        symbol = self.safe_symbol(marketId, market, None, marketType)
        return self.safe_ticker({
            'symbol': symbol,
            'timestamp': None,
            'datetime': None,
            'high': self.safe_string(ticker, 'high'),
            'low': self.safe_string(ticker, 'low'),
            'bid': None,
            'bidVolume': self.safe_string(ticker, 'volume_buy'),
            'ask': None,
            'askVolume': self.safe_string(ticker, 'volume_sell'),
            'vwap': None,
            'open': self.safe_string(ticker, 'open'),
            'close': self.safe_string(ticker, 'close'),
            'last': self.safe_string(ticker, 'last'),
            'previousClose': None,
            'change': None,
            'percentage': None,
            'average': None,
            'baseVolume': self.safe_string(ticker, 'volume'),
            'quoteVolume': None,
            'markPrice': self.safe_string(ticker, 'mark_price'),
            'indexPrice': self.safe_string(ticker, 'index_price'),
            'info': ticker,
        }, market)

    async def fetch_ticker(self, symbol: str, params={}) -> Ticker:
        """
        fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market

        https://docs.coinex.com/api/v2/spot/market/http/list-market-ticker
        https://docs.coinex.com/api/v2/futures/market/http/list-market-ticker

        :param str symbol: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
        }
        response = None
        if market['swap']:
            response = await self.v2PublicGetFuturesTicker(self.extend(request, params))
        else:
            response = await self.v2PublicGetSpotTicker(self.extend(request, params))
        #
        # Spot
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "close": "62393.47",
        #                 "high": "64106.41",
        #                 "last": "62393.47",
        #                 "low": "59650.01",
        #                 "market": "BTCUSDT",
        #                 "open": "61616.15",
        #                 "period": 86400,
        #                 "value": "28711273.4065667262",
        #                 "volume": "461.76557205",
        #                 "volume_buy": "11.41506354",
        #                 "volume_sell": "7.3240169"
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        # Swap
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "close": "62480.08",
        #                 "high": "64100",
        #                 "index_price": "62443.05",
        #                 "last": "62480.08",
        #                 "low": "59600",
        #                 "mark_price": "62443.05",
        #                 "market": "BTCUSDT",
        #                 "open": "61679.98",
        #                 "period": 86400,
        #                 "value": "180226025.69791713065326633165",
        #                 "volume": "2900.2218",
        #                 "volume_buy": "7.3847",
        #                 "volume_sell": "6.1249"
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        result = self.safe_dict(data, 0, {})
        return self.parse_ticker(result, market)

    async def fetch_tickers(self, symbols: Strings = None, params={}) -> Tickers:
        """
        fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market

        https://docs.coinex.com/api/v2/spot/market/http/list-market-ticker
        https://docs.coinex.com/api/v2/futures/market/http/list-market-ticker

        :param str[]|None symbols: unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `ticker structures <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        symbols = self.market_symbols(symbols)
        market = None
        if symbols is not None:
            symbol = self.safe_value(symbols, 0)
            market = self.market(symbol)
        marketType, query = self.handle_market_type_and_params('fetchTickers', market, params)
        response = None
        if marketType == 'swap':
            response = await self.v2PublicGetFuturesTicker(query)
        else:
            response = await self.v2PublicGetSpotTicker(query)
        #
        # Spot
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "close": "62393.47",
        #                 "high": "64106.41",
        #                 "last": "62393.47",
        #                 "low": "59650.01",
        #                 "market": "BTCUSDT",
        #                 "open": "61616.15",
        #                 "period": 86400,
        #                 "value": "28711273.4065667262",
        #                 "volume": "461.76557205",
        #                 "volume_buy": "11.41506354",
        #                 "volume_sell": "7.3240169"
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        # Swap
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "close": "62480.08",
        #                 "high": "64100",
        #                 "index_price": "62443.05",
        #                 "last": "62480.08",
        #                 "low": "59600",
        #                 "mark_price": "62443.05",
        #                 "market": "BTCUSDT",
        #                 "open": "61679.98",
        #                 "period": 86400,
        #                 "value": "180226025.69791713065326633165",
        #                 "volume": "2900.2218",
        #                 "volume_buy": "7.3847",
        #                 "volume_sell": "6.1249"
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_tickers(data, symbols)

    async def fetch_time(self, params={}) -> Int:
        """
        fetches the current integer timestamp in milliseconds from the exchange server

        https://docs.coinex.com/api/v2/common/http/time

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int: the current integer timestamp in milliseconds from the exchange server
        """
        response = await self.v2PublicGetTime(params)
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "timestamp": 1711699867777
        #         },
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data', {})
        return self.safe_integer(data, 'timestamp')

    async def fetch_order_book(self, symbol: str, limit: Int = 20, params={}):
        """
        fetches information on open orders with bid(buy) and ask(sell) prices, volumes and other data

        https://docs.coinex.com/api/v2/spot/market/http/list-market-depth
        https://docs.coinex.com/api/v2/futures/market/http/list-market-depth

        :param str symbol: unified symbol of the market to fetch the order book for
        :param int [limit]: the maximum amount of order book entries to return
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: A dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbols
        """
        await self.load_markets()
        market = self.market(symbol)
        if limit is None:
            limit = 20  # default
        request: dict = {
            'market': market['id'],
            'limit': limit,
            'interval': '0',
        }
        response = None
        if market['swap']:
            response = await self.v2PublicGetFuturesDepth(self.extend(request, params))
            #
            #     {
            #         "code": 0,
            #         "data": {
            #             "depth": {
            #                 "asks": [
            #                     ["70851.94", "0.2119"],
            #                     ["70851.95", "0.0004"],
            #                     ["70851.96", "0.0004"]
            #                 ],
            #                 "bids": [
            #                     ["70851.93", "1.0314"],
            #                     ["70850.93", "0.0021"],
            #                     ["70850.42", "0.0306"]
            #                 ],
            #                 "checksum": 2956436260,
            #                 "last": "70851.94",
            #                 "updated_at": 1712824003252
            #             },
            #             "is_full": True,
            #             "market": "BTCUSDT"
            #         },
            #         "message": "OK"
            #     }
            #
        else:
            response = await self.v2PublicGetSpotDepth(self.extend(request, params))
            #
            #     {
            #         "code": 0,
            #         "data": {
            #             "depth": {
            #                 "asks": [
            #                     ["70875.31", "0.28670282"],
            #                     ["70875.32", "0.31008114"],
            #                     ["70875.42", "0.05876653"]
            #                 ],
            #                 "bids": [
            #                     ["70855.3", "0.00632222"],
            #                     ["70855.29", "0.36216834"],
            #                     ["70855.17", "0.10166802"]
            #                 ],
            #                 "checksum": 2313816665,
            #                 "last": "70857.19",
            #                 "updated_at": 1712823790987
            #             },
            #             "is_full": True,
            #             "market": "BTCUSDT"
            #         },
            #         "message": "OK"
            #     }
            #
        data = self.safe_dict(response, 'data', {})
        depth = self.safe_dict(data, 'depth', {})
        timestamp = self.safe_integer(depth, 'updated_at')
        return self.parse_order_book(depth, symbol, timestamp)

    def parse_trade(self, trade: dict, market: Market = None) -> Trade:
        #
        # Spot and Swap fetchTrades(public)
        #
        #     {
        #         "amount": "0.00049432",
        #         "created_at": 1713849825667,
        #         "deal_id": 4137517302,
        #         "price": "66251",
        #         "side": "buy"
        #     }
        #
        # Spot and Margin fetchMyTrades(private)
        #
        #     {
        #         "amount": "0.00010087",
        #         "created_at": 1714618087585,
        #         "deal_id": 4161200602,
        #         "margin_market": "",
        #         "market": "BTCUSDT",
        #         "order_id": 117654919342,
        #         "price": "57464.04",
        #         "side": "sell"
        #     }
        #
        # Swap fetchMyTrades(private)
        #
        #     {
        #         "deal_id": 1180222387,
        #         "created_at": 1714119054558,
        #         "market": "BTCUSDT",
        #         "side": "buy",
        #         "order_id": 136915589622,
        #         "price": "64376",
        #         "amount": "0.0001",
        #         "role": "taker",
        #         "fee": "0.0299",
        #         "fee_ccy": "USDT"
        #     }
        #
        timestamp = self.safe_integer(trade, 'created_at')
        defaultType = self.safe_string(self.options, 'defaultType')
        if market is not None:
            defaultType = market['type']
        marketId = self.safe_string(trade, 'market')
        market = self.safe_market(marketId, market, None, defaultType)
        feeCostString = self.safe_string(trade, 'fee')
        fee = None
        if feeCostString is not None:
            feeCurrencyId = self.safe_string(trade, 'fee_ccy')
            feeCurrencyCode = self.safe_currency_code(feeCurrencyId)
            fee = {
                'cost': feeCostString,
                'currency': feeCurrencyCode,
            }
        return self.safe_trade({
            'info': trade,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'symbol': market['symbol'],
            'id': self.safe_string(trade, 'deal_id'),
            'order': self.safe_string(trade, 'order_id'),
            'type': None,
            'side': self.safe_string(trade, 'side'),
            'takerOrMaker': self.safe_string(trade, 'role'),
            'price': self.safe_string(trade, 'price'),
            'amount': self.safe_string(trade, 'amount'),
            'cost': self.safe_string(trade, 'deal_money'),
            'fee': fee,
        }, market)

    async def fetch_trades(self, symbol: str, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """
        get the list of the most recent trades for a particular symbol

        https://docs.coinex.com/api/v2/spot/market/http/list-market-deals
        https://docs.coinex.com/api/v2/futures/market/http/list-market-deals

        :param str symbol: unified symbol of the market to fetch trades for
        :param int [since]: timestamp in ms of the earliest trade to fetch
        :param int [limit]: the maximum amount of trades to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=public-trades>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
            # 'last_id': 0,
        }
        if limit is not None:
            request['limit'] = limit
        response = None
        if market['swap']:
            response = await self.v2PublicGetFuturesDeals(self.extend(request, params))
        else:
            response = await self.v2PublicGetSpotDeals(self.extend(request, params))
        #
        # Spot and Swap
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "amount": "0.00049432",
        #                 "created_at": 1713849825667,
        #                 "deal_id": 4137517302,
        #                 "price": "66251",
        #                 "side": "buy"
        #             },
        #         ],
        #         "message": "OK"
        #     }
        #
        return self.parse_trades(response['data'], market, since, limit)

    async def fetch_trading_fee(self, symbol: str, params={}) -> TradingFeeInterface:
        """
        fetch the trading fees for a market

        https://docs.coinex.com/api/v2/spot/market/http/list-market
        https://docs.coinex.com/api/v2/futures/market/http/list-market

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `fee structure <https://docs.ccxt.com/#/?id=fee-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
        }
        response = None
        if market['spot']:
            response = await self.v2PublicGetSpotMarket(self.extend(request, params))
            #
            #     {
            #         "code": 0,
            #         "data": [
            #             {
            #                 "base_ccy": "BTC",
            #                 "base_ccy_precision": 8,
            #                 "is_amm_available": False,
            #                 "is_margin_available": True,
            #                 "maker_fee_rate": "0.002",
            #                 "market": "BTCUSDT",
            #                 "min_amount": "0.0001",
            #                 "quote_ccy": "USDT",
            #                 "quote_ccy_precision": 2,
            #                 "taker_fee_rate": "0.002"
            #             }
            #         ],
            #         "message": "OK"
            #     }
            #
        else:
            response = await self.v2PublicGetFuturesMarket(self.extend(request, params))
            #
            #     {
            #         "code": 0,
            #         "data": [
            #             {
            #                 "base_ccy": "BTC",
            #                 "base_ccy_precision": 8,
            #                 "contract_type": "linear",
            #                 "leverage": ["1","2","3","5","8","10","15","20","30","50","100"],
            #                 "maker_fee_rate": "0",
            #                 "market": "BTCUSDT",
            #                 "min_amount": "0.0001",
            #                 "open_interest_volume": "185.7498",
            #                 "quote_ccy": "USDT",
            #                 "quote_ccy_precision": 2,
            #                 "taker_fee_rate": "0"
            #             }
            #         ],
            #         "message": "OK"
            #     }
            #
        data = self.safe_list(response, 'data', [])
        result = self.safe_dict(data, 0, {})
        return self.parse_trading_fee(result, market)

    async def fetch_trading_fees(self, params={}) -> TradingFees:
        """
        fetch the trading fees for multiple markets

        https://docs.coinex.com/api/v2/spot/market/http/list-market
        https://docs.coinex.com/api/v2/futures/market/http/list-market

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `fee structures <https://docs.ccxt.com/#/?id=fee-structure>` indexed by market symbols
        """
        await self.load_markets()
        type = None
        type, params = self.handle_market_type_and_params('fetchTradingFees', None, params)
        response = None
        if type == 'swap':
            response = await self.v2PublicGetFuturesMarket(params)
            #
            #     {
            #         "code": 0,
            #         "data": [
            #             {
            #                 "base_ccy": "BTC",
            #                 "base_ccy_precision": 8,
            #                 "contract_type": "linear",
            #                 "leverage": ["1","2","3","5","8","10","15","20","30","50","100"],
            #                 "maker_fee_rate": "0",
            #                 "market": "BTCUSDT",
            #                 "min_amount": "0.0001",
            #                 "open_interest_volume": "185.7498",
            #                 "quote_ccy": "USDT",
            #                 "quote_ccy_precision": 2,
            #                 "taker_fee_rate": "0"
            #             }
            #         ],
            #         "message": "OK"
            #     }
            #
        else:
            response = await self.v2PublicGetSpotMarket(params)
            #
            #     {
            #         "code": 0,
            #         "data": [
            #             {
            #                 "base_ccy": "BTC",
            #                 "base_ccy_precision": 8,
            #                 "is_amm_available": False,
            #                 "is_margin_available": True,
            #                 "maker_fee_rate": "0.002",
            #                 "market": "BTCUSDT",
            #                 "min_amount": "0.0001",
            #                 "quote_ccy": "USDT",
            #                 "quote_ccy_precision": 2,
            #                 "taker_fee_rate": "0.002"
            #             },
            #         ],
            #         "message": "OK"
            #     }
            #
        data = self.safe_list(response, 'data', [])
        result: dict = {}
        for i in range(0, len(data)):
            entry = data[i]
            marketId = self.safe_string(entry, 'market')
            market = self.safe_market(marketId, None, None, type)
            symbol = market['symbol']
            result[symbol] = self.parse_trading_fee(entry, market)
        return result

    def parse_trading_fee(self, fee: dict, market: Market = None) -> TradingFeeInterface:
        marketId = self.safe_value(fee, 'market')
        symbol = self.safe_symbol(marketId, market)
        return {
            'info': fee,
            'symbol': symbol,
            'maker': self.safe_number(fee, 'maker_fee_rate'),
            'taker': self.safe_number(fee, 'taker_fee_rate'),
            'percentage': True,
            'tierBased': True,
        }

    def parse_ohlcv(self, ohlcv, market: Market = None) -> list:
        #
        #     {
        #         "close": "66999.95",
        #         "created_at": 1713934620000,
        #         "high": "66999.95",
        #         "low": "66988.53",
        #         "market": "BTCUSDT",
        #         "open": "66988.53",
        #         "value": "0.1572393",        # base volume
        #         "volume": "10533.**********"  # quote volume
        #     }
        #
        return [
            self.safe_integer(ohlcv, 'created_at'),
            self.safe_number(ohlcv, 'open'),
            self.safe_number(ohlcv, 'high'),
            self.safe_number(ohlcv, 'low'),
            self.safe_number(ohlcv, 'close'),
            self.safe_number(ohlcv, 'value'),
        ]

    async def fetch_ohlcv(self, symbol: str, timeframe='1m', since: Int = None, limit: Int = None, params={}) -> List[list]:
        """
        fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market

        https://docs.coinex.com/api/v2/spot/market/http/list-market-kline
        https://docs.coinex.com/api/v2/futures/market/http/list-market-kline

        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
            'period': self.safe_string(self.timeframes, timeframe, timeframe),
        }
        if limit is not None:
            request['limit'] = limit
        response = None
        if market['swap']:
            response = await self.v2PublicGetFuturesKline(self.extend(request, params))
        else:
            response = await self.v2PublicGetSpotKline(self.extend(request, params))
        #
        # Spot and Swap
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "close": "66999.95",
        #                 "created_at": 1713934620000,
        #                 "high": "66999.95",
        #                 "low": "66988.53",
        #                 "market": "BTCUSDT",
        #                 "open": "66988.53",
        #                 "value": "0.1572393",
        #                 "volume": "10533.**********"
        #             },
        #         ],
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_ohlcvs(data, market, timeframe, since, limit)

    async def fetch_margin_balance(self, params={}):
        await self.load_markets()
        response = await self.v2PrivateGetAssetsMarginBalance(params)
        #
        #     {
        #         "data": [
        #             {
        #                 "margin_account": "BTCUSDT",
        #                 "base_ccy": "BTC",
        #                 "quote_ccy": "USDT",
        #                 "available": {
        #                     "base_ccy": "0.********",
        #                     "quote_ccy": "0"
        #                 },
        #                 "frozen": {
        #                     "base_ccy": "0",
        #                     "quote_ccy": "0"
        #                 },
        #                 "repaid": {
        #                     "base_ccy": "0",
        #                     "quote_ccy": "0"
        #                 },
        #                 "interest": {
        #                     "base_ccy": "0",
        #                     "quote_ccy": "0"
        #                 },
        #                 "rik_rate": "",
        #                 "liq_price": ""
        #             },
        #         ],
        #         "code": 0,
        #         "message": "OK"
        #     }
        #
        result: dict = {'info': response}
        balances = self.safe_list(response, 'data', [])
        for i in range(0, len(balances)):
            entry = balances[i]
            free = self.safe_dict(entry, 'available', {})
            used = self.safe_dict(entry, 'frozen', {})
            loan = self.safe_dict(entry, 'repaid', {})
            interest = self.safe_dict(entry, 'interest', {})
            baseAccount = self.account()
            baseCurrencyId = self.safe_string(entry, 'base_ccy')
            baseCurrencyCode = self.safe_currency_code(baseCurrencyId)
            baseAccount['free'] = self.safe_string(free, 'base_ccy')
            baseAccount['used'] = self.safe_string(used, 'base_ccy')
            baseDebt = self.safe_string(loan, 'base_ccy')
            baseInterest = self.safe_string(interest, 'base_ccy')
            baseAccount['debt'] = Precise.string_add(baseDebt, baseInterest)
            result[baseCurrencyCode] = baseAccount
        return self.safe_balance(result)

    async def fetch_spot_balance(self, params={}):
        await self.load_markets()
        response = await self.v2PrivateGetAssetsSpotBalance(params)
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "available": "0.********",
        #                 "ccy": "USDT",
        #                 "frozen": "0"
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        result: dict = {'info': response}
        balances = self.safe_list(response, 'data', [])
        for i in range(0, len(balances)):
            entry = balances[i]
            currencyId = self.safe_string(entry, 'ccy')
            code = self.safe_currency_code(currencyId)
            account = self.account()
            account['free'] = self.safe_string(entry, 'available')
            account['used'] = self.safe_string(entry, 'frozen')
            result[code] = account
        return self.safe_balance(result)

    async def fetch_swap_balance(self, params={}):
        await self.load_markets()
        response = await self.v2PrivateGetAssetsFuturesBalance(params)
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "available": "0.********",
        #                 "ccy": "USDT",
        #                 "frozen": "0",
        #                 "margin": "0",
        #                 "transferrable": "0.********",
        #                 "unrealized_pnl": "0"
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        result: dict = {'info': response}
        balances = self.safe_list(response, 'data', [])
        for i in range(0, len(balances)):
            entry = balances[i]
            currencyId = self.safe_string(entry, 'ccy')
            code = self.safe_currency_code(currencyId)
            account = self.account()
            account['free'] = self.safe_string(entry, 'available')
            account['used'] = self.safe_string(entry, 'frozen')
            result[code] = account
        return self.safe_balance(result)

    async def fetch_financial_balance(self, params={}):
        await self.load_markets()
        response = await self.v2PrivateGetAssetsFinancialBalance(params)
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "available": "0.********",
        #                 "ccy": "USDT",
        #                 "frozen": "0"
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        result: dict = {'info': response}
        balances = self.safe_list(response, 'data', [])
        for i in range(0, len(balances)):
            entry = balances[i]
            currencyId = self.safe_string(entry, 'ccy')
            code = self.safe_currency_code(currencyId)
            account = self.account()
            account['free'] = self.safe_string(entry, 'available')
            account['used'] = self.safe_string(entry, 'frozen')
            result[code] = account
        return self.safe_balance(result)

    async def fetch_balance(self, params={}) -> Balances:
        """
        query for balance and get the amount of funds available for trading or funds locked in orders

        https://docs.coinex.com/api/v2/assets/balance/http/get-spot-balance         # spot
        https://docs.coinex.com/api/v2/assets/balance/http/get-futures-balance      # swap
        https://docs.coinex.com/api/v2/assets/balance/http/get-marigin-balance      # margin
        https://docs.coinex.com/api/v2/assets/balance/http/get-financial-balance    # financial

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.type]: 'margin', 'swap', 'financial', or 'spot'
        :returns dict: a `balance structure <https://docs.ccxt.com/#/?id=balance-structure>`
        """
        marketType = None
        marketType, params = self.handle_market_type_and_params('fetchBalance', None, params)
        marginMode = None
        marginMode, params = self.handle_margin_mode_and_params('fetchBalance', params)
        isMargin = (marginMode is not None) or (marketType == 'margin')
        if marketType == 'swap':
            return await self.fetch_swap_balance(params)
        elif marketType == 'financial':
            return await self.fetch_financial_balance(params)
        elif isMargin:
            return await self.fetch_margin_balance(params)
        else:
            return await self.fetch_spot_balance(params)

    def parse_order_status(self, status: Str):
        statuses: dict = {
            'rejected': 'rejected',
            'open': 'open',
            'not_deal': 'open',
            'part_deal': 'open',
            'done': 'closed',
            'cancel': 'canceled',
        }
        return self.safe_string(statuses, status, status)

    def parse_order(self, order: dict, market: Market = None) -> Order:
        #
        # Spot and Margin createOrder, createOrders, editOrder, cancelOrders, cancelOrder, fetchOpenOrders
        #
        #     {
        #         "amount": "0.0001",
        #         "base_fee": "0",
        #         "ccy": "BTC",
        #         "client_id": "x-*********-a0a3c6461459a801",
        #         "created_at": 1714114386250,
        #         "discount_fee": "0",
        #         "filled_amount": "0",
        #         "filled_value": "0",
        #         "last_fill_amount": "0",
        #         "last_fill_price": "0",
        #         "maker_fee_rate": "0.002",
        #         "market": "BTCUSDT",
        #         "market_type": "SPOT",
        #         "order_id": 117178743547,
        #         "price": "61000",
        #         "quote_fee": "0",
        #         "side": "buy",
        #         "taker_fee_rate": "0.002",
        #         "type": "limit",
        #         "unfilled_amount": "0.0001",
        #         "updated_at": 1714114386250
        #     }
        #
        # Spot and Margin fetchClosedOrders
        #
        #     {
        #         "order_id": 117180532345,
        #         "market": "BTCUSDT",
        #         "market_type": "SPOT",
        #         "side": "sell",
        #         "type": "market",
        #         "ccy": "BTC",
        #         "amount": "0.00015484",
        #         "price": "0",
        #         "client_id": "",
        #         "created_at": 1714116494219,
        #         "updated_at": 0,
        #         "base_fee": "0",
        #         "quote_fee": "0.0199931699632",
        #         "discount_fee": "0",
        #         "maker_fee_rate": "0",
        #         "taker_fee_rate": "0.002",
        #         "unfilled_amount": "0",
        #         "filled_amount": "0.00015484",
        #         "filled_value": "9.9965849816"
        #     }
        #
        # Spot, Margin and Swap trigger createOrder, createOrders, editOrder
        #
        #     {
        #         "stop_id": 117180138153
        #     }
        #
        # Swap createOrder, createOrders, editOrder, cancelOrders, cancelOrder, fetchOpenOrders, fetchClosedOrders, closePosition
        #
        #     {
        #         "amount": "0.0001",
        #         "client_id": "x-*********-1471b81d747080a0",
        #         "created_at": 1714116769986,
        #         "fee": "0",
        #         "fee_ccy": "USDT",
        #         "filled_amount": "0",
        #         "filled_value": "0",
        #         "last_filled_amount": "0",
        #         "last_filled_price": "0",
        #         "maker_fee_rate": "0.0003",
        #         "market": "BTCUSDT",
        #         "market_type": "FUTURES",
        #         "order_id": 136913377780,
        #         "price": "61000.42",
        #         "realized_pnl": "0",
        #         "side": "buy",
        #         "taker_fee_rate": "0.0005",
        #         "type": "limit",
        #         "unfilled_amount": "0.0001",
        #         "updated_at": 1714116769986
        #     }
        #
        # Swap stopLossPrice and takeProfitPrice createOrder
        #
        #     {
        #         "adl_level": 1,
        #         "ath_margin_size": "2.14586666",
        #         "ath_position_amount": "0.0001",
        #         "avg_entry_price": "64376",
        #         "bkr_price": "0",
        #         "close_avbl": "0.0001",
        #         "cml_position_value": "6.4376",
        #         "created_at": 1714119054558,
        #         "leverage": "3",
        #         "liq_price": "0",
        #         "maintenance_margin_rate": "0.005",
        #         "maintenance_margin_value": "0.03218632",
        #         "margin_avbl": "2.14586666",
        #         "margin_mode": "cross",
        #         "market": "BTCUSDT",
        #         "market_type": "FUTURES",
        #         "max_position_value": "6.4376",
        #         "open_interest": "0.0001",
        #         "position_id": 303884204,
        #         "position_margin_rate": "3.10624785634397912265",
        #         "realized_pnl": "-0.0032188",
        #         "settle_price": "64376",
        #         "settle_value": "6.4376",
        #         "side": "long",
        #         "stop_loss_price": "62000",
        #         "stop_loss_type": "latest_price",
        #         "take_profit_price": "0",
        #         "take_profit_type": "",
        #         "unrealized_pnl": "0",
        #         "updated_at": 1714119054559
        #     }
        #
        # Swap fetchOrder
        #
        #     {
        #         "amount": "0.0001",
        #         "client_id": "x-*********-da5f31dcd478a829",
        #         "created_at": 1714460987164,
        #         "fee": "0",
        #         "fee_ccy": "USDT",
        #         "filled_amount": "0",
        #         "filled_value": "0",
        #         "last_filled_amount": "0",
        #         "last_filled_price": "0",
        #         "maker_fee_rate": "0.0003",
        #         "market": "BTCUSDT",
        #         "market_type": "FUTURES",
        #         "order_id": 137319868771,
        #         "price": "61000",
        #         "realized_pnl": "0",
        #         "side": "buy",
        #         "status": "open",
        #         "taker_fee_rate": "0.0005",
        #         "type": "limit",
        #         "unfilled_amount": "0.0001",
        #         "updated_at": 1714460987164
        #     }
        #
        # Spot and Margin fetchOrder
        #
        #     {
        #         "amount": "0.0001",
        #         "base_fee": "0",
        #         "ccy": "BTC",
        #         "client_id": "x-*********-da918d6724e3af81",
        #         "created_at": 1714461638958,
        #         "discount_fee": "0",
        #         "filled_amount": "0",
        #         "filled_value": "0",
        #         "last_fill_amount": "0",
        #         "last_fill_price": "0",
        #         "maker_fee_rate": "0.002",
        #         "market": "BTCUSDT",
        #         "market_type": "SPOT",
        #         "order_id": 117492012985,
        #         "price": "61000",
        #         "quote_fee": "0",
        #         "side": "buy",
        #         "status": "open",
        #         "taker_fee_rate": "0.002",
        #         "type": "limit",
        #         "unfilled_amount": "0.0001",
        #         "updated_at": 1714461638958
        #     }
        #
        # Swap trigger fetchOpenOrders, fetchClosedOrders - Spot and Swap trigger cancelOrders, cancelOrder
        #
        #     {
        #         "amount": "0.0001",
        #         "client_id": "x-*********-a7d7714c6478acf6",
        #         "created_at": 1714187923820,
        #         "market": "BTCUSDT",
        #         "market_type": "FUTURES",
        #         "price": "61000",
        #         "side": "buy",
        #         "stop_id": 136984426097,
        #         "trigger_direction": "higher",
        #         "trigger_price": "62000",
        #         "trigger_price_type": "latest_price",
        #         "type": "limit",
        #         "updated_at": 1714187974363
        #     }
        #
        # Spot and Margin trigger fetchOpenOrders, fetchClosedOrders
        #
        #     {
        #         "stop_id": 117586439530,
        #         "market": "BTCUSDT",
        #         "market_type": "SPOT",
        #         "ccy": "BTC",
        #         "side": "buy",
        #         "type": "limit",
        #         "amount": "0.0001",
        #         "price": "51000",
        #         "trigger_price": "52000",
        #         "trigger_direction": "higher",
        #         "trigger_price_type": "mark_price",
        #         "client_id": "x-*********-df61777094c69312",
        #         "created_at": 1714551237335,
        #         "updated_at": 1714551237335
        #     }
        #
        rawStatus = self.safe_string(order, 'status')
        timestamp = self.safe_integer(order, 'created_at')
        updatedTimestamp = self.safe_integer(order, 'updated_at')
        if updatedTimestamp == 0:
            updatedTimestamp = timestamp
        marketId = self.safe_string(order, 'market')
        defaultType = self.safe_string(self.options, 'defaultType')
        orderType = self.safe_string_lower(order, 'market_type', defaultType)
        if orderType == 'futures':
            orderType = 'swap'
        marketType = 'swap' if (orderType == 'swap') else 'spot'
        market = self.safe_market(marketId, market, None, marketType)
        feeCurrencyId = self.safe_string(order, 'fee_ccy')
        feeCurrency = self.safe_currency_code(feeCurrencyId)
        if feeCurrency is None:
            feeCurrency = market['quote']
        side = self.safe_string(order, 'side')
        if side == 'long':
            side = 'buy'
        elif side == 'short':
            side = 'sell'
        clientOrderId = self.safe_string(order, 'client_id')
        if clientOrderId == '':
            clientOrderId = None
        return self.safe_order({
            'id': self.safe_string_n(order, ['position_id', 'order_id', 'stop_id']),
            'clientOrderId': clientOrderId,
            'datetime': self.iso8601(timestamp),
            'timestamp': timestamp,
            'lastTradeTimestamp': updatedTimestamp,
            'status': self.parse_order_status(rawStatus),
            'symbol': market['symbol'],
            'type': self.safe_string(order, 'type'),
            'timeInForce': None,
            'postOnly': None,
            'reduceOnly': None,
            'side': side,
            'price': self.safe_string(order, 'price'),
            'triggerPrice': self.safe_string(order, 'trigger_price'),
            'takeProfitPrice': self.safe_number(order, 'take_profit_price'),
            'stopLossPrice': self.safe_number(order, 'stop_loss_price'),
            'cost': self.safe_string(order, 'filled_value'),
            'average': self.safe_string(order, 'avg_entry_price'),
            'amount': self.safe_string(order, 'amount'),
            'filled': self.safe_string(order, 'filled_amount'),
            'remaining': self.safe_string(order, 'unfilled_amount'),
            'trades': None,
            'fee': {
                'currency': feeCurrency,
                'cost': self.safe_string_2(order, 'quote_fee', 'fee'),
            },
            'info': order,
        }, market)

    async def create_market_buy_order_with_cost(self, symbol: str, cost: float, params={}):
        """
        create a market buy order by providing the symbol and cost

        https://viabtc.github.io/coinex_api_en_doc/spot/#docsspot003_trade003_market_order
        https://docs.coinex.com/api/v2/spot/order/http/put-order

        :param str symbol: unified symbol of the market to create an order in
        :param float cost: how much you want to trade in units of the quote currency
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        if not market['spot']:
            raise NotSupported(self.id + ' createMarketBuyOrderWithCost() supports spot orders only')
        params['createMarketBuyOrderRequiresPrice'] = False
        return await self.create_order(symbol, 'market', 'buy', cost, None, params)

    def create_order_request(self, symbol: str, type: OrderType, side: OrderSide, amount: float, price: Num = None, params={}):
        market = self.market(symbol)
        swap = market['swap']
        clientOrderId = self.safe_string_2(params, 'client_id', 'clientOrderId')
        triggerPrice = self.safe_string_2(params, 'stopPrice', 'triggerPrice')
        stopLossPrice = self.safe_string(params, 'stopLossPrice')
        takeProfitPrice = self.safe_string(params, 'takeProfitPrice')
        option = self.safe_string(params, 'option')
        isMarketOrder = type == 'market'
        postOnly = self.is_post_only(isMarketOrder, option == 'maker_only', params)
        timeInForceRaw = self.safe_string_upper(params, 'timeInForce')
        reduceOnly = self.safe_bool(params, 'reduceOnly')
        if reduceOnly:
            if not market['swap']:
                raise InvalidOrder(self.id + ' createOrder() does not support reduceOnly for ' + market['type'] + ' orders, reduceOnly orders are supported for swap markets only')
        request: dict = {
            'market': market['id'],
        }
        if clientOrderId is None:
            defaultId = 'x-*********'
            brokerId = self.safe_string(self.options, 'brokerId', defaultId)
            request['client_id'] = brokerId + '-' + self.uuid16()
        else:
            request['client_id'] = clientOrderId
        if (stopLossPrice is None) and (takeProfitPrice is None):
            if not reduceOnly:
                request['side'] = side
            requestType = type
            if postOnly:
                requestType = 'maker_only'
            elif timeInForceRaw is not None:
                if timeInForceRaw == 'IOC':
                    requestType = 'ioc'
                elif timeInForceRaw == 'FOK':
                    requestType = 'fok'
            if not isMarketOrder:
                request['price'] = self.price_to_precision(symbol, price)
            request['type'] = requestType
        if swap:
            request['market_type'] = 'FUTURES'
            if stopLossPrice or takeProfitPrice:
                if stopLossPrice:
                    request['stop_loss_price'] = self.price_to_precision(symbol, stopLossPrice)
                    request['stop_loss_type'] = self.safe_string(params, 'stop_type', 'latest_price')
                elif takeProfitPrice:
                    request['take_profit_price'] = self.price_to_precision(symbol, takeProfitPrice)
                    request['take_profit_type'] = self.safe_string(params, 'stop_type', 'latest_price')
            else:
                request['amount'] = self.amount_to_precision(symbol, amount)
                if triggerPrice is not None:
                    request['trigger_price'] = self.price_to_precision(symbol, triggerPrice)
                    request['trigger_price_type'] = self.safe_string(params, 'stop_type', 'latest_price')
        else:
            marginMode = None
            marginMode, params = self.handle_margin_mode_and_params('createOrder', params)
            if marginMode is not None:
                request['market_type'] = 'MARGIN'
            else:
                request['market_type'] = 'SPOT'
            if (type == 'market') and (side == 'buy'):
                createMarketBuyOrderRequiresPrice = True
                createMarketBuyOrderRequiresPrice, params = self.handle_option_and_params(params, 'createOrder', 'createMarketBuyOrderRequiresPrice', True)
                cost = self.safe_number(params, 'cost')
                params = self.omit(params, 'cost')
                if createMarketBuyOrderRequiresPrice:
                    if (price is None) and (cost is None):
                        raise InvalidOrder(self.id + ' createOrder() requires the price argument for market buy orders to calculate the total cost to spend(amount * price), alternatively set the createMarketBuyOrderRequiresPrice option or param to False and pass the cost to spend in the amount argument')
                    else:
                        amountString = self.number_to_string(amount)
                        priceString = self.number_to_string(price)
                        quoteAmount = self.parse_to_numeric(Precise.string_mul(amountString, priceString))
                        costRequest = cost if (cost is not None) else quoteAmount
                        request['amount'] = self.cost_to_precision(symbol, costRequest)
                else:
                    request['amount'] = self.cost_to_precision(symbol, amount)
            else:
                request['amount'] = self.amount_to_precision(symbol, amount)
            if triggerPrice is not None:
                request['trigger_price'] = self.price_to_precision(symbol, triggerPrice)
        params = self.omit(params, ['reduceOnly', 'timeInForce', 'postOnly', 'stopPrice', 'triggerPrice', 'stopLossPrice', 'takeProfitPrice'])
        return self.extend(request, params)

    async def create_order(self, symbol: str, type: OrderType, side: OrderSide, amount: float, price: Num = None, params={}):
        """
        create a trade order

        https://docs.coinex.com/api/v2/spot/order/http/put-order
        https://docs.coinex.com/api/v2/spot/order/http/put-stop-order
        https://docs.coinex.com/api/v2/futures/order/http/put-order
        https://docs.coinex.com/api/v2/futures/order/http/put-stop-order
        https://docs.coinex.com/api/v2/futures/position/http/close-position
        https://docs.coinex.com/api/v2/futures/position/http/set-position-stop-loss
        https://docs.coinex.com/api/v2/futures/position/http/set-position-take-profit

        :param str symbol: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much you want to trade in units of the base currency
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param float [params.triggerPrice]: price to trigger stop orders
        :param float [params.stopLossPrice]: price to trigger stop loss orders
        :param float [params.takeProfitPrice]: price to trigger take profit orders
        :param str [params.timeInForce]: 'GTC', 'IOC', 'FOK', 'PO'
        :param boolean [params.postOnly]: set to True if you wish to make a post only order
        :param boolean [params.reduceOnly]: *contract only* indicates if self order is to reduce the size of a position
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        reduceOnly = self.safe_bool(params, 'reduceOnly')
        triggerPrice = self.safe_string_2(params, 'stopPrice', 'triggerPrice')
        stopLossTriggerPrice = self.safe_string(params, 'stopLossPrice')
        takeProfitTriggerPrice = self.safe_string(params, 'takeProfitPrice')
        isTriggerOrder = triggerPrice is not None
        isStopLossTriggerOrder = stopLossTriggerPrice is not None
        isTakeProfitTriggerOrder = takeProfitTriggerPrice is not None
        isStopLossOrTakeProfitTrigger = isStopLossTriggerOrder or isTakeProfitTriggerOrder
        request = self.create_order_request(symbol, type, side, amount, price, params)
        response = None
        if market['spot']:
            if isTriggerOrder:
                response = await self.v2PrivatePostSpotStopOrder(request)
                #
                #     {
                #         "code": 0,
                #         "data": {
                #             "stop_id": 117180138153
                #         },
                #         "message": "OK"
                #     }
                #
            else:
                response = await self.v2PrivatePostSpotOrder(request)
                #
                #     {
                #         "code": 0,
                #         "data": {
                #             "amount": "0.0001",
                #             "base_fee": "0",
                #             "ccy": "BTC",
                #             "client_id": "x-*********-a0a3c6461459a801",
                #             "created_at": 1714114386250,
                #             "discount_fee": "0",
                #             "filled_amount": "0",
                #             "filled_value": "0",
                #             "last_fill_amount": "0",
                #             "last_fill_price": "0",
                #             "maker_fee_rate": "0.002",
                #             "market": "BTCUSDT",
                #             "market_type": "SPOT",
                #             "order_id": 117178743547,
                #             "price": "61000",
                #             "quote_fee": "0",
                #             "side": "buy",
                #             "taker_fee_rate": "0.002",
                #             "type": "limit",
                #             "unfilled_amount": "0.0001",
                #             "updated_at": 1714114386250
                #         },
                #         "message": "OK"
                #     }
                #
        else:
            if isTriggerOrder:
                response = await self.v2PrivatePostFuturesStopOrder(request)
                #
                #     {
                #         "code": 0,
                #         "data": {
                #             "stop_id": 136915460994
                #         },
                #         "message": "OK"
                #     }
                #
            elif isStopLossOrTakeProfitTrigger:
                if isStopLossTriggerOrder:
                    response = await self.v2PrivatePostFuturesSetPositionStopLoss(request)
                    #
                    #     {
                    #         "code": 0,
                    #         "data": {
                    #             "adl_level": 1,
                    #             "ath_margin_size": "2.14586666",
                    #             "ath_position_amount": "0.0001",
                    #             "avg_entry_price": "64376",
                    #             "bkr_price": "0",
                    #             "close_avbl": "0.0001",
                    #             "cml_position_value": "6.4376",
                    #             "created_at": 1714119054558,
                    #             "leverage": "3",
                    #             "liq_price": "0",
                    #             "maintenance_margin_rate": "0.005",
                    #             "maintenance_margin_value": "0.03218632",
                    #             "margin_avbl": "2.14586666",
                    #             "margin_mode": "cross",
                    #             "market": "BTCUSDT",
                    #             "market_type": "FUTURES",
                    #             "max_position_value": "6.4376",
                    #             "open_interest": "0.0001",
                    #             "position_id": 303884204,
                    #             "position_margin_rate": "3.10624785634397912265",
                    #             "realized_pnl": "-0.0032188",
                    #             "settle_price": "64376",
                    #             "settle_value": "6.4376",
                    #             "side": "long",
                    #             "stop_loss_price": "62000",
                    #             "stop_loss_type": "latest_price",
                    #             "take_profit_price": "0",
                    #             "take_profit_type": "",
                    #             "unrealized_pnl": "0",
                    #             "updated_at": 1714119054559
                    #         },
                    #         "message": "OK"
                    #     }
                    #
                elif isTakeProfitTriggerOrder:
                    response = await self.v2PrivatePostFuturesSetPositionTakeProfit(request)
                    #
                    #     {
                    #         "code": 0,
                    #         "data": {
                    #             "adl_level": 1,
                    #             "ath_margin_size": "2.14586666",
                    #             "ath_position_amount": "0.0001",
                    #             "avg_entry_price": "64376",
                    #             "bkr_price": "0",
                    #             "close_avbl": "0.0001",
                    #             "cml_position_value": "6.4376",
                    #             "created_at": 1714119054558,
                    #             "leverage": "3",
                    #             "liq_price": "0",
                    #             "maintenance_margin_rate": "0.005",
                    #             "maintenance_margin_value": "0.03218632",
                    #             "margin_avbl": "2.14586666",
                    #             "margin_mode": "cross",
                    #             "market": "BTCUSDT",
                    #             "market_type": "FUTURES",
                    #             "max_position_value": "6.4376",
                    #             "open_interest": "0.0001",
                    #             "position_id": 303884204,
                    #             "position_margin_rate": "3.10624785634397912265",
                    #             "realized_pnl": "-0.0032188",
                    #             "settle_price": "64376",
                    #             "settle_value": "6.4376",
                    #             "side": "long",
                    #             "stop_loss_price": "62000",
                    #             "stop_loss_type": "latest_price",
                    #             "take_profit_price": "70000",
                    #             "take_profit_type": "latest_price",
                    #             "unrealized_pnl": "0",
                    #             "updated_at": 1714119054559
                    #         },
                    #         "message": "OK"
                    #     }
                    #
            else:
                if reduceOnly:
                    response = await self.v2PrivatePostFuturesClosePosition(request)
                    #
                    #     {
                    #         "code": 0,
                    #         "data": {
                    #             "amount": "0.0001",
                    #             "client_id": "x-*********-4f264600c432ac06",
                    #             "created_at": 1714119323764,
                    #             "fee": "0.003221",
                    #             "fee_ccy": "USDT",
                    #             "filled_amount": "0.0001",
                    #             "filled_value": "6.442017",
                    #             "last_filled_amount": "0.0001",
                    #             "last_filled_price": "64420.17",
                    #             "maker_fee_rate": "0",
                    #             "market": "BTCUSDT",
                    #             "market_type": "FUTURES",
                    #             "order_id": 136915813578,
                    #             "price": "0",
                    #             "realized_pnl": "0.004417",
                    #             "side": "sell",
                    #             "taker_fee_rate": "0.0005",
                    #             "type": "market",
                    #             "unfilled_amount": "0",
                    #             "updated_at": 1714119323764
                    #         },
                    #         "message": "OK"
                    #     }
                    #
                else:
                    response = await self.v2PrivatePostFuturesOrder(request)
                    #
                    #     {
                    #         "code": 0,
                    #         "data": {
                    #             "amount": "0.0001",
                    #             "client_id": "x-*********-1471b81d747080a0",
                    #             "created_at": 1714116769986,
                    #             "fee": "0",
                    #             "fee_ccy": "USDT",
                    #             "filled_amount": "0",
                    #             "filled_value": "0",
                    #             "last_filled_amount": "0",
                    #             "last_filled_price": "0",
                    #             "maker_fee_rate": "0.0003",
                    #             "market": "BTCUSDT",
                    #             "market_type": "FUTURES",
                    #             "order_id": 136913377780,
                    #             "price": "61000.42",
                    #             "realized_pnl": "0",
                    #             "side": "buy",
                    #             "taker_fee_rate": "0.0005",
                    #             "type": "limit",
                    #             "unfilled_amount": "0.0001",
                    #             "updated_at": 1714116769986
                    #         },
                    #         "message": "OK"
                    #     }
                    #
        data = self.safe_dict(response, 'data', {})
        return self.parse_order(data, market)

    async def create_orders(self, orders: List[OrderRequest], params={}) -> List[Order]:
        """
        create a list of trade orders(all orders should be of the same symbol)

        https://docs.coinex.com/api/v2/spot/order/http/put-multi-order
        https://docs.coinex.com/api/v2/spot/order/http/put-multi-stop-order
        https://docs.coinex.com/api/v2/futures/order/http/put-multi-order
        https://docs.coinex.com/api/v2/futures/order/http/put-multi-stop-order

        :param Array orders: list of orders to create, each object should contain the parameters required by createOrder, namely symbol, type, side, amount, price and params
        :param dict [params]: extra parameters specific to the api endpoint
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        ordersRequests = []
        symbol = None
        reduceOnly = False
        isTriggerOrder = False
        isStopLossOrTakeProfitTrigger = False
        for i in range(0, len(orders)):
            rawOrder = orders[i]
            marketId = self.safe_string(rawOrder, 'symbol')
            if symbol is None:
                symbol = marketId
            else:
                if symbol != marketId:
                    raise BadRequest(self.id + ' createOrders() requires all orders to have the same symbol')
            type = self.safe_string(rawOrder, 'type')
            side = self.safe_string(rawOrder, 'side')
            amount = self.safe_value(rawOrder, 'amount')
            price = self.safe_value(rawOrder, 'price')
            orderParams = self.safe_value(rawOrder, 'params', {})
            if type != 'limit':
                raise NotSupported(self.id + ' createOrders() does not support ' + type + ' orders, only limit orders are accepted')
            reduceOnly = self.safe_value(orderParams, 'reduceOnly')
            triggerPrice = self.safe_number_2(orderParams, 'stopPrice', 'triggerPrice')
            stopLossTriggerPrice = self.safe_number(orderParams, 'stopLossPrice')
            takeProfitTriggerPrice = self.safe_number(orderParams, 'takeProfitPrice')
            isTriggerOrder = triggerPrice is not None
            isStopLossTriggerOrder = stopLossTriggerPrice is not None
            isTakeProfitTriggerOrder = takeProfitTriggerPrice is not None
            isStopLossOrTakeProfitTrigger = isStopLossTriggerOrder or isTakeProfitTriggerOrder
            orderRequest = self.create_order_request(marketId, type, side, amount, price, orderParams)
            ordersRequests.append(orderRequest)
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
            'orders': ordersRequests,
        }
        response = None
        if market['spot']:
            if isTriggerOrder:
                response = await self.v2PrivatePostSpotBatchStopOrder(request)
                #
                #     {
                #         "code": 0,
                #         "data": [
                #             {
                #                 "code": 0,
                #                 "data": {
                #                     "stop_id": 117186257510
                #                 },
                #                 "message": "OK"
                #             },
                #         ],
                #         "message": "OK"
                #     }
                #
            else:
                response = await self.v2PrivatePostSpotBatchOrder(request)
                #
                #     {
                #         "code": 0,
                #         "data": [
                #             {
                #                 "amount": "0.0001",
                #                 "base_fee": "0",
                #                 "ccy": "BTC",
                #                 "client_id": "x-*********-f3651372049dab0d",
                #                 "created_at": 1714121403450,
                #                 "discount_fee": "0",
                #                 "filled_amount": "0",
                #                 "filled_value": "0",
                #                 "last_fill_amount": "0",
                #                 "last_fill_price": "0",
                #                 "maker_fee_rate": "0.002",
                #                 "market": "BTCUSDT",
                #                 "market_type": "SPOT",
                #                 "order_id": 117185362233,
                #                 "price": "61000",
                #                 "quote_fee": "0",
                #                 "side": "buy",
                #                 "taker_fee_rate": "0.002",
                #                 "type": "limit",
                #                 "unfilled_amount": "0.0001",
                #                 "updated_at": 1714121403450
                #             },
                #             {
                #                 "code": 3109,
                #                 "data": null,
                #                 "message": "balance not enough"
                #             }
                #         ],
                #         "message": "OK"
                #     }
                #
        else:
            if isTriggerOrder:
                response = await self.v2PrivatePostFuturesBatchStopOrder(request)
                #
                #     {
                #         "code": 0,
                #         "data": [
                #             {
                #                 "code": 0,
                #                 "data": {
                #                     "stop_id": 136919625994
                #                 },
                #                 "message": "OK"
                #             },
                #         ],
                #         "message": "OK"
                #     }
                #
            elif isStopLossOrTakeProfitTrigger:
                raise NotSupported(self.id + ' createOrders() does not support stopLossPrice or takeProfitPrice orders')
            else:
                if reduceOnly:
                    raise NotSupported(self.id + ' createOrders() does not support reduceOnly orders')
                else:
                    response = await self.v2PrivatePostFuturesBatchOrder(request)
                    #
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "code": 0,
                    #                 "data": {
                    #                     "amount": "0.0001",
                    #                     "client_id": "x-*********-2cb7436f3462a654",
                    #                     "created_at": 1714122832493,
                    #                     "fee": "0",
                    #                     "fee_ccy": "USDT",
                    #                     "filled_amount": "0",
                    #                     "filled_value": "0",
                    #                     "last_filled_amount": "0",
                    #                     "last_filled_price": "0",
                    #                     "maker_fee_rate": "0.0003",
                    #                     "market": "BTCUSDT",
                    #                     "market_type": "FUTURES",
                    #                     "order_id": 136918835063,
                    #                     "price": "61000",
                    #                     "realized_pnl": "0",
                    #                     "side": "buy",
                    #                     "taker_fee_rate": "0.0005",
                    #                     "type": "limit",
                    #                     "unfilled_amount": "0.0001",
                    #                     "updated_at": 1714122832493
                    #                 },
                    #                 "message": "OK"
                    #             },
                    #         ],
                    #         "message": "OK"
                    #     }
                    #
        data = self.safe_list(response, 'data', [])
        results = []
        for i in range(0, len(data)):
            entry = data[i]
            status = None
            code = self.safe_integer(entry, 'code')
            if code is not None:
                if code != 0:
                    status = 'rejected'
                else:
                    status = 'open'
            innerData = self.safe_dict(entry, 'data', {})
            order = None
            if market['spot'] and not isTriggerOrder:
                entry['status'] = status
                order = self.parse_order(entry, market)
            else:
                innerData['status'] = status
                order = self.parse_order(innerData, market)
            results.append(order)
        return results

    async def cancel_orders(self, ids, symbol: Str = None, params={}):
        """
        cancel multiple orders

        https://docs.coinex.com/api/v2/spot/order/http/cancel-batch-order
        https://docs.coinex.com/api/v2/spot/order/http/cancel-batch-stop-order
        https://docs.coinex.com/api/v2/futures/order/http/cancel-batch-order
        https://docs.coinex.com/api/v2/futures/order/http/cancel-batch-stop-order

        :param str[] ids: order ids
        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param boolean [params.trigger]: set to True for canceling stop orders
        :returns dict: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' cancelOrders() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
        }
        trigger = self.safe_bool_2(params, 'stop', 'trigger')
        params = self.omit(params, ['stop', 'trigger'])
        response = None
        requestIds = []
        for i in range(0, len(ids)):
            requestIds.append(int(ids[i]))
        if trigger:
            request['stop_ids'] = requestIds
        else:
            request['order_ids'] = requestIds
        if market['spot']:
            if trigger:
                response = await self.v2PrivatePostSpotCancelBatchStopOrder(self.extend(request, params))
                #
                #     {
                #         "code": 0,
                #         "data": [
                #             {
                #                 "code": 0,
                #                 "data": {
                #                     "amount": "0.0001",
                #                     "ccy": "BTC",
                #                     "client_id": "x-*********-8e33d6f4a4bcb022",
                #                     "created_at": 1714188827291,
                #                     "market": "BTCUSDT",
                #                     "market_type": "SPOT",
                #                     "price": "61000",
                #                     "side": "buy",
                #                     "stop_id": 117248845854,
                #                     "trigger_direction": "higher",
                #                     "trigger_price": "62000",
                #                     "trigger_price_type": "mark_price",
                #                     "type": "limit",
                #                     "updated_at": 1714188827291
                #                 },
                #                 "message": "OK"
                #             },
                #         ],
                #         "message": "OK"
                #     }
                #
            else:
                response = await self.v2PrivatePostSpotCancelBatchOrder(self.extend(request, params))
                #
                #     {
                #         "code": 0,
                #         "data": [
                #             {
                #                 "code": 0,
                #                 "data": {
                #                     "amount": "0.0001",
                #                     "base_fee": "0",
                #                     "ccy": "BTC",
                #                     "client_id": "x-*********-c1cc78e5b42d8c4e",
                #                     "created_at": 1714188449497,
                #                     "discount_fee": "0",
                #                     "filled_amount": "0",
                #                     "filled_value": "0",
                #                     "last_fill_amount": "0",
                #                     "last_fill_price": "0",
                #                     "maker_fee_rate": "0.002",
                #                     "market": "BTCUSDT",
                #                     "market_type": "SPOT",
                #                     "order_id": 117248494358,
                #                     "price": "60000",
                #                     "quote_fee": "0",
                #                     "side": "buy",
                #                     "taker_fee_rate": "0.002",
                #                     "type": "limit",
                #                     "unfilled_amount": "0.0001",
                #                     "updated_at": 1714188449497
                #                 },
                #                 "message": ""
                #             },
                #         ],
                #         "message": "OK"
                #     }
                #
        else:
            request['market_type'] = 'FUTURES'
            if trigger:
                response = await self.v2PrivatePostFuturesCancelBatchStopOrder(self.extend(request, params))
                #
                #     {
                #         "code": 0,
                #         "data": [
                #             {
                #                 "code": 0,
                #                 "data": {
                #                     "amount": "0.0001",
                #                     "client_id": "x-*********-a7d7714c6478acf6",
                #                     "created_at": 1714187923820,
                #                     "market": "BTCUSDT",
                #                     "market_type": "FUTURES",
                #                     "price": "61000",
                #                     "side": "buy",
                #                     "stop_id": 136984426097,
                #                     "trigger_direction": "higher",
                #                     "trigger_price": "62000",
                #                     "trigger_price_type": "latest_price",
                #                     "type": "limit",
                #                     "updated_at": 1714187974363
                #                 },
                #                 "message": ""
                #             },
                #         ],
                #         "message": "OK"
                #     }
                #
            else:
                response = await self.v2PrivatePostFuturesCancelBatchOrder(self.extend(request, params))
                #
                #     {
                #         "code": 0,
                #         "data": [
                #             {
                #                 "code": 0,
                #                 "data": {
                #                     "amount": "0.0001",
                #                     "client_id": "x-*********-9f80fde284339a72",
                #                     "created_at": 1714187491784,
                #                     "fee": "0",
                #                     "fee_ccy": "USDT",
                #                     "filled_amount": "0",
                #                     "filled_value": "0",
                #                     "last_filled_amount": "0",
                #                     "last_filled_price": "0",
                #                     "maker_fee_rate": "0.0003",
                #                     "market": "BTCUSDT",
                #                     "market_type": "FUTURES",
                #                     "order_id": 136983851788,
                #                     "price": "61000",
                #                     "realized_pnl": "0",
                #                     "side": "buy",
                #                     "taker_fee_rate": "0.0005",
                #                     "type": "limit",
                #                     "unfilled_amount": "0.0001",
                #                     "updated_at": 1714187567079
                #                 },
                #                 "message": ""
                #             },
                #         ],
                #         "message": "OK"
                #     }
                #
        data = self.safe_list(response, 'data', [])
        results = []
        for i in range(0, len(data)):
            entry = data[i]
            item = self.safe_dict(entry, 'data', {})
            order = self.parse_order(item, market)
            results.append(order)
        return results

    async def edit_order(self, id: str, symbol: str, type: OrderType, side: OrderSide, amount: Num = None, price: Num = None, params={}):
        """
        edit a trade order

        https://docs.coinex.com/api/v2/spot/order/http/edit-order
        https://docs.coinex.com/api/v2/spot/order/http/edit-stop-order
        https://docs.coinex.com/api/v2/futures/order/http/edit-order
        https://docs.coinex.com/api/v2/futures/order/http/edit-stop-order

        :param str id: order id
        :param str symbol: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much of the currency you want to trade in units of the base currency
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param float [params.triggerPrice]: the price to trigger stop orders
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' editOrder() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
        }
        if amount is not None:
            request['amount'] = self.amount_to_precision(symbol, amount)
        if price is not None:
            request['price'] = self.price_to_precision(symbol, price)
        response = None
        triggerPrice = self.safe_string_n(params, ['stopPrice', 'triggerPrice', 'trigger_price'])
        params = self.omit(params, ['stopPrice', 'triggerPrice'])
        isTriggerOrder = triggerPrice is not None
        if isTriggerOrder:
            request['trigger_price'] = self.price_to_precision(symbol, triggerPrice)
            request['stop_id'] = self.parse_to_numeric(id)
        else:
            request['order_id'] = self.parse_to_numeric(id)
        marginMode = None
        marginMode, params = self.handle_margin_mode_and_params('editOrder', params)
        if market['spot']:
            if marginMode is not None:
                request['market_type'] = 'MARGIN'
            else:
                request['market_type'] = 'SPOT'
            if isTriggerOrder:
                response = await self.v2PrivatePostSpotModifyStopOrder(self.extend(request, params))
                #
                #     {
                #         "code": 0,
                #         "data": {
                #             "stop_id": 117337235167
                #         },
                #         "message": "OK"
                #     }
                #
            else:
                response = await self.v2PrivatePostSpotModifyOrder(self.extend(request, params))
                #
                #     {
                #         "code": 0,
                #         "data": {
                #             "amount": "0.0001",
                #             "base_fee": "0",
                #             "ccy": "BTC",
                #             "client_id": "x-*********-87eb2bebf42882d8",
                #             "created_at": 1714290302047,
                #             "discount_fee": "0",
                #             "filled_amount": "0",
                #             "filled_value": "0",
                #             "last_fill_amount": "0",
                #             "last_fill_price": "0",
                #             "maker_fee_rate": "0.002",
                #             "market": "BTCUSDT",
                #             "market_type": "SPOT",
                #             "order_id": 117336922195,
                #             "price": "61000",
                #             "quote_fee": "0",
                #             "side": "buy",
                #             "status": "open",
                #             "taker_fee_rate": "0.002",
                #             "type": "limit",
                #             "unfilled_amount": "0.0001",
                #             "updated_at": 1714290191141
                #         },
                #         "message": "OK"
                #     }
                #
        else:
            request['market_type'] = 'FUTURES'
            if isTriggerOrder:
                response = await self.v2PrivatePostFuturesModifyStopOrder(self.extend(request, params))
                #
                #     {
                #         "code": 0,
                #         "data": {
                #             "stop_id": 137091875605
                #         },
                #         "message": "OK"
                #     }
                #
            else:
                response = await self.v2PrivatePostFuturesModifyOrder(self.extend(request, params))
                #
                #     {
                #         "code": 0,
                #         "data": {
                #             "amount": "0.0001",
                #             "client_id": "x-*********-3f2d09191462b207",
                #             "created_at": 1714290927630,
                #             "fee": "0",
                #             "fee_ccy": "USDT",
                #             "filled_amount": "0",
                #             "filled_value": "0",
                #             "last_filled_amount": "0",
                #             "last_filled_price": "0",
                #             "maker_fee_rate": "0.0003",
                #             "market": "BTCUSDT",
                #             "market_type": "FUTURES",
                #             "order_id": 137091566717,
                #             "price": "61000",
                #             "realized_pnl": "0",
                #             "side": "buy",
                #             "taker_fee_rate": "0.0005",
                #             "type": "limit",
                #             "unfilled_amount": "0.0001",
                #             "updated_at": 1714290927630
                #         },
                #         "message": "OK"
                #     }
                #
        data = self.safe_dict(response, 'data', {})
        return self.parse_order(data, market)

    async def cancel_order(self, id: str, symbol: Str = None, params={}):
        """
        cancels an open order

        https://docs.coinex.com/api/v2/spot/order/http/cancel-order
        https://docs.coinex.com/api/v2/spot/order/http/cancel-stop-order
        https://docs.coinex.com/api/v2/spot/order/http/cancel-order-by-client-id
        https://docs.coinex.com/api/v2/spot/order/http/cancel-stop-order-by-client-id
        https://docs.coinex.com/api/v2/futures/order/http/cancel-order
        https://docs.coinex.com/api/v2/futures/order/http/cancel-stop-order
        https://docs.coinex.com/api/v2/futures/order/http/cancel-order-by-client-id
        https://docs.coinex.com/api/v2/futures/order/http/cancel-stop-order-by-client-id

        :param str id: order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.clientOrderId]: client order id, defaults to id if not passed
        :param boolean [params.trigger]: set to True for canceling a trigger order
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' cancelOrder() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        isTriggerOrder = self.safe_bool_2(params, 'stop', 'trigger')
        swap = market['swap']
        request: dict = {
            'market': market['id'],
        }
        marginMode = None
        marginMode, params = self.handle_margin_mode_and_params('cancelOrder', params)
        if swap:
            request['market_type'] = 'FUTURES'
        else:
            if marginMode is not None:
                request['market_type'] = 'MARGIN'
            else:
                request['market_type'] = 'SPOT'
        clientOrderId = self.safe_string_2(params, 'client_id', 'clientOrderId')
        params = self.omit(params, ['stop', 'trigger', 'clientOrderId'])
        response = None
        if clientOrderId is not None:
            request['client_id'] = clientOrderId
            if isTriggerOrder:
                if swap:
                    response = await self.v2PrivatePostFuturesCancelStopOrderByClientId(self.extend(request, params))
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "code": 0,
                    #                 "data": {
                    #                     "amount": "0.0001",
                    #                     "client_id": "client01",
                    #                     "created_at": 1714368624473,
                    #                     "market": "BTCUSDT",
                    #                     "market_type": "FUTURES",
                    #                     "price": "61000",
                    #                     "side": "buy",
                    #                     "stop_id": 137175823891,
                    #                     "trigger_direction": "higher",
                    #                     "trigger_price": "61500",
                    #                     "trigger_price_type": "latest_price",
                    #                     "type": "limit",
                    #                     "updated_at": 1714368661444
                    #                 },
                    #                 "message": ""
                    #             }
                    #         ],
                    #         "message": "OK"
                    #     }
                else:
                    response = await self.v2PrivatePostSpotCancelStopOrderByClientId(self.extend(request, params))
                    #     {
                    #         "code" :0,
                    #         "data": [
                    #             {
                    #                 "code": 0,
                    #                 "data": {
                    #                     "amount": "0.0001",
                    #                     "ccy": "BTC",
                    #                     "client_id": "client01",
                    #                     "created_at": 1714366950279,
                    #                     "market": "BTCUSDT",
                    #                     "market_type": "SPOT",
                    #                     "price": "61000",
                    #                     "side": "buy",
                    #                     "stop_id": 117402512706,
                    #                     "trigger_direction": "higher",
                    #                     "trigger_price": "61500",
                    #                     "trigger_price_type": "mark_price",
                    #                     "type": "limit",
                    #                     "updated_at": 1714366950279
                    #                 },
                    #                 "message": "OK"
                    #             }
                    #         ],
                    #         "message": "OK"
                    #     }
            else:
                if swap:
                    response = await self.v2PrivatePostFuturesCancelOrderByClientId(self.extend(request, params))
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "code": 0,
                    #                 "data": {
                    #                     "amount": "0.0001",
                    #                     "client_id": "x-*********-bf60e24bb437a3df",
                    #                     "created_at": 1714368416437,
                    #                     "fee": "0",
                    #                     "fee_ccy": "USDT",
                    #                     "filled_amount": "0",
                    #                     "filled_value": "0",
                    #                     "last_filled_amount": "0",
                    #                     "last_filled_price": "0",
                    #                     "maker_fee_rate": "0.0003",
                    #                     "market": "BTCUSDT",
                    #                     "market_type": "FUTURES",
                    #                     "order_id": 137175616437,
                    #                     "price": "61000",
                    #                     "realized_pnl": "0",
                    #                     "side": "buy",
                    #                     "taker_fee_rate": "0.0005",
                    #                     "type": "limit",
                    #                     "unfilled_amount": "0.0001",
                    #                     "updated_at": 1714368507174
                    #                 },
                    #                 "message": ""
                    #             }
                    #         ],
                    #         "message": "OK"
                    #     }
                else:
                    response = await self.v2PrivatePostSpotCancelOrderByClientId(self.extend(request, params))
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "code": 0,
                    #                 "data": {
                    #                     "amount": "0.0001",
                    #                     "base_fee": "0",
                    #                     "ccy": "BTC",
                    #                     "client_id": "x-*********-d49eaca5f412afc8",
                    #                     "created_at": 1714366502807,
                    #                     "discount_fee": "0",
                    #                     "filled_amount": "0",
                    #                     "filled_value": "0",
                    #                     "last_fill_amount": "0",
                    #                     "last_fill_price": "0",
                    #                     "maker_fee_rate": "0.002",
                    #                     "market": "BTCUSDT",
                    #                     "market_type": "SPOT",
                    #                     "order_id": 117402157490,
                    #                     "price": "61000",
                    #                     "quote_fee": "0",
                    #                     "side": "buy",
                    #                     "taker_fee_rate": "0.002",
                    #                     "type": "limit",
                    #                     "unfilled_amount": "0.0001",
                    #                     "updated_at": 1714366502807
                    #                 },
                    #                 "message": "OK"
                    #             }
                    #         ],
                    #         "message": "OK"
                    #     }
        else:
            if isTriggerOrder:
                request['stop_id'] = self.parse_to_numeric(id)
                if swap:
                    response = await self.v2PrivatePostFuturesCancelStopOrder(self.extend(request, params))
                    #     {
                    #         "code": 0,
                    #         "data": {
                    #             "amount": "0.0001",
                    #             "ccy": "BTC",
                    #             "client_id": "x-*********-f21ecfd7542abf1f",
                    #             "created_at": 1714366177334,
                    #             "market": "BTCUSDT",
                    #             "market_type": "SPOT",
                    #             "price": "61000",
                    #             "side": "buy",
                    #             "stop_id": 117401897954,
                    #             "trigger_direction": "higher",
                    #             "trigger_price": "61500",
                    #             "trigger_price_type": "mark_price",
                    #             "type": "limit",
                    #             "updated_at": 1714366177334
                    #         },
                    #         "message": "OK"
                    #     }
                else:
                    response = await self.v2PrivatePostSpotCancelStopOrder(self.extend(request, params))
                    #     {
                    #         "code": 0,
                    #         "data": {
                    #             "amount": "0.0001",
                    #             "ccy": "BTC",
                    #             "client_id": "x-*********-f21ecfd7542abf1f",
                    #             "created_at": 1714366177334,
                    #             "market": "BTCUSDT",
                    #             "market_type": "SPOT",
                    #             "price": "61000",
                    #             "side": "buy",
                    #             "stop_id": 117401897954,
                    #             "trigger_direction": "higher",
                    #             "trigger_price": "61500",
                    #             "trigger_price_type": "mark_price",
                    #             "type": "limit",
                    #             "updated_at": 1714366177334
                    #         },
                    #         "message": "OK"
                    #     }
            else:
                request['order_id'] = self.parse_to_numeric(id)
                if swap:
                    response = await self.v2PrivatePostFuturesCancelOrder(self.extend(request, params))
                    #     {
                    #         "code": 0,
                    #         "data": {
                    #             "amount": "0.0001",
                    #             "client_id": "x-*********-7f14381c74a98a85",
                    #             "created_at": 1714367342024,
                    #             "fee": "0",
                    #             "fee_ccy": "USDT",
                    #             "filled_amount": "0",
                    #             "filled_value": "0",
                    #             "last_filled_amount": "0",
                    #             "last_filled_price": "0",
                    #             "maker_fee_rate": "0.0003",
                    #             "market": "BTCUSDT",
                    #             "market_type": "FUTURES",
                    #             "order_id": 137174472136,
                    #             "price": "61000",
                    #             "realized_pnl": "0",
                    #             "side": "buy",
                    #             "taker_fee_rate": "0.0005",
                    #             "type": "limit",
                    #             "unfilled_amount": "0.0001",
                    #             "updated_at": 1714367515978
                    #         },
                    #         "message": "OK"
                    #     }
                else:
                    response = await self.v2PrivatePostSpotCancelOrder(self.extend(request, params))
                    #     {
                    #         "code": 0,
                    #         "data": {
                    #             "amount": "0.0001",
                    #             "base_fee": "0",
                    #             "ccy": "BTC",
                    #             "client_id": "x-*********-86fbe37b54a2aea3",
                    #             "created_at": 1714365277437,
                    #             "discount_fee": "0",
                    #             "filled_amount": "0",
                    #             "filled_value": "0",
                    #             "last_fill_amount": "0",
                    #             "last_fill_price": "0",
                    #             "maker_fee_rate": "0.002",
                    #             "market": "BTCUSDT",
                    #             "market_type": "SPOT",
                    #             "order_id": 117401168172,
                    #             "price": "61000",
                    #             "quote_fee": "0",
                    #             "side": "buy",
                    #             "taker_fee_rate": "0.002",
                    #             "type": "limit",
                    #             "unfilled_amount": "0.0001",
                    #             "updated_at": 1714365277437
                    #         },
                    #         "message": "OK"
                    #     }
        data = None
        if clientOrderId is not None:
            rows = self.safe_list(response, 'data', [])
            data = self.safe_dict(rows[0], 'data', {})
        else:
            data = self.safe_dict(response, 'data', {})
        return self.parse_order(data, market)

    async def cancel_all_orders(self, symbol: Str = None, params={}):
        """
        cancel all open orders in a market

        https://docs.coinex.com/api/v2/spot/order/http/cancel-all-order
        https://docs.coinex.com/api/v2/futures/order/http/cancel-all-order

        :param str symbol: unified market symbol of the market to cancel orders in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.marginMode]: 'cross' or 'isolated' for canceling spot margin orders
        :returns dict[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' cancelAllOrders() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
        }
        response = None
        if market['swap']:
            request['market_type'] = 'FUTURES'
            response = await self.v2PrivatePostFuturesCancelAllOrder(self.extend(request, params))
            #
            # {"code":0,"data":{},"message":"OK"}
            #
        else:
            marginMode = None
            marginMode, params = self.handle_margin_mode_and_params('cancelAllOrders', params)
            if marginMode is not None:
                request['market_type'] = 'MARGIN'
            else:
                request['market_type'] = 'SPOT'
            response = await self.v2PrivatePostSpotCancelAllOrder(self.extend(request, params))
            #
            # {"code":0,"data":{},"message":"OK"}
            #
        return [
            self.safe_order({
                'info': response,
            }),
        ]

    async def fetch_order(self, id: str, symbol: Str = None, params={}):
        """
        fetches information on an order made by the user

        https://docs.coinex.com/api/v2/spot/order/http/get-order-status
        https://docs.coinex.com/api/v2/futures/order/http/get-order-status

        :param str id: order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchOrder() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
            'order_id': self.parse_to_numeric(id),
        }
        response = None
        if market['swap']:
            response = await self.v2PrivateGetFuturesOrderStatus(self.extend(request, params))
            #
            #     {
            #         "code": 0,
            #         "data": {
            #             "amount": "0.0001",
            #             "client_id": "x-*********-da5f31dcd478a829",
            #             "created_at": 1714460987164,
            #             "fee": "0",
            #             "fee_ccy": "USDT",
            #             "filled_amount": "0",
            #             "filled_value": "0",
            #             "last_filled_amount": "0",
            #             "last_filled_price": "0",
            #             "maker_fee_rate": "0.0003",
            #             "market": "BTCUSDT",
            #             "market_type": "FUTURES",
            #             "order_id": 137319868771,
            #             "price": "61000",
            #             "realized_pnl": "0",
            #             "side": "buy",
            #             "status": "open",
            #             "taker_fee_rate": "0.0005",
            #             "type": "limit",
            #             "unfilled_amount": "0.0001",
            #             "updated_at": 1714460987164
            #         },
            #         "message": "OK"
            #     }
            #
        else:
            response = await self.v2PrivateGetSpotOrderStatus(self.extend(request, params))
            #
            #     {
            #         "code": 0,
            #         "data": {
            #             "amount": "0.0001",
            #             "base_fee": "0",
            #             "ccy": "BTC",
            #             "client_id": "x-*********-da918d6724e3af81",
            #             "created_at": 1714461638958,
            #             "discount_fee": "0",
            #             "filled_amount": "0",
            #             "filled_value": "0",
            #             "last_fill_amount": "0",
            #             "last_fill_price": "0",
            #             "maker_fee_rate": "0.002",
            #             "market": "BTCUSDT",
            #             "market_type": "SPOT",
            #             "order_id": 117492012985,
            #             "price": "61000",
            #             "quote_fee": "0",
            #             "side": "buy",
            #             "status": "open",
            #             "taker_fee_rate": "0.002",
            #             "type": "limit",
            #             "unfilled_amount": "0.0001",
            #             "updated_at": 1714461638958
            #         },
            #         "message": "OK"
            #     }
            #
        data = self.safe_dict(response, 'data', {})
        return self.parse_order(data, market)

    async def fetch_orders_by_status(self, status, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch a list of orders

        https://docs.coinex.com/api/v2/spot/order/http/list-finished-order
        https://docs.coinex.com/api/v2/spot/order/http/list-finished-stop-order
        https://docs.coinex.com/api/v2/futures/order/http/list-finished-order
        https://docs.coinex.com/api/v2/futures/order/http/list-finished-stop-order

        :param str status: order status to fetch for
        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param boolean [params.trigger]: set to True for fetching trigger orders
        :param str [params.marginMode]: 'cross' or 'isolated' for fetching spot margin orders
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {}
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['market'] = market['id']
        if limit is not None:
            request['limit'] = limit
        trigger = self.safe_bool_2(params, 'stop', 'trigger')
        params = self.omit(params, ['stop', 'trigger'])
        marketType = None
        marketType, params = self.handle_market_type_and_params('fetchOrdersByStatus', market, params)
        response = None
        isClosed = (status == 'finished') or (status == 'closed')
        isOpen = (status == 'pending') or (status == 'open')
        if marketType == 'swap':
            request['market_type'] = 'FUTURES'
            if isClosed:
                if trigger:
                    response = await self.v2PrivateGetFuturesFinishedStopOrder(self.extend(request, params))
                    #
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "stop_id": 52431158859,
                    #                 "market": "BTCUSDT",
                    #                 "market_type": "FUTURES",
                    #                 "side": "sell",
                    #                 "type": "market",
                    #                 "amount": "0.0005",
                    #                 "price": "20599.64",
                    #                 "client_id": "",
                    #                 "created_at": 1667547909856,
                    #                 "updated_at": 1667547909856,
                    #                 "trigger_price": "20599.64",
                    #                 "trigger_price_type": "latest_price",
                    #                 "trigger_direction": ""
                    #             },
                    #         ],
                    #         "message": "OK",
                    #         "pagination": {
                    #             "has_next": False
                    #         }
                    #     }
                    #
                else:
                    response = await self.v2PrivateGetFuturesFinishedOrder(self.extend(request, params))
                    #
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "order_id": 136915813578,
                    #                 "market": "BTCUSDT",
                    #                 "market_type": "FUTURES",
                    #                 "side": "sell",
                    #                 "type": "market",
                    #                 "amount": "0.0001",
                    #                 "price": "0",
                    #                 "client_id": "x-*********-4f264600c432ac06",
                    #                 "created_at": 1714119323764,
                    #                 "updated_at": 1714119323764,
                    #                 "unfilled_amount": "0",
                    #                 "filled_amount": "0.0001",
                    #                 "filled_value": "6.442017",
                    #                 "fee": "0.003221",
                    #                 "fee_ccy": "USDT",
                    #                 "maker_fee_rate": "0",
                    #                 "taker_fee_rate": "0.0005"
                    #             },
                    #         ],
                    #         "message": "OK",
                    #         "pagination": {
                    #             "has_next": False
                    #         }
                    #     }
                    #
            elif isOpen:
                if trigger:
                    response = await self.v2PrivateGetFuturesPendingStopOrder(self.extend(request, params))
                    #
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "stop_id": 137481469849,
                    #                 "market": "BTCUSDT",
                    #                 "market_type": "FUTURES",
                    #                 "side": "buy",
                    #                 "type": "limit",
                    #                 "amount": "0.0001",
                    #                 "price": "51000",
                    #                 "client_id": "x-*********-2b932341949fa2a1",
                    #                 "created_at": 1714552257876,
                    #                 "updated_at": 1714552257876,
                    #                 "trigger_price": "52000",
                    #                 "trigger_price_type": "latest_price",
                    #                 "trigger_direction": "higher"
                    #             }
                    #         ],
                    #         "message": "OK",
                    #         "pagination": {
                    #             "total": 1,
                    #             "has_next": False
                    #         }
                    #     }
                    #
                else:
                    response = await self.v2PrivateGetFuturesPendingOrder(self.extend(request, params))
                    #
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "order_id": 137480580906,
                    #                 "market": "BTCUSDT",
                    #                 "market_type": "FUTURES",
                    #                 "side": "buy",
                    #                 "type": "limit",
                    #                 "amount": "0.0001",
                    #                 "price": "51000",
                    #                 "client_id": "",
                    #                 "created_at": 1714551877569,
                    #                 "updated_at": 1714551877569,
                    #                 "unfilled_amount": "0.0001",
                    #                 "filled_amount": "0",
                    #                 "filled_value": "0",
                    #                 "fee": "0",
                    #                 "fee_ccy": "USDT",
                    #                 "maker_fee_rate": "0.0003",
                    #                 "taker_fee_rate": "0.0005",
                    #                 "last_filled_amount": "0",
                    #                 "last_filled_price": "0",
                    #                 "realized_pnl": "0"
                    #             }
                    #         ],
                    #         "message": "OK",
                    #         "pagination": {
                    #             "total": 1,
                    #             "has_next": False
                    #         }
                    #     }
                    #
        else:
            marginMode = None
            marginMode, params = self.handle_margin_mode_and_params('fetchOrdersByStatus', params)
            if marginMode is not None:
                request['market_type'] = 'MARGIN'
            else:
                request['market_type'] = 'SPOT'
            if isClosed:
                if trigger:
                    response = await self.v2PrivateGetSpotFinishedStopOrder(self.extend(request, params))
                    #
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "stop_id": 117654881420,
                    #                 "market": "BTCUSDT",
                    #                 "market_type": "SPOT",
                    #                 "ccy": "USDT",
                    #                 "side": "buy",
                    #                 "type": "market",
                    #                 "amount": "5.83325524",
                    #                 "price": "0",
                    #                 "trigger_price": "57418",
                    #                 "trigger_direction": "lower",
                    #                 "trigger_price_type": "mark_price",
                    #                 "client_id": "",
                    #                 "created_at": 1714618050597,
                    #                 "updated_at": 0
                    #             }
                    #         ],
                    #         "message": "OK",
                    #         "pagination": {
                    #             "has_next": False
                    #         }
                    #     }
                    #
                else:
                    response = await self.v2PrivateGetSpotFinishedOrder(self.extend(request, params))
                    #
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "order_id": 117180532345,
                    #                 "market": "BTCUSDT",
                    #                 "market_type": "SPOT",
                    #                 "side": "sell",
                    #                 "type": "market",
                    #                 "ccy": "BTC",
                    #                 "amount": "0.00015484",
                    #                 "price": "0",
                    #                 "client_id": "",
                    #                 "created_at": 1714116494219,
                    #                 "updated_at": 0,
                    #                 "base_fee": "0",
                    #                 "quote_fee": "0.0199931699632",
                    #                 "discount_fee": "0",
                    #                 "maker_fee_rate": "0",
                    #                 "taker_fee_rate": "0.002",
                    #                 "unfilled_amount": "0",
                    #                 "filled_amount": "0.00015484",
                    #                 "filled_value": "9.9965849816"
                    #             },
                    #         ],
                    #         "message": "OK",
                    #         "pagination": {
                    #             "has_next": False
                    #         }
                    #     }
                    #
            elif status == 'pending':
                if trigger:
                    response = await self.v2PrivateGetSpotPendingStopOrder(self.extend(request, params))
                    #
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "stop_id": 117586439530,
                    #                 "market": "BTCUSDT",
                    #                 "market_type": "SPOT",
                    #                 "ccy": "BTC",
                    #                 "side": "buy",
                    #                 "type": "limit",
                    #                 "amount": "0.0001",
                    #                 "price": "51000",
                    #                 "trigger_price": "52000",
                    #                 "trigger_direction": "higher",
                    #                 "trigger_price_type": "mark_price",
                    #                 "client_id": "x-*********-df61777094c69312",
                    #                 "created_at": 1714551237335,
                    #                 "updated_at": 1714551237335
                    #             }
                    #         ],
                    #         "message": "OK",
                    #         "pagination": {
                    #             "total": 1,
                    #             "has_next": False
                    #         }
                    #     }
                    #
                else:
                    response = await self.v2PrivateGetSpotPendingOrder(self.extend(request, params))
                    #
                    #     {
                    #         "code": 0,
                    #         "data": [
                    #             {
                    #                 "order_id": 117585921297,
                    #                 "market": "BTCUSDT",
                    #                 "market_type": "SPOT",
                    #                 "side": "buy",
                    #                 "type": "limit",
                    #                 "ccy": "BTC",
                    #                 "amount": "0.00011793",
                    #                 "price": "52000",
                    #                 "client_id": "",
                    #                 "created_at": 1714550707486,
                    #                 "updated_at": 1714550707486,
                    #                 "base_fee": "0",
                    #                 "quote_fee": "0",
                    #                 "discount_fee": "0",
                    #                 "maker_fee_rate": "0.002",
                    #                 "taker_fee_rate": "0.002",
                    #                 "last_fill_amount": "0",
                    #                 "last_fill_price": "0",
                    #                 "unfilled_amount": "0.00011793",
                    #                 "filled_amount": "0",
                    #                 "filled_value": "0"
                    #             }
                    #         ],
                    #         "message": "OK",
                    #         "pagination": {
                    #             "total": 1,
                    #             "has_next": False
                    #         }
                    #     }
                    #
        data = self.safe_list(response, 'data', [])
        return self.parse_orders(data, market, since, limit)

    async def fetch_open_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetch all unfilled currently open orders

        https://docs.coinex.com/api/v2/spot/order/http/list-pending-order
        https://docs.coinex.com/api/v2/spot/order/http/list-pending-stop-order
        https://docs.coinex.com/api/v2/futures/order/http/list-pending-order
        https://docs.coinex.com/api/v2/futures/order/http/list-pending-stop-order

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch open orders for
        :param int [limit]: the maximum number of open order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param boolean [params.trigger]: set to True for fetching trigger orders
        :param str [params.marginMode]: 'cross' or 'isolated' for fetching spot margin orders
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        openOrders = await self.fetch_orders_by_status('pending', symbol, since, limit, params)
        for i in range(0, len(openOrders)):
            openOrders[i]['status'] = 'open'
        return openOrders

    async def fetch_closed_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetches information on multiple closed orders made by the user

        https://docs.coinex.com/api/v2/spot/order/http/list-finished-order
        https://docs.coinex.com/api/v2/spot/order/http/list-finished-stop-order
        https://docs.coinex.com/api/v2/futures/order/http/list-finished-order
        https://docs.coinex.com/api/v2/futures/order/http/list-finished-stop-order

        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param boolean [params.trigger]: set to True for fetching trigger orders
        :param str [params.marginMode]: 'cross' or 'isolated' for fetching spot margin orders
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        return await self.fetch_orders_by_status('finished', symbol, since, limit, params)

    async def create_deposit_address(self, code: str, params={}) -> DepositAddress:
        """
        create a currency deposit address

        https://docs.coinex.com/api/v2/assets/deposit-withdrawal/http/update-deposit-address

        :param str code: unified currency code of the currency for the deposit address
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.network]: the blockchain network to create a deposit address on
        :returns dict: an `address structure <https://docs.ccxt.com/#/?id=address-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        network = self.safe_string_2(params, 'chain', 'network')
        if network is None:
            raise ArgumentsRequired(self.id + ' createDepositAddress() requires a network parameter')
        params = self.omit(params, 'network')
        request: dict = {
            'ccy': currency['id'],
            'chain': self.network_code_to_id(network, currency['code']),
        }
        response = await self.v2PrivatePostAssetsRenewalDepositAddress(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "address": "0x321bd6479355142334f45653ad5d8b76105a1234",
        #             "memo": ""
        #         },
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data', {})
        return self.parse_deposit_address(data, currency)

    async def fetch_deposit_address(self, code: str, params={}) -> DepositAddress:
        """
        fetch the deposit address for a currency associated with self account

        https://docs.coinex.com/api/v2/assets/deposit-withdrawal/http/get-deposit-address

        :param str code: unified currency code
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.network]: the blockchain network to create a deposit address on
        :returns dict: an `address structure <https://docs.ccxt.com/#/?id=address-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'ccy': currency['id'],
        }
        networkCode = None
        networkCode, params = self.handle_network_code_and_params(params)
        if networkCode is None:
            raise ArgumentsRequired(self.id + ' fetchDepositAddress() requires a "network" parameter')
        request['chain'] = self.network_code_to_id(networkCode)  # required for on-chain, not required for inter-user transfer
        response = await self.v2PrivateGetAssetsDepositAddress(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "address": "0x321bd6479355142334f45653ad5d8b76105a1234",
        #             "memo": ""
        #         },
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data', {})
        return self.parse_deposit_address(data, currency)

    def parse_deposit_address(self, depositAddress, currency: Currency = None) -> DepositAddress:
        #
        #     {
        #         "address": "1P1JqozxioQwaqPwgMAQdNDYNyaVSqgARq",
        #         "memo": ""
        #     }
        #
        coinAddress = self.safe_string(depositAddress, 'address')
        parts = coinAddress.split(':')
        address = None
        tag = None
        partsLength = len(parts)
        if partsLength > 1 and parts[0] != 'cfx':
            address = parts[0]
            tag = parts[1]
        else:
            address = coinAddress
        return {
            'info': depositAddress,
            'currency': self.safe_currency_code(None, currency),
            'network': None,
            'address': address,
            'tag': self.safe_string(depositAddress, 'memo', tag),
        }

    async def fetch_my_trades(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all trades made by the user

        https://docs.coinex.com/api/v2/spot/deal/http/list-user-deals
        https://docs.coinex.com/api/v2/futures/deal/http/list-user-deals

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trade structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int [params.until]: timestamp in ms of the latest trades
        :param str [params.side]: the side of the trades, either 'buy' or 'sell', required for swap
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchMyTrades() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
        }
        if limit is not None:
            request['limit'] = limit
        if since is not None:
            request['start_time'] = since
        request, params = self.handle_until_option('end_time', request, params)
        response = None
        if market['swap']:
            request['market_type'] = 'FUTURES'
            response = await self.v2PrivateGetFuturesUserDeals(self.extend(request, params))
            #
            #     {
            #         "code": 0,
            #         "data": [
            #             {
            #                 "deal_id": 1180222387,
            #                 "created_at": 1714119054558,
            #                 "market": "BTCUSDT",
            #                 "side": "buy",
            #                 "order_id": 136915589622,
            #                 "price": "64376",
            #                 "amount": "0.0001"
            #             }
            #         ],
            #         "message": "OK",
            #         "pagination": {
            #             "has_next": True
            #         }
            #     }
            #
        else:
            marginMode = None
            marginMode, params = self.handle_margin_mode_and_params('fetchMyTrades', params)
            if marginMode is not None:
                request['market_type'] = 'MARGIN'
            else:
                request['market_type'] = 'SPOT'
            response = await self.v2PrivateGetSpotUserDeals(self.extend(request, params))
            #
            #     {
            #         "code": 0,
            #         "data": [
            #             {
            #                 "amount": "0.00010087",
            #                 "created_at": 1714618087585,
            #                 "deal_id": 4161200602,
            #                 "margin_market": "",
            #                 "market": "BTCUSDT",
            #                 "order_id": 117654919342,
            #                 "price": "57464.04",
            #                 "side": "sell"
            #             }
            #         ],
            #         "message": "OK",
            #         "pagination": {
            #             "has_next": True
            #         }
            #     }
            #
        data = self.safe_list(response, 'data', [])
        return self.parse_trades(data, market, since, limit)

    async def fetch_positions(self, symbols: Strings = None, params={}):
        """
        fetch all open positions

        https://docs.coinex.com/api/v2/futures/position/http/list-pending-position
        https://docs.coinex.com/api/v2/futures/position/http/list-finished-position

        :param str[] [symbols]: list of unified market symbols
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.method]: the method to use 'v2PrivateGetFuturesPendingPosition' or 'v2PrivateGetFuturesFinishedPosition' default is 'v2PrivateGetFuturesPendingPosition'
        :returns dict[]: a list of `position structure <https://docs.ccxt.com/#/?id=position-structure>`
        """
        await self.load_markets()
        defaultMethod = None
        defaultMethod, params = self.handle_option_and_params(params, 'fetchPositions', 'method', 'v2PrivateGetFuturesPendingPosition')
        symbols = self.market_symbols(symbols)
        request: dict = {
            'market_type': 'FUTURES',
        }
        market = None
        if symbols is not None:
            symbol = None
            if isinstance(symbols, list):
                symbolsLength = len(symbols)
                if symbolsLength > 1:
                    raise BadRequest(self.id + ' fetchPositions() symbols argument cannot contain more than 1 symbol')
                symbol = symbols[0]
            else:
                symbol = symbols
            market = self.market(symbol)
            request['market'] = market['id']
        response = None
        if defaultMethod == 'v2PrivateGetFuturesPendingPosition':
            response = await self.v2PrivateGetFuturesPendingPosition(self.extend(request, params))
        else:
            response = await self.v2PrivateGetFuturesFinishedPosition(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "position_id": 305891033,
        #                 "market": "BTCUSDT",
        #                 "market_type": "FUTURES",
        #                 "side": "long",
        #                 "margin_mode": "cross",
        #                 "open_interest": "0.0001",
        #                 "close_avbl": "0.0001",
        #                 "ath_position_amount": "0.0001",
        #                 "unrealized_pnl": "0",
        #                 "realized_pnl": "-0.00311684",
        #                 "avg_entry_price": "62336.8",
        #                 "cml_position_value": "6.23368",
        #                 "max_position_value": "6.23368",
        #                 "created_at": 1715152208041,
        #                 "updated_at": 1715152208041,
        #                 "take_profit_price": "0",
        #                 "stop_loss_price": "0",
        #                 "take_profit_type": "",
        #                 "stop_loss_type": "",
        #                 "settle_price": "62336.8",
        #                 "settle_value": "6.23368",
        #                 "leverage": "3",
        #                 "margin_avbl": "2.07789333",
        #                 "ath_margin_size": "2.07789333",
        #                 "position_margin_rate": "2.40545879023305655728",
        #                 "maintenance_margin_rate": "0.005",
        #                 "maintenance_margin_value": "0.03118094",
        #                 "liq_price": "0",
        #                 "bkr_price": "0",
        #                 "adl_level": 1
        #             }
        #         ],
        #         "message": "OK",
        #         "pagination": {
        #             "has_next": False
        #         }
        #     }
        #
        position = self.safe_list(response, 'data', [])
        result = []
        for i in range(0, len(position)):
            result.append(self.parse_position(position[i], market))
        return self.filter_by_array_positions(result, 'symbol', symbols, False)

    async def fetch_position(self, symbol: str, params={}):
        """
        fetch data on a single open contract trade position

        https://docs.coinex.com/api/v2/futures/position/http/list-pending-position

        :param str symbol: unified market symbol of the market the position is held in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `position structure <https://docs.ccxt.com/#/?id=position-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market_type': 'FUTURES',
            'market': market['id'],
        }
        response = await self.v2PrivateGetFuturesPendingPosition(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "position_id": 305891033,
        #                 "market": "BTCUSDT",
        #                 "market_type": "FUTURES",
        #                 "side": "long",
        #                 "margin_mode": "cross",
        #                 "open_interest": "0.0001",
        #                 "close_avbl": "0.0001",
        #                 "ath_position_amount": "0.0001",
        #                 "unrealized_pnl": "0",
        #                 "realized_pnl": "-0.00311684",
        #                 "avg_entry_price": "62336.8",
        #                 "cml_position_value": "6.23368",
        #                 "max_position_value": "6.23368",
        #                 "created_at": 1715152208041,
        #                 "updated_at": 1715152208041,
        #                 "take_profit_price": "0",
        #                 "stop_loss_price": "0",
        #                 "take_profit_type": "",
        #                 "stop_loss_type": "",
        #                 "settle_price": "62336.8",
        #                 "settle_value": "6.23368",
        #                 "leverage": "3",
        #                 "margin_avbl": "2.07789333",
        #                 "ath_margin_size": "2.07789333",
        #                 "position_margin_rate": "2.40545879023305655728",
        #                 "maintenance_margin_rate": "0.005",
        #                 "maintenance_margin_value": "0.03118094",
        #                 "liq_price": "0",
        #                 "bkr_price": "0",
        #                 "adl_level": 1
        #             }
        #         ],
        #         "message": "OK",
        #         "pagination": {
        #             "has_next": False
        #         }
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_position(data[0], market)

    def parse_position(self, position: dict, market: Market = None):
        #
        #     {
        #         "position_id": 305891033,
        #         "market": "BTCUSDT",
        #         "market_type": "FUTURES",
        #         "side": "long",
        #         "margin_mode": "cross",
        #         "open_interest": "0.0001",
        #         "close_avbl": "0.0001",
        #         "ath_position_amount": "0.0001",
        #         "unrealized_pnl": "0",
        #         "realized_pnl": "-0.00311684",
        #         "avg_entry_price": "62336.8",
        #         "cml_position_value": "6.23368",
        #         "max_position_value": "6.23368",
        #         "created_at": 1715152208041,
        #         "updated_at": 1715152208041,
        #         "take_profit_price": "0",
        #         "stop_loss_price": "0",
        #         "take_profit_type": "",
        #         "stop_loss_type": "",
        #         "settle_price": "62336.8",
        #         "settle_value": "6.23368",
        #         "leverage": "3",
        #         "margin_avbl": "2.07789333",
        #         "ath_margin_size": "2.07789333",
        #         "position_margin_rate": "2.40545879023305655728",
        #         "maintenance_margin_rate": "0.005",
        #         "maintenance_margin_value": "0.03118094",
        #         "liq_price": "0",
        #         "bkr_price": "0",
        #         "adl_level": 1
        #     }
        #
        marketId = self.safe_string(position, 'market')
        market = self.safe_market(marketId, market, None, 'swap')
        timestamp = self.safe_integer(position, 'created_at')
        return self.safe_position({
            'info': position,
            'id': self.safe_integer(position, 'position_id'),
            'symbol': market['symbol'],
            'notional': self.safe_number(position, 'settle_value'),
            'marginMode': self.safe_string(position, 'margin_mode'),
            'liquidationPrice': self.safe_number(position, 'liq_price'),
            'entryPrice': self.safe_number(position, 'avg_entry_price'),
            'unrealizedPnl': self.safe_number(position, 'unrealized_pnl'),
            'realizedPnl': self.safe_number(position, 'realized_pnl'),
            'percentage': None,
            'contracts': self.safe_number(position, 'close_avbl'),
            'contractSize': self.safe_number(market, 'contractSize'),
            'markPrice': None,
            'lastPrice': None,
            'side': self.safe_string(position, 'side'),
            'hedged': None,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'lastUpdateTimestamp': self.safe_integer(position, 'updated_at'),
            'maintenanceMargin': self.safe_number(position, 'maintenance_margin_value'),
            'maintenanceMarginPercentage': self.safe_number(position, 'maintenance_margin_rate'),
            'collateral': self.safe_number(position, 'margin_avbl'),
            'initialMargin': None,
            'initialMarginPercentage': None,
            'leverage': self.safe_number(position, 'leverage'),
            'marginRatio': self.safe_number(position, 'position_margin_rate'),
            'stopLossPrice': self.omit_zero(self.safe_string(position, 'stop_loss_price')),
            'takeProfitPrice': self.omit_zero(self.safe_string(position, 'take_profit_price')),
        })

    async def set_margin_mode(self, marginMode: str, symbol: Str = None, params={}):
        """
        set margin mode to 'cross' or 'isolated'

        https://docs.coinex.com/api/v2/futures/position/http/adjust-position-leverage

        :param str marginMode: 'cross' or 'isolated'
        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int params['leverage']: the rate of leverage
        :returns dict: response from the exchange
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' setMarginMode() requires a symbol argument')
        marginMode = marginMode.lower()
        if marginMode != 'isolated' and marginMode != 'cross':
            raise BadRequest(self.id + ' setMarginMode() marginMode argument should be isolated or cross')
        await self.load_markets()
        market = self.market(symbol)
        if market['type'] != 'swap':
            raise BadSymbol(self.id + ' setMarginMode() supports swap contracts only')
        leverage = self.safe_integer(params, 'leverage')
        maxLeverage = self.safe_integer(market['limits']['leverage'], 'max', 100)
        if leverage is None:
            raise ArgumentsRequired(self.id + ' setMarginMode() requires a leverage parameter')
        if (leverage < 1) or (leverage > maxLeverage):
            raise BadRequest(self.id + ' setMarginMode() leverage should be between 1 and ' + str(maxLeverage) + ' for ' + symbol)
        request: dict = {
            'market': market['id'],
            'market_type': 'FUTURES',
            'margin_mode': marginMode,
            'leverage': leverage,
        }
        return await self.v2PrivatePostFuturesAdjustPositionLeverage(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "leverage": 1,
        #             "margin_mode": "isolated"
        #         },
        #         "message": "OK"
        #     }
        #

    async def set_leverage(self, leverage: Int, symbol: Str = None, params={}):
        """

        https://docs.coinex.com/api/v2/futures/position/http/adjust-position-leverage

        set the level of leverage for a market
        :param float leverage: the rate of leverage
        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.marginMode]: 'cross' or 'isolated'(default is 'cross')
        :returns dict: response from the exchange
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' setLeverage() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        if not market['swap']:
            raise BadSymbol(self.id + ' setLeverage() supports swap contracts only')
        marginMode = None
        marginMode, params = self.handle_margin_mode_and_params('setLeverage', params, 'cross')
        minLeverage = self.safe_integer(market['limits']['leverage'], 'min', 1)
        maxLeverage = self.safe_integer(market['limits']['leverage'], 'max', 100)
        if (leverage < minLeverage) or (leverage > maxLeverage):
            raise BadRequest(self.id + ' setLeverage() leverage should be between ' + str(minLeverage) + ' and ' + str(maxLeverage) + ' for ' + symbol)
        request: dict = {
            'market': market['id'],
            'market_type': 'FUTURES',
            'margin_mode': marginMode,
            'leverage': leverage,
        }
        return await self.v2PrivatePostFuturesAdjustPositionLeverage(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "leverage": 1,
        #             "margin_mode": "isolated"
        #         },
        #         "message": "OK"
        #     }
        #

    async def fetch_leverage_tiers(self, symbols: Strings = None, params={}) -> LeverageTiers:
        """
        retrieve information on the maximum leverage, and maintenance margin for trades of varying trade sizes

        https://docs.coinex.com/api/v2/futures/market/http/list-market-position-level

        :param str[]|None symbols: list of unified market symbols
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `leverage tiers structures <https://docs.ccxt.com/#/?id=leverage-tiers-structure>`, indexed by market symbols
        """
        await self.load_markets()
        request: dict = {}
        if symbols is not None:
            marketIds = self.market_ids(symbols)
            request['market'] = ','.join(marketIds)
        response = await self.v2PublicGetFuturesPositionLevel(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "level": [
        #                     {
        #                         "amount": "20001",
        #                         "leverage": "20",
        #                         "maintenance_margin_rate": "0.02",
        #                         "min_initial_margin_rate": "0.05"
        #                     },
        #                     {
        #                         "amount": "50001",
        #                         "leverage": "10",
        #                         "maintenance_margin_rate": "0.04",
        #                         "min_initial_margin_rate": "0.1"
        #                     },
        #                 ],
        #                 "market": "MINAUSDT"
        #             },
        #         ],
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_leverage_tiers(data, symbols, 'market')

    def parse_market_leverage_tiers(self, info, market: Market = None) -> List[LeverageTier]:
        tiers = []
        brackets = self.safe_list(info, 'level', [])
        minNotional = 0
        for i in range(0, len(brackets)):
            tier = brackets[i]
            marketId = self.safe_string(info, 'market')
            market = self.safe_market(marketId, market, None, 'swap')
            maxNotional = self.safe_number(tier, 'amount')
            tiers.append({
                'tier': self.sum(i, 1),
                'symbol': self.safe_symbol(marketId, market, None, 'swap'),
                'currency': market['base'] if market['linear'] else market['quote'],
                'minNotional': minNotional,
                'maxNotional': maxNotional,
                'maintenanceMarginRate': self.safe_number(tier, 'maintenance_margin_rate'),
                'maxLeverage': self.safe_integer(tier, 'leverage'),
                'info': tier,
            })
            minNotional = maxNotional
        return tiers

    async def modify_margin_helper(self, symbol: str, amount, addOrReduce, params={}):
        await self.load_markets()
        market = self.market(symbol)
        rawAmount = self.amount_to_precision(symbol, amount)
        requestAmount = rawAmount
        if addOrReduce == 'reduce':
            requestAmount = Precise.string_neg(rawAmount)
        request: dict = {
            'market': market['id'],
            'market_type': 'FUTURES',
            'amount': requestAmount,
        }
        response = await self.v2PrivatePostFuturesAdjustPositionMargin(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "adl_level": 1,
        #             "ath_margin_size": "2.034928",
        #             "ath_position_amount": "0.0001",
        #             "avg_entry_price": "61047.84",
        #             "bkr_price": "30698.5600000000000004142",
        #             "close_avbl": "0.0001",
        #             "cml_position_value": "6.104784",
        #             "created_at": 1715488472908,
        #             "leverage": "3",
        #             "liq_price": "30852.82412060301507579316",
        #             "maintenance_margin_rate": "0.005",
        #             "maintenance_margin_value": "0.03051465",
        #             "margin_avbl": "3.034928",
        #             "margin_mode": "isolated",
        #             "market": "BTCUSDT",
        #             "market_type": "FUTURES",
        #             "max_position_value": "6.104784",
        #             "open_interest": "0.0001",
        #             "position_id": 306458800,
        #             "position_margin_rate": "0.49713929272518077625",
        #             "realized_pnl": "-0.003052392",
        #             "settle_price": "61047.84",
        #             "settle_value": "6.104784",
        #             "side": "long",
        #             "stop_loss_price": "0",
        #             "stop_loss_type": "",
        #             "take_profit_price": "0",
        #             "take_profit_type": "",
        #             "unrealized_pnl": "0",
        #             "updated_at": 1715488805563
        #         },
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data')
        status = self.safe_string_lower(response, 'message')
        type = 'reduce' if (addOrReduce == 'reduce') else 'add'
        return self.extend(self.parse_margin_modification(data, market), {
            'type': type,
            'amount': self.parse_number(amount),
            'status': status,
        })

    def parse_margin_modification(self, data: dict, market: Market = None) -> MarginModification:
        #
        # addMargin/reduceMargin
        #
        #     {
        #         "adl_level": 1,
        #         "ath_margin_size": "2.034928",
        #         "ath_position_amount": "0.0001",
        #         "avg_entry_price": "61047.84",
        #         "bkr_price": "30698.5600000000000004142",
        #         "close_avbl": "0.0001",
        #         "cml_position_value": "6.104784",
        #         "created_at": 1715488472908,
        #         "leverage": "3",
        #         "liq_price": "30852.82412060301507579316",
        #         "maintenance_margin_rate": "0.005",
        #         "maintenance_margin_value": "0.03051465",
        #         "margin_avbl": "3.034928",
        #         "margin_mode": "isolated",
        #         "market": "BTCUSDT",
        #         "market_type": "FUTURES",
        #         "max_position_value": "6.104784",
        #         "open_interest": "0.0001",
        #         "position_id": 306458800,
        #         "position_margin_rate": "0.49713929272518077625",
        #         "realized_pnl": "-0.003052392",
        #         "settle_price": "61047.84",
        #         "settle_value": "6.104784",
        #         "side": "long",
        #         "stop_loss_price": "0",
        #         "stop_loss_type": "",
        #         "take_profit_price": "0",
        #         "take_profit_type": "",
        #         "unrealized_pnl": "0",
        #         "updated_at": 1715488805563
        #     }
        #
        # fetchMarginAdjustmentHistory
        #
        #     {
        #         "bkr_pirce": "24698.56000000000000005224",
        #         "created_at": 1715489978697,
        #         "leverage": "3",
        #         "liq_price": "24822.67336683417085432386",
        #         "margin_avbl": "3.634928",
        #         "margin_change": "-1.5",
        #         "margin_mode": "isolated",
        #         "market": "BTCUSDT",
        #         "market_type": "FUTURES",
        #         "open_interest": "0.0001",
        #         "position_id": 306458800,
        #         "settle_price": "61047.84"
        #     }
        #
        marketId = self.safe_string(data, 'market')
        timestamp = self.safe_integer_2(data, 'updated_at', 'created_at')
        change = self.safe_string(data, 'margin_change')
        return {
            'info': data,
            'symbol': self.safe_symbol(marketId, market, None, 'swap'),
            'type': None,
            'marginMode': 'isolated',
            'amount': self.parse_number(Precise.string_abs(change)),
            'total': self.safe_number(data, 'margin_avbl'),
            'code': market['quote'],
            'status': None,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
        }

    async def add_margin(self, symbol: str, amount: float, params={}) -> MarginModification:
        """
        add margin

        https://docs.coinex.com/api/v2/futures/position/http/adjust-position-margin

        :param str symbol: unified market symbol
        :param float amount: amount of margin to add
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `margin structure <https://docs.ccxt.com/#/?id=add-margin-structure>`
        """
        return await self.modify_margin_helper(symbol, amount, 'add', params)

    async def reduce_margin(self, symbol: str, amount: float, params={}) -> MarginModification:
        """
        remove margin from a position

        https://docs.coinex.com/api/v2/futures/position/http/adjust-position-margin

        :param str symbol: unified market symbol
        :param float amount: the amount of margin to remove
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `margin structure <https://docs.ccxt.com/#/?id=reduce-margin-structure>`
        """
        return await self.modify_margin_helper(symbol, amount, 'reduce', params)

    async def fetch_funding_history(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch the history of funding fee payments paid and received on self account

        https://docs.coinex.com/api/v2/futures/position/http/list-position-funding-history

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch funding history for
        :param int [limit]: the maximum number of funding history structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `funding history structure <https://docs.ccxt.com/#/?id=funding-history-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchFundingHistory() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
            'market_type': 'FUTURES',
        }
        request, params = self.handle_until_option('end_time', request, params)
        if since is not None:
            request['start_time'] = since
        if limit is not None:
            request['limit'] = limit
        response = await self.v2PrivateGetFuturesPositionFundingHistory(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "ccy": "USDT",
        #                 "created_at": 1715673620183,
        #                 "funding_rate": "0",
        #                 "funding_value": "0",
        #                 "market": "BTCUSDT",
        #                 "market_type": "FUTURES",
        #                 "position_id": 306458800,
        #                 "side": "long"
        #             },
        #         ],
        #         "message": "OK",
        #         "pagination": {
        #             "has_next": True
        #         }
        #     }
        #
        data = self.safe_list(response, 'data', [])
        result = []
        for i in range(0, len(data)):
            entry = data[i]
            timestamp = self.safe_integer(entry, 'created_at')
            currencyId = self.safe_string(entry, 'ccy')
            code = self.safe_currency_code(currencyId)
            result.append({
                'info': entry,
                'symbol': symbol,
                'code': code,
                'timestamp': timestamp,
                'datetime': self.iso8601(timestamp),
                'id': self.safe_number(entry, 'position_id'),
                'amount': self.safe_number(entry, 'funding_value'),
            })
        return result

    async def fetch_funding_rate(self, symbol: str, params={}) -> FundingRate:
        """
        fetch the current funding rate

        https://docs.coinex.com/api/v2/futures/market/http/list-market-funding-rate

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `funding rate structure <https://docs.ccxt.com/#/?id=funding-rate-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        if not market['swap']:
            raise BadSymbol(self.id + ' fetchFundingRate() supports swap contracts only')
        request: dict = {
            'market': market['id'],
        }
        response = await self.v2PublicGetFuturesFundingRate(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "latest_funding_rate": "0",
        #                 "latest_funding_time": 1715731200000,
        #                 "mark_price": "61602.22",
        #                 "market": "BTCUSDT",
        #                 "max_funding_rate": "0.00375",
        #                 "min_funding_rate": "-0.00375",
        #                 "next_funding_rate": "0.00021074",
        #                 "next_funding_time": 1715760000000
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        first = self.safe_dict(data, 0, {})
        return self.parse_funding_rate(first, market)

    async def fetch_funding_interval(self, symbol: str, params={}) -> FundingRate:
        """
        fetch the current funding rate interval

        https://docs.coinex.com/api/v2/futures/market/http/list-market-funding-rate

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `funding rate structure <https://docs.ccxt.com/#/?id=funding-rate-structure>`
        """
        return await self.fetch_funding_rate(symbol, params)

    def parse_funding_rate(self, contract, market: Market = None) -> FundingRate:
        #
        # fetchFundingRate, fetchFundingRates, fetchFundingInterval
        #
        #     {
        #         "latest_funding_rate": "0",
        #         "latest_funding_time": 1715731200000,
        #         "mark_price": "61602.22",
        #         "market": "BTCUSDT",
        #         "max_funding_rate": "0.00375",
        #         "min_funding_rate": "-0.00375",
        #         "next_funding_rate": "0.00021074",
        #         "next_funding_time": 1715760000000
        #     }
        #
        currentFundingTimestamp = self.safe_integer(contract, 'latest_funding_time')
        futureFundingTimestamp = self.safe_integer(contract, 'next_funding_time')
        fundingTimeString = self.safe_string(contract, 'latest_funding_time')
        nextFundingTimeString = self.safe_string(contract, 'next_funding_time')
        millisecondsInterval = Precise.string_sub(nextFundingTimeString, fundingTimeString)
        marketId = self.safe_string(contract, 'market')
        return {
            'info': contract,
            'symbol': self.safe_symbol(marketId, market, None, 'swap'),
            'markPrice': self.safe_number(contract, 'mark_price'),
            'indexPrice': None,
            'interestRate': None,
            'estimatedSettlePrice': None,
            'timestamp': None,
            'datetime': None,
            'fundingRate': self.safe_number(contract, 'latest_funding_rate'),
            'fundingTimestamp': currentFundingTimestamp,
            'fundingDatetime': self.iso8601(currentFundingTimestamp),
            'nextFundingRate': self.safe_number(contract, 'next_funding_rate'),
            'nextFundingTimestamp': futureFundingTimestamp,
            'nextFundingDatetime': self.iso8601(futureFundingTimestamp),
            'previousFundingRate': None,
            'previousFundingTimestamp': None,
            'previousFundingDatetime': None,
            'interval': self.parse_funding_interval(millisecondsInterval),
        }

    def parse_funding_interval(self, interval):
        intervals: dict = {
            '3600000': '1h',
            '14400000': '4h',
            '28800000': '8h',
            '57600000': '16h',
            '86400000': '24h',
        }
        return self.safe_string(intervals, interval, interval)

    async def fetch_funding_rates(self, symbols: Strings = None, params={}) -> FundingRates:
        """
        fetch the current funding rates for multiple markets

        https://docs.coinex.com/api/v2/futures/market/http/list-market-funding-rate

        :param str[] symbols: unified market symbols
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: an array of `funding rate structures <https://docs.ccxt.com/#/?id=funding-rate-structure>`
        """
        await self.load_markets()
        symbols = self.market_symbols(symbols)
        request: dict = {}
        market = None
        if symbols is not None:
            symbol = self.safe_value(symbols, 0)
            market = self.market(symbol)
            if not market['swap']:
                raise BadSymbol(self.id + ' fetchFundingRates() supports swap contracts only')
            marketIds = self.market_ids(symbols)
            request['market'] = ','.join(marketIds)
        response = await self.v2PublicGetFuturesFundingRate(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "latest_funding_rate": "0",
        #                 "latest_funding_time": 1715731200000,
        #                 "mark_price": "61602.22",
        #                 "market": "BTCUSDT",
        #                 "max_funding_rate": "0.00375",
        #                 "min_funding_rate": "-0.00375",
        #                 "next_funding_rate": "0.00021074",
        #                 "next_funding_time": 1715760000000
        #             }
        #         ],
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_funding_rates(data, symbols)

    async def withdraw(self, code: str, amount: float, address: str, tag=None, params={}) -> Transaction:
        """
        make a withdrawal

        https://docs.coinex.com/api/v2/assets/deposit-withdrawal/http/withdrawal

        :param str code: unified currency code
        :param float amount: the amount to withdraw
        :param str address: the address to withdraw to
        :param str tag:
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.network]: unified network code
        :returns dict: a `transaction structure <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        tag, params = self.handle_withdraw_tag_and_params(tag, params)
        self.check_address(address)
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'ccy': currency['id'],
            'to_address': address,  # must be authorized, inter-user transfer by a registered mobile phone number or an email address is supported
            'amount': self.currency_to_precision(code, amount),  # the actual amount without fees, https://www.coinex.com/fees
        }
        if tag is not None:
            request['memo'] = tag
        networkCode = None
        networkCode, params = self.handle_network_code_and_params(params)
        if networkCode is not None:
            request['chain'] = self.network_code_to_id(networkCode)  # required for on-chain, not required for inter-user transfer
        response = await self.v2PrivatePostAssetsWithdraw(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "withdraw_id": 31193755,
        #             "created_at": 1716874165038,
        #             "withdraw_method": "ON_CHAIN",
        #             "ccy": "USDT",
        #             "amount": "17.3",
        #             "actual_amount": "15",
        #             "chain": "TRC20",
        #             "tx_fee": "2.3",
        #             "fee_asset": "USDT",
        #             "fee_amount": "2.3",
        #             "to_address": "TY5vq3MT6b5cQVAHWHtpGyPg1ERcQgi3UN",
        #             "memo": "",
        #             "tx_id": "",
        #             "confirmations": 0,
        #             "explorer_address_url": "https://tronscan.org/#/address/TY5vq3MT6b5cQVAHWHtpGyPg1ERcQgi3UN",
        #             "explorer_tx_url": "https://tronscan.org/#/transaction/",
        #             "remark": "",
        #             "status": "audit_required"
        #         },
        #         "message": "OK"
        #     }
        #
        transaction = self.safe_dict(response, 'data', {})
        return self.parse_transaction(transaction, currency)

    def parse_transaction_status(self, status: Str):
        statuses: dict = {
            'audit': 'pending',
            'pass': 'pending',
            'audit_required': 'pending',
            'processing': 'pending',
            'confirming': 'pending',
            'not_pass': 'failed',
            'cancel': 'canceled',
            'finish': 'ok',
            'finished': 'ok',
            'fail': 'failed',
        }
        return self.safe_string(statuses, status, status)

    async def fetch_funding_rate_history(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetches historical funding rate prices

        https://docs.coinex.com/api/v2/futures/market/http/list-market-funding-rate-history

        :param str symbol: unified symbol of the market to fetch the funding rate history for
        :param int [since]: timestamp in ms of the earliest funding rate to fetch
        :param int [limit]: the maximum amount of `funding rate structures <https://docs.ccxt.com/#/?id=funding-rate-history-structure>` to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param boolean [params.paginate]: default False, when True will automatically paginate by calling self endpoint multiple times. See in the docs all the [available parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
        :param int [params.until]: timestamp in ms of the latest funding rate
        :returns dict[]: a list of `funding rate structures <https://docs.ccxt.com/#/?id=funding-rate-history-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchFundingRateHistory() requires a symbol argument')
        await self.load_markets()
        paginate = False
        paginate, params = self.handle_option_and_params(params, 'fetchFundingRateHistory', 'paginate')
        if paginate:
            return await self.fetch_paginated_call_deterministic('fetchFundingRateHistory', symbol, since, limit, '8h', params, 1000)
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
        }
        if since is not None:
            request['start_time'] = since
        if limit is not None:
            request['limit'] = limit
        request, params = self.handle_until_option('end_time', request, params)
        response = await self.v2PublicGetFuturesFundingRateHistory(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "actual_funding_rate": "0",
        #                 "funding_time": 1715731221761,
        #                 "market": "BTCUSDT",
        #                 "theoretical_funding_rate": "0"
        #             },
        #         ],
        #         "message": "OK",
        #         "pagination": {
        #             "has_next": True
        #         }
        #     }
        #
        data = self.safe_list(response, 'data', [])
        rates = []
        for i in range(0, len(data)):
            entry = data[i]
            marketId = self.safe_string(entry, 'market')
            symbolInner = self.safe_symbol(marketId, market, None, 'swap')
            timestamp = self.safe_integer(entry, 'funding_time')
            rates.append({
                'info': entry,
                'symbol': symbolInner,
                'fundingRate': self.safe_number(entry, 'actual_funding_rate'),
                'timestamp': timestamp,
                'datetime': self.iso8601(timestamp),
            })
        sorted = self.sort_by(rates, 'timestamp')
        return self.filter_by_symbol_since_limit(sorted, market['symbol'], since, limit)

    def parse_transaction(self, transaction: dict, currency: Currency = None) -> Transaction:
        #
        # fetchDeposits
        #
        #     {
        #         "deposit_id": 5173806,
        #         "created_at": 1714021652557,
        #         "tx_id": "d9f47d2550397c635cb89a8963118f8fe78ef048bc8b6f0caaeaa7dc6",
        #         "tx_id_display": "",
        #         "ccy": "USDT",
        #         "chain": "TRC20",
        #         "deposit_method": "ON_CHAIN",
        #         "amount": "30",
        #         "actual_amount": "",
        #         "to_address": "TYewD2pVWDUwfNr9A",
        #         "confirmations": 20,
        #         "status": "FINISHED",
        #         "tx_explorer_url": "https://tronscan.org/#/transaction",
        #         "to_addr_explorer_url": "https://tronscan.org/#/address",
        #         "remark": ""
        #     }
        #
        # fetchWithdrawals and withdraw
        #
        #     {
        #         "withdraw_id": 259364,
        #         "created_at": 1701323541548,
        #         "withdraw_method": "ON_CHAIN",
        #         "ccy": "USDT",
        #         "amount": "23.845744",
        #         "actual_amount": "22.445744",
        #         "chain": "TRC20",
        #         "tx_fee": "1.4",
        #         "fee_asset": "USDT",
        #         "fee_amount": "1.4",
        #         "to_address": "T8t5i2454dhdhnnnGdi49vMbihvY",
        #         "memo": "",
        #         "tx_id": "1237623941964de9954ed2e36640228d78765c1026",
        #         "confirmations": 18,
        #         "explorer_address_url": "https://tronscan.org/#/address",
        #         "explorer_tx_url": "https://tronscan.org/#/transaction",
        #         "remark": "",
        #         "status": "finished"
        #     }
        #
        address = self.safe_string(transaction, 'to_address')
        tag = self.safe_string(transaction, 'memo')
        if tag is not None:
            if len(tag) < 1:
                tag = None
        remark = self.safe_string(transaction, 'remark')
        if remark is not None:
            if len(remark) < 1:
                remark = None
        txid = self.safe_string(transaction, 'tx_id')
        if txid is not None:
            if len(txid) < 1:
                txid = None
        currencyId = self.safe_string(transaction, 'ccy')
        code = self.safe_currency_code(currencyId, currency)
        timestamp = self.safe_integer(transaction, 'created_at')
        type = 'withdrawal' if ('withdraw_id' in transaction) else 'deposit'
        networkId = self.safe_string(transaction, 'chain')
        feeCost = self.safe_string(transaction, 'tx_fee')
        transferMethod = self.safe_string_lower_2(transaction, 'withdraw_method', 'deposit_method')
        internal = transferMethod == 'local'
        amount = self.safe_number(transaction, 'actual_amount')
        if amount is None:
            amount = self.safe_number(transaction, 'amount')
        if type == 'deposit':
            feeCost = '0'
        feeCurrencyId = self.safe_string(transaction, 'fee_asset')
        fee = {
            'cost': self.parse_number(feeCost),
            'currency': self.safe_currency_code(feeCurrencyId),
        }
        return {
            'info': transaction,
            'id': self.safe_string_2(transaction, 'withdraw_id', 'deposit_id'),
            'txid': txid,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'network': self.network_id_to_code(networkId),
            'address': address,
            'addressTo': address,
            'addressFrom': None,
            'tag': tag,
            'tagTo': None,
            'tagFrom': None,
            'type': type,
            'amount': amount,
            'currency': code,
            'status': self.parse_transaction_status(self.safe_string(transaction, 'status')),
            'updated': None,
            'fee': fee,
            'comment': remark,
            'internal': internal,
        }

    async def transfer(self, code: str, amount: float, fromAccount: str, toAccount: str, params={}) -> TransferEntry:
        """
        transfer currency internally between wallets on the same account

        https://docs.coinex.com/api/v2/assets/transfer/http/transfer

        :param str code: unified currency code
        :param float amount: amount to transfer
        :param str fromAccount: account to transfer from
        :param str toAccount: account to transfer to
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.symbol]: unified ccxt symbol, required when either the fromAccount or toAccount is margin
        :returns dict: a `transfer structure <https://docs.ccxt.com/#/?id=transfer-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        amountToPrecision = self.currency_to_precision(code, amount)
        accountsByType = self.safe_dict(self.options, 'accountsByType', {})
        fromId = self.safe_string(accountsByType, fromAccount, fromAccount)
        toId = self.safe_string(accountsByType, toAccount, toAccount)
        request: dict = {
            'ccy': currency['id'],
            'amount': amountToPrecision,
            'from_account_type': fromId,
            'to_account_type': toId,
        }
        if (fromAccount == 'margin') or (toAccount == 'margin'):
            symbol = self.safe_string(params, 'symbol')
            if symbol is None:
                raise ArgumentsRequired(self.id + ' transfer() the symbol parameter must be defined for a margin account')
            params = self.omit(params, 'symbol')
            request['market'] = self.market_id(symbol)
        if (fromAccount != 'spot') and (toAccount != 'spot'):
            raise BadRequest(self.id + ' transfer() can only be between spot and swap, or spot and margin, either the fromAccount or toAccount must be spot')
        response = await self.v2PrivatePostAssetsTransfer(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {},
        #         "message": "OK"
        #     }
        #
        return self.extend(self.parse_transfer(response, currency), {
            'amount': self.parse_number(amountToPrecision),
            'fromAccount': fromAccount,
            'toAccount': toAccount,
        })

    def parse_transfer_status(self, status):
        statuses: dict = {
            '0': 'ok',
            'SUCCESS': 'ok',
            'OK': 'ok',
            'finished': 'ok',
            'FINISHED': 'ok',
        }
        return self.safe_string(statuses, status, status)

    def parse_transfer(self, transfer: dict, currency: Currency = None) -> TransferEntry:
        timestamp = self.safe_integer(transfer, 'created_at')
        currencyId = self.safe_string(transfer, 'ccy')
        fromId = self.safe_string(transfer, 'from_account_type')
        toId = self.safe_string(transfer, 'to_account_type')
        accountsById = self.safe_value(self.options, 'accountsById', {})
        return {
            'id': None,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'currency': self.safe_currency_code(currencyId, currency),
            'amount': self.safe_number(transfer, 'amount'),
            'fromAccount': self.safe_string(accountsById, fromId, fromId),
            'toAccount': self.safe_string(accountsById, toId, toId),
            'status': self.parse_transfer_status(self.safe_string_2(transfer, 'code', 'status')),
        }

    async def fetch_transfers(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[TransferEntry]:
        """
        fetch a history of internal transfers made on an account

        https://docs.coinex.com/api/v2/assets/transfer/http/list-transfer-history

        :param str code: unified currency code of the currency transferred
        :param int [since]: the earliest time in ms to fetch transfers for
        :param int [limit]: the maximum number of transfer structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.marginMode]: 'cross' or 'isolated' for fetching transfers to and from your margin account
        :returns dict[]: a list of `transfer structures <https://docs.ccxt.com/#/?id=transfer-structure>`
        """
        await self.load_markets()
        if code is None:
            raise ArgumentsRequired(self.id + ' fetchTransfers() requires a code argument')
        currency = self.currency(code)
        request: dict = {
            'ccy': currency['id'],
        }
        marginMode = None
        marginMode, params = self.handle_margin_mode_and_params('fetchTransfers', params)
        if marginMode is not None:
            request['transfer_type'] = 'MARGIN'
        else:
            request['transfer_type'] = 'FUTURES'
        if since is not None:
            request['start_time'] = since
        if limit is not None:
            request['limit'] = limit
        request, params = self.handle_until_option('end_time', request, params)
        response = await self.v2PrivateGetAssetsTransferHistory(self.extend(request, params))
        #
        #     {
        #         "data": [
        #             {
        #                 "created_at": *************,
        #                 "from_account_type": "SPOT",
        #                 "to_account_type": "FUTURES",
        #                 "ccy": "USDT",
        #                 "amount": "10",
        #                 "status": "finished"
        #             },
        #         ],
        #         "pagination": {
        #             "total": 8,
        #             "has_next": False
        #         },
        #         "code": 0,
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_transfers(data, currency, since, limit)

    async def fetch_withdrawals(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all withdrawals made from an account

        https://docs.coinex.com/api/v2/assets/deposit-withdrawal/http/list-withdrawal-history

        :param str [code]: unified currency code
        :param int [since]: the earliest time in ms to fetch withdrawals for
        :param int [limit]: the maximum number of withdrawal structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        request: dict = {}
        currency = None
        if code is not None:
            currency = self.currency(code)
            request['ccy'] = currency['id']
        if limit is not None:
            request['limit'] = limit
        response = await self.v2PrivateGetAssetsWithdraw(self.extend(request, params))
        #
        #     {
        #         "data": [
        #             {
        #                 "withdraw_id": 259364,
        #                 "created_at": 1701323541548,
        #                 "withdraw_method": "ON_CHAIN",
        #                 "ccy": "USDT",
        #                 "amount": "23.845744",
        #                 "actual_amount": "22.445744",
        #                 "chain": "TRC20",
        #                 "tx_fee": "1.4",
        #                 "fee_asset": "USDT",
        #                 "fee_amount": "1.4",
        #                 "to_address": "T8t5i2454dhdhnnnGdi49vMbihvY",
        #                 "memo": "",
        #                 "tx_id": "1237623941964de9954ed2e36640228d78765c1026",
        #                 "confirmations": 18,
        #                 "explorer_address_url": "https://tronscan.org/#/address",
        #                 "explorer_tx_url": "https://tronscan.org/#/transaction",
        #                 "remark": "",
        #                 "status": "finished"
        #             },
        #         ],
        #         "pagination": {
        #             "total": 9,
        #             "has_next": True
        #         },
        #         "code": 0,
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_transactions(data, currency, since, limit)

    async def fetch_deposits(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all deposits made to an account

        https://docs.coinex.com/api/v2/assets/deposit-withdrawal/http/list-deposit-history

        :param str [code]: unified currency code
        :param int [since]: the earliest time in ms to fetch deposits for
        :param int [limit]: the maximum number of deposit structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        await self.load_markets()
        request: dict = {}
        currency = None
        if code is not None:
            currency = self.currency(code)
            request['ccy'] = currency['id']
        if limit is not None:
            request['limit'] = limit
        response = await self.v2PrivateGetAssetsDepositHistory(self.extend(request, params))
        #
        #     {
        #         "data": [
        #             {
        #                 "deposit_id": 5173806,
        #                 "created_at": 1714021652557,
        #                 "tx_id": "d9f47d2550397c635cb89a8963118f8fe78ef048bc8b6f0caaeaa7dc6",
        #                 "tx_id_display": "",
        #                 "ccy": "USDT",
        #                 "chain": "TRC20",
        #                 "deposit_method": "ON_CHAIN",
        #                 "amount": "30",
        #                 "actual_amount": "",
        #                 "to_address": "TYewD2pVWDUwfNr9A",
        #                 "confirmations": 20,
        #                 "status": "FINISHED",
        #                 "tx_explorer_url": "https://tronscan.org/#/transaction",
        #                 "to_addr_explorer_url": "https://tronscan.org/#/address",
        #                 "remark": ""
        #             },
        #         ],
        #         "paginatation": {
        #             "total": 8,
        #             "has_next": True
        #         },
        #         "code": 0,
        #         "message": "OK"
        #     }
        #
        data = self.safe_list(response, 'data', [])
        return self.parse_transactions(data, currency, since, limit)

    def parse_isolated_borrow_rate(self, info: dict, market: Market = None) -> IsolatedBorrowRate:
        #
        #     {
        #         "market": "BTCUSDT",
        #         "ccy": "USDT",
        #         "leverage": 10,
        #         "min_amount": "60",
        #         "max_amount": "500000",
        #         "daily_interest_rate": "0.001"
        #     }
        #
        marketId = self.safe_string(info, 'market')
        market = self.safe_market(marketId, market, None, 'spot')
        currency = self.safe_string(info, 'ccy')
        rate = self.safe_number(info, 'daily_interest_rate')
        baseRate = None
        quoteRate = None
        if currency == market['baseId']:
            baseRate = rate
        elif currency == market['quoteId']:
            quoteRate = rate
        return {
            'symbol': market['symbol'],
            'base': market['base'],
            'baseRate': baseRate,
            'quote': market['quote'],
            'quoteRate': quoteRate,
            'period': 86400000,
            'timestamp': None,
            'datetime': None,
            'info': info,
        }

    async def fetch_isolated_borrow_rate(self, symbol: str, params={}) -> IsolatedBorrowRate:
        """
        fetch the rate of interest to borrow a currency for margin trading

        https://docs.coinex.com/api/v2/assets/loan-flat/http/list-margin-interest-limit

        :param str symbol: unified symbol of the market to fetch the borrow rate for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str params['code']: unified currency code
        :returns dict: an `isolated borrow rate structure <https://docs.ccxt.com/#/?id=isolated-borrow-rate-structure>`
        """
        await self.load_markets()
        code = self.safe_string(params, 'code')
        if code is None:
            raise ArgumentsRequired(self.id + ' fetchIsolatedBorrowRate() requires a code parameter')
        params = self.omit(params, 'code')
        currency = self.currency(code)
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
            'ccy': currency['id'],
        }
        response = await self.v2PrivateGetAssetsMarginInterestLimit(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "market": "BTCUSDT",
        #             "ccy": "USDT",
        #             "leverage": 10,
        #             "min_amount": "60",
        #             "max_amount": "500000",
        #             "daily_interest_rate": "0.001"
        #         },
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data', {})
        return self.parse_isolated_borrow_rate(data, market)

    async def fetch_borrow_interest(self, code: Str = None, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[BorrowInterest]:
        """
        fetch the interest owed by the user for borrowing currency for margin trading

        https://docs.coinex.com/api/v2/assets/loan-flat/http/list-margin-borrow-history

        :param str [code]: unified currency code
        :param str [symbol]: unified market symbol when fetch interest in isolated markets
        :param int [since]: the earliest time in ms to fetch borrrow interest for
        :param int [limit]: the maximum number of structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `borrow interest structures <https://docs.ccxt.com/#/?id=borrow-interest-structure>`
        """
        await self.load_markets()
        request: dict = {}
        market = None
        if symbol is not None:
            market = self.market(symbol)
            request['market'] = market['id']
        if limit is not None:
            request['limit'] = limit
        response = await self.v2PrivateGetAssetsMarginBorrowHistory(self.extend(request, params))
        #
        #     {
        #         "data": [
        #             {
        #                 "borrow_id": 2642934,
        #                 "created_at": 1654761016000,
        #                 "market": "BTCUSDT",
        #                 "ccy": "USDT",
        #                 "daily_interest_rate": "0.001",
        #                 "expired_at": 1655625016000,
        #                 "borrow_amount": "100",
        #                 "to_repaied_amount": "0",
        #                 "is_auto_renew": False,
        #                 "status": "finish"
        #             },
        #         ],
        #         "pagination": {
        #             "total": 4,
        #             "has_next": True
        #         },
        #         "code": 0,
        #         "message": "OK"
        #     }
        #
        rows = self.safe_value(response, 'data', [])
        interest = self.parse_borrow_interests(rows, market)
        return self.filter_by_currency_since_limit(interest, code, since, limit)

    def parse_borrow_interest(self, info: dict, market: Market = None) -> BorrowInterest:
        #
        #     {
        #         "borrow_id": 2642934,
        #         "created_at": 1654761016000,
        #         "market": "BTCUSDT",
        #         "ccy": "USDT",
        #         "daily_interest_rate": "0.001",
        #         "expired_at": 1655625016000,
        #         "borrow_amount": "100",
        #         "to_repaied_amount": "0",
        #         "is_auto_renew": False,
        #         "status": "finish"
        #     }
        #
        marketId = self.safe_string(info, 'market')
        market = self.safe_market(marketId, market, None, 'spot')
        timestamp = self.safe_integer(info, 'expired_at')
        return {
            'info': info,
            'symbol': market['symbol'],
            'currency': self.safe_currency_code(self.safe_string(info, 'ccy')),
            'interest': self.safe_number(info, 'to_repaied_amount'),
            'interestRate': self.safe_number(info, 'daily_interest_rate'),
            'amountBorrowed': self.safe_number(info, 'borrow_amount'),
            'marginMode': 'isolated',
            'timestamp': timestamp,  # expiry time
            'datetime': self.iso8601(timestamp),
        }

    async def borrow_isolated_margin(self, symbol: str, code: str, amount: float, params={}):
        """
        create a loan to borrow margin

        https://docs.coinex.com/api/v2/assets/loan-flat/http/margin-borrow

        :param str symbol: unified market symbol, required for coinex
        :param str code: unified currency code of the currency to borrow
        :param float amount: the amount to borrow
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param boolean [params.isAutoRenew]: whether to renew the margin loan automatically or not, default is False
        :returns dict: a `margin loan structure <https://docs.ccxt.com/#/?id=margin-loan-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        currency = self.currency(code)
        isAutoRenew = self.safe_bool_2(params, 'isAutoRenew', 'is_auto_renew', False)
        params = self.omit(params, 'isAutoRenew')
        request: dict = {
            'market': market['id'],
            'ccy': currency['id'],
            'borrow_amount': self.currency_to_precision(code, amount),
            'is_auto_renew': isAutoRenew,
        }
        response = await self.v2PrivatePostAssetsMarginBorrow(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "borrow_id": 13784021,
        #             "market": "BTCUSDT",
        #             "ccy": "USDT",
        #             "daily_interest_rate": "0.001",
        #             "expired_at": 1717299948340,
        #             "borrow_amount": "60",
        #             "to_repaied_amount": "60.0025",
        #             "status": "loan"
        #         },
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data', {})
        transaction = self.parse_margin_loan(data, currency)
        return self.extend(transaction, {
            'amount': amount,
            'symbol': symbol,
        })

    async def repay_isolated_margin(self, symbol: str, code: str, amount, params={}):
        """
        repay borrowed margin and interest

        https://docs.coinex.com/api/v2/assets/loan-flat/http/margin-repay

        :param str symbol: unified market symbol, required for coinex
        :param str code: unified currency code of the currency to repay
        :param float amount: the amount to repay
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.borrow_id]: extra parameter that is not required
        :returns dict: a `margin loan structure <https://docs.ccxt.com/#/?id=margin-loan-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        currency = self.currency(code)
        request: dict = {
            'market': market['id'],
            'ccy': currency['id'],
            'amount': self.currency_to_precision(code, amount),
        }
        response = await self.v2PrivatePostAssetsMarginRepay(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {},
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data', {})
        transaction = self.parse_margin_loan(data, currency)
        return self.extend(transaction, {
            'amount': amount,
            'symbol': symbol,
        })

    def parse_margin_loan(self, info, currency: Currency = None):
        #
        #     {
        #         "borrow_id": 13784021,
        #         "market": "BTCUSDT",
        #         "ccy": "USDT",
        #         "daily_interest_rate": "0.001",
        #         "expired_at": 1717299948340,
        #         "borrow_amount": "60",
        #         "to_repaied_amount": "60.0025",
        #         "status": "loan"
        #     }
        #
        currencyId = self.safe_string(info, 'ccy')
        marketId = self.safe_string(info, 'market')
        timestamp = self.safe_integer(info, 'expired_at')
        return {
            'id': self.safe_integer(info, 'borrow_id'),
            'currency': self.safe_currency_code(currencyId, currency),
            'amount': self.safe_string(info, 'borrow_amount'),
            'symbol': self.safe_symbol(marketId, None, None, 'spot'),
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'info': info,
        }

    async def fetch_deposit_withdraw_fee(self, code: str, params={}):
        """
        fetch the fee for deposits and withdrawals

        https://docs.coinex.com/api/v2/assets/deposit-withdrawal/http/get-deposit-withdrawal-config

        :param str code: unified currency code
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `fee structure <https://docs.ccxt.com/#/?id=fee-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'ccy': currency['id'],
        }
        response = await self.v2PublicGetAssetsDepositWithdrawConfig(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "asset": {
        #                 "ccy": "USDT",
        #                 "deposit_enabled": True,
        #                 "withdraw_enabled": True,
        #                 "inter_transfer_enabled": True,
        #                 "is_st": False
        #             },
        #             "chains": [
        #                 {
        #                     "chain": "TRC20",
        #                     "min_deposit_amount": "2.4",
        #                     "min_withdraw_amount": "2.4",
        #                     "deposit_enabled": True,
        #                     "withdraw_enabled": True,
        #                     "deposit_delay_minutes": 0,
        #                     "safe_confirmations": 10,
        #                     "irreversible_confirmations": 20,
        #                     "deflation_rate": "0",
        #                     "withdrawal_fee": "2.4",
        #                     "withdrawal_precision": 6,
        #                     "memo": "",
        #                     "is_memo_required_for_deposit": False,
        #                     "explorer_asset_url": "https://tronscan.org/#/token20/TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
        #                 },
        #             ]
        #         },
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data', {})
        return self.parse_deposit_withdraw_fee(data, currency)

    def parse_deposit_withdraw_fee(self, fee, currency: Currency = None):
        #
        #     {
        #         "asset": {
        #             "ccy": "USDT",
        #             "deposit_enabled": True,
        #             "withdraw_enabled": True,
        #             "inter_transfer_enabled": True,
        #             "is_st": False
        #         },
        #         "chains": [
        #             {
        #                 "chain": "TRC20",
        #                 "min_deposit_amount": "2.4",
        #                 "min_withdraw_amount": "2.4",
        #                 "deposit_enabled": True,
        #                 "withdraw_enabled": True,
        #                 "deposit_delay_minutes": 0,
        #                 "safe_confirmations": 10,
        #                 "irreversible_confirmations": 20,
        #                 "deflation_rate": "0",
        #                 "withdrawal_fee": "2.4",
        #                 "withdrawal_precision": 6,
        #                 "memo": "",
        #                 "is_memo_required_for_deposit": False,
        #                 "explorer_asset_url": "https://tronscan.org/#/token20/TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
        #             },
        #         ]
        #     }
        #
        result: dict = {
            'info': fee,
            'withdraw': {
                'fee': None,
                'percentage': None,
            },
            'deposit': {
                'fee': None,
                'percentage': None,
            },
            'networks': {},
        }
        chains = self.safe_list(fee, 'chains', [])
        asset = self.safe_dict(fee, 'asset', {})
        for i in range(0, len(chains)):
            entry = chains[i]
            isWithdrawEnabled = self.safe_bool(entry, 'withdraw_enabled')
            if isWithdrawEnabled:
                result['withdraw']['fee'] = self.safe_number(entry, 'withdrawal_fee')
                result['withdraw']['percentage'] = False
                networkId = self.safe_string(entry, 'chain')
                if networkId:
                    networkCode = self.network_id_to_code(networkId, self.safe_string(asset, 'ccy'))
                    result['networks'][networkCode] = {
                        'withdraw': {
                            'fee': self.safe_number(entry, 'withdrawal_fee'),
                            'percentage': False,
                        },
                        'deposit': {
                            'fee': None,
                            'percentage': None,
                        },
                    }
        return result

    async def fetch_leverage(self, symbol: str, params={}) -> Leverage:
        """
        fetch the set leverage for a market

        https://docs.coinex.com/api/v2/assets/loan-flat/http/list-margin-interest-limit

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str params['code']: unified currency code
        :returns dict: a `leverage structure <https://docs.ccxt.com/#/?id=leverage-structure>`
        """
        await self.load_markets()
        code = self.safe_string(params, 'code')
        if code is None:
            raise ArgumentsRequired(self.id + ' fetchLeverage() requires a code parameter')
        params = self.omit(params, 'code')
        currency = self.currency(code)
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
            'ccy': currency['id'],
        }
        response = await self.v2PrivateGetAssetsMarginInterestLimit(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "market": "BTCUSDT",
        #             "ccy": "USDT",
        #             "leverage": 10,
        #             "min_amount": "50",
        #             "max_amount": "500000",
        #             "daily_interest_rate": "0.001"
        #         },
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data', {})
        return self.parse_leverage(data, market)

    def parse_leverage(self, leverage: dict, market: Market = None) -> Leverage:
        #
        #     {
        #         "market": "BTCUSDT",
        #         "ccy": "USDT",
        #         "leverage": 10,
        #         "min_amount": "50",
        #         "max_amount": "500000",
        #         "daily_interest_rate": "0.001"
        #     }
        #
        marketId = self.safe_string(leverage, 'market')
        leverageValue = self.safe_integer(leverage, 'leverage')
        return {
            'info': leverage,
            'symbol': self.safe_symbol(marketId, market, None, 'spot'),
            'marginMode': 'isolated',
            'longLeverage': leverageValue,
            'shortLeverage': leverageValue,
        }

    async def fetch_position_history(self, symbol: str, since: Int = None, limit: Int = None, params={}) -> List[Position]:
        """
        fetches historical positions

        https://docs.coinex.com/api/v2/futures/position/http/list-finished-position

        :param str symbol: unified contract symbol
        :param int [since]: the earliest time in ms to fetch positions for
        :param int [limit]: the maximum amount of records to fetch, default is 10
        :param dict [params]: extra parameters specific to the exchange api endpoint
        :param int [params.until]: the latest time in ms to fetch positions for
        :returns dict[]: a list of `position structures <https://docs.ccxt.com/#/?id=position-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'market_type': 'FUTURES',
            'market': market['id'],
        }
        if limit is not None:
            request['limit'] = limit
        if since is not None:
            request['start_time'] = since
        request, params = self.handle_until_option('end_time', request, params)
        response = await self.v2PrivateGetFuturesFinishedPosition(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "position_id": 305891033,
        #                 "market": "BTCUSDT",
        #                 "market_type": "FUTURES",
        #                 "side": "long",
        #                 "margin_mode": "cross",
        #                 "open_interest": "0.0001",
        #                 "close_avbl": "0.0001",
        #                 "ath_position_amount": "0.0001",
        #                 "unrealized_pnl": "0",
        #                 "realized_pnl": "-0.00311684",
        #                 "avg_entry_price": "62336.8",
        #                 "cml_position_value": "6.23368",
        #                 "max_position_value": "6.23368",
        #                 "created_at": 1715152208041,
        #                 "updated_at": 1715152208041,
        #                 "take_profit_price": "0",
        #                 "stop_loss_price": "0",
        #                 "take_profit_type": "",
        #                 "stop_loss_type": "",
        #                 "settle_price": "62336.8",
        #                 "settle_value": "6.23368",
        #                 "leverage": "3",
        #                 "margin_avbl": "2.07789333",
        #                 "ath_margin_size": "2.07789333",
        #                 "position_margin_rate": "2.40545879023305655728",
        #                 "maintenance_margin_rate": "0.005",
        #                 "maintenance_margin_value": "0.03118094",
        #                 "liq_price": "0",
        #                 "bkr_price": "0",
        #                 "adl_level": 1
        #             }
        #         ],
        #         "message": "OK",
        #         "pagination": {
        #             "has_next": False
        #         }
        #     }
        #
        records = self.safe_list(response, 'data', [])
        positions = self.parse_positions(records)
        return self.filter_by_symbol_since_limit(positions, symbol, since, limit)

    async def close_position(self, symbol: str, side: OrderSide = None, params={}) -> Order:
        """
        closes an open position for a market

        https://docs.coinex.com/api/v2/futures/position/http/close-position

        :param str symbol: unified CCXT market symbol
        :param str [side]: buy or sell, not used by coinex
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str params['type']: required by coinex, one of: limit, market, maker_only, ioc or fok, default is *market*
        :param str [params.price]: the price to fulfill the order, ignored in market orders
        :param str [params.amount]: the amount to trade in units of the base currency
        :param str [params.clientOrderId]: the client id of the order
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        type = self.safe_string(params, 'type', 'market')
        request: dict = {
            'market': market['id'],
            'market_type': 'FUTURES',
            'type': type,
        }
        clientOrderId = self.safe_string_2(params, 'client_id', 'clientOrderId')
        if clientOrderId is not None:
            request['client_id'] = clientOrderId
        params = self.omit(params, 'clientOrderId')
        response = await self.v2PrivatePostFuturesClosePosition(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": {
        #             "amount": "0.0001",
        #             "client_id": "",
        #             "created_at": 1729666043969,
        #             "fee": "0.00335858",
        #             "fee_ccy": "USDT",
        #             "filled_amount": "0.0001",
        #             "filled_value": "6.717179",
        #             "last_filled_amount": "0.0001",
        #             "last_filled_price": "67171.79",
        #             "maker_fee_rate": "0",
        #             "market": "BTCUSDT",
        #             "market_type": "FUTURES",
        #             "order_id": 155477479761,
        #             "price": "0",
        #             "realized_pnl": "-0.001823",
        #             "side": "sell",
        #             "taker_fee_rate": "0.0005",
        #             "type": "market",
        #             "unfilled_amount": "0",
        #             "updated_at": 1729666043969
        #         },
        #         "message": "OK"
        #     }
        #
        data = self.safe_dict(response, 'data', {})
        return self.parse_order(data, market)

    def handle_margin_mode_and_params(self, methodName, params={}, defaultValue=None):
        """
 @ignore
        marginMode specified by params["marginMode"], self.options["marginMode"], self.options["defaultMarginMode"], params["margin"] = True or self.options["defaultType"] = 'margin'
        :param dict params: extra parameters specific to the exchange api endpoint
        :returns Array: the marginMode in lowercase
        """
        defaultType = self.safe_string(self.options, 'defaultType')
        isMargin = self.safe_bool(params, 'margin', False)
        marginMode = None
        marginMode, params = super(coinex, self).handle_margin_mode_and_params(methodName, params, defaultValue)
        if marginMode is None:
            if (defaultType == 'margin') or (isMargin is True):
                marginMode = 'isolated'
        return [marginMode, params]

    def nonce(self):
        return self.milliseconds()

    def sign(self, path, api=[], method='GET', params={}, headers=None, body=None):
        path = self.implode_params(path, params)
        version = api[0]
        requestUrl = api[1]
        url = self.urls['api'][requestUrl] + '/' + version + '/' + path
        query = self.omit(params, self.extract_params(path))
        nonce = str(self.nonce())
        if method == 'POST':
            parts = path.split('/')
            firstPart = self.safe_string(parts, 0, '')
            numParts = len(parts)
            lastPart = self.safe_string(parts, numParts - 1, '')
            lastWords = lastPart.split('_')
            numWords = len(lastWords)
            lastWord = self.safe_string(lastWords, numWords - 1, '')
            if (firstPart == 'order') and (lastWord == 'limit' or lastWord == 'market'):
                # inject in implicit API calls
                # POST /order/limit - Place limit orders
                # POST /order/market - Place market orders
                # POST /order/stop/limit - Place stop limit orders
                # POST /order/stop/market - Place stop market orders
                # POST /perpetual/v1/order/put_limit - Place limit orders
                # POST /perpetual/v1/order/put_market - Place market orders
                # POST /perpetual/v1/order/put_stop_limit - Place stop limit orders
                # POST /perpetual/v1/order/put_stop_market - Place stop market orders
                clientOrderId = self.safe_string(params, 'client_id')
                if clientOrderId is None:
                    defaultId = 'x-*********'
                    brokerId = self.safe_value(self.options, 'brokerId', defaultId)
                    query['client_id'] = brokerId + '_' + self.uuid16()
        if requestUrl == 'perpetualPrivate':
            self.check_required_credentials()
            query = self.extend({
                'access_id': self.apiKey,
                'timestamp': nonce,
            }, query)
            query = self.keysort(query)
            urlencoded = self.rawencode(query)
            signature = self.hash(self.encode(urlencoded + '&secret_key=' + self.secret), 'sha256')
            headers = {
                'Authorization': signature.lower(),
                'AccessId': self.apiKey,
            }
            if (method == 'GET') or (method == 'PUT'):
                url += '?' + urlencoded
            else:
                headers['Content-Type'] = 'application/x-www-form-urlencoded'
                body = urlencoded
        elif requestUrl == 'public' or requestUrl == 'perpetualPublic':
            if query:
                url += '?' + self.urlencode(query)
        else:
            if version == 'v1':
                self.check_required_credentials()
                query = self.extend({
                    'access_id': self.apiKey,
                    'tonce': nonce,
                }, query)
                query = self.keysort(query)
                urlencoded = self.rawencode(query)
                signature = self.hash(self.encode(urlencoded + '&secret_key=' + self.secret), 'md5')
                headers = {
                    'Authorization': signature.upper(),
                    'Content-Type': 'application/json',
                }
                if (method == 'GET') or (method == 'DELETE') or (method == 'PUT'):
                    url += '?' + urlencoded
                else:
                    body = self.json(query)
            elif version == 'v2':
                self.check_required_credentials()
                query = self.keysort(query)
                urlencoded = self.rawencode(query)
                preparedString = method + '/' + version + '/' + path
                if method == 'POST':
                    body = self.json(query)
                    preparedString += body
                elif urlencoded:
                    preparedString += '?' + urlencoded
                preparedString += nonce + self.secret
                signature = self.hash(self.encode(preparedString), 'sha256')
                headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-COINEX-KEY': self.apiKey,
                    'X-COINEX-SIGN': signature,
                    'X-COINEX-TIMESTAMP': nonce,
                }
                if method != 'POST':
                    if urlencoded:
                        url += '?' + urlencoded
        return {'url': url, 'method': method, 'body': body, 'headers': headers}

    def handle_errors(self, httpCode: int, reason: str, url: str, method: str, headers: dict, body: str, response, requestHeaders, requestBody):
        if response is None:
            return None
        code = self.safe_string(response, 'code')
        data = self.safe_value(response, 'data')
        message = self.safe_string(response, 'message')
        if (code != '0') or ((message != 'Success') and (message != 'Succeeded') and (message.lower() != 'ok') and not data):
            feedback = self.id + ' ' + message
            self.throw_broadly_matched_exception(self.exceptions['broad'], message, feedback)
            self.throw_exactly_matched_exception(self.exceptions['exact'], code, feedback)
            raise ExchangeError(feedback)
        return None

    async def fetch_margin_adjustment_history(self, symbol: Str = None, type: Str = None, since: Num = None, limit: Num = None, params={}) -> List[MarginModification]:
        """
        fetches the history of margin added or reduced from contract isolated positions

        https://docs.coinex.com/api/v2/futures/position/http/list-position-margin-history

        :param str symbol: unified market symbol
        :param str [type]: not used by coinex fetchMarginAdjustmentHistory
        :param int [since]: timestamp in ms of the earliest change to fetch
        :param int [limit]: the maximum amount of changes to fetch, default is 10
        :param dict params: extra parameters specific to the exchange api endpoint
        :param int [params.until]: timestamp in ms of the latest change to fetch
        :param int [params.positionId]: the id of the position that you want to retrieve margin adjustment history for
        :returns dict[]: a list of `margin structures <https://docs.ccxt.com/#/?id=margin-loan-structure>`
        """
        await self.load_markets()
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchMarginAdjustmentHistory() requires a symbol argument')
        positionId = self.safe_integer_2(params, 'positionId', 'position_id')
        params = self.omit(params, 'positionId')
        if positionId is None:
            raise ArgumentsRequired(self.id + ' fetchMarginAdjustmentHistory() requires a positionId parameter')
        market = self.market(symbol)
        request: dict = {
            'market': market['id'],
            'market_type': 'FUTURES',
            'position_id': positionId,
        }
        request, params = self.handle_until_option('end_time', request, params)
        if since is not None:
            request['start_time'] = since
        if limit is not None:
            request['limit'] = limit
        response = await self.v2PrivateGetFuturesPositionMarginHistory(self.extend(request, params))
        #
        #     {
        #         "code": 0,
        #         "data": [
        #             {
        #                 "bkr_pirce": "24698.56000000000000005224",
        #                 "created_at": 1715489978697,
        #                 "leverage": "3",
        #                 "liq_price": "24822.67336683417085432386",
        #                 "margin_avbl": "3.634928",
        #                 "margin_change": "-1.5",
        #                 "margin_mode": "isolated",
        #                 "market": "BTCUSDT",
        #                 "market_type": "FUTURES",
        #                 "open_interest": "0.0001",
        #                 "position_id": 306458800,
        #                 "settle_price": "61047.84"
        #             },
        #         ],
        #         "message": "OK",
        #         "pagination": {
        #             "has_next": True
        #         }
        #     }
        #
        data = self.safe_list(response, 'data', [])
        modifications = self.parse_margin_modifications(data, None, 'market', 'swap')
        return self.filter_by_symbol_since_limit(modifications, symbol, since, limit)
