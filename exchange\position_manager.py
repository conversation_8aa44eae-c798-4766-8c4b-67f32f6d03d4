"""
Position Manager Module
Handles position management, stop loss, take profit, and risk management
"""

import os
import logging
import datetime
import json
from analysis.indicators import find_recent_low, find_recent_high

# Set up logging
logger = logging.getLogger("position_manager")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/position_manager.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class PositionManager:
    """
    Manages trading positions, stop loss, take profit, and risk management
    """

    def __init__(self, exchange_api, max_positions=3, max_risk_percent=5):
        """
        Initialize the position manager

        Args:
            exchange_api: Exchange API instance
            max_positions (int): Maximum number of open positions
            max_risk_percent (float): Maximum risk percentage across all positions
        """
        self.exchange = exchange_api
        self.max_positions = max_positions
        self.max_risk_percent = max_risk_percent
        self.positions = {}  # Dictionary of open positions
        self.position_history = []  # List of closed positions
        self.consecutive_losses = 0  # Counter for consecutive losses
        self.last_trade_time = None  # Timestamp of last trade
        self.trading_freeze_until = None  # Timestamp until trading is frozen
        self.volatility_suspension_until = None  # Timestamp until trading is suspended due to volatility
        self.base_trade_amount = 100  # Base trade amount in USDT
        self.current_trade_amount = 100  # Current trade amount in USDT (adjusted for compounding)

        # Load position history if exists
        self._load_position_history()

        logger.info(f"PositionManager initialized with max positions: {max_positions}, max risk: {max_risk_percent}%")

    def _load_position_history(self):
        """
        Load position history from file
        """
        try:
            history_file = f"logs/position_history.json"
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    self.position_history = json.load(f)
                logger.info(f"Loaded {len(self.position_history)} positions from history file")
        except Exception as e:
            logger.error(f"Error loading position history: {e}")

    def _save_position_history(self):
        """
        Save position history to file
        """
        try:
            history_file = f"logs/position_history.json"
            with open(history_file, 'w') as f:
                json.dump(self.position_history, f, indent=4)
            logger.info(f"Saved {len(self.position_history)} positions to history file")
        except Exception as e:
            logger.error(f"Error saving position history: {e}")

    def get_available_balance(self):
        """
        Get available balance

        Returns:
            float: Available balance in USDT
        """
        try:
            balance = self.exchange.get_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0)
            return usdt_balance
        except Exception as e:
            logger.error(f"Error getting available balance: {e}")
            return 0

    def calculate_position_size(self, symbol, risk_percent, stop_loss_percent):
        """
        Calculate position size based on risk percentage and stop loss

        Args:
            symbol (str): Trading pair symbol
            risk_percent (float): Risk percentage (0-100)
            stop_loss_percent (float): Stop loss percentage (0-100)

        Returns:
            float: Position size in USDT
        """
        try:
            # Get available balance
            available_balance = self.get_available_balance()

            # Calculate risk amount
            risk_amount = available_balance * (risk_percent / 100)

            # Calculate position size
            if stop_loss_percent > 0:
                position_size = risk_amount / (stop_loss_percent / 100)
            else:
                position_size = 0

            # Limit position size to available balance
            position_size = min(position_size, available_balance)

            # Ensure minimum position size
            if position_size < 10:
                logger.warning(f"Position size {position_size:.2f} USDT is below minimum (10 USDT)")
                return 0

            logger.info(f"Calculated position size: {position_size:.2f} USDT (risk: {risk_percent}%, stop loss: {stop_loss_percent}%)")
            return position_size
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0

    def calculate_risk_percent(self, is_volatile):
        """
        Calculate risk percentage based on market conditions

        Args:
            is_volatile (bool): Whether the market is volatile

        Returns:
            float: Risk percentage (0-100)
        """
        try:
            # Calculate total risk of open positions
            total_risk = self.calculate_total_risk()

            # Calculate remaining risk
            remaining_risk = self.max_risk_percent - total_risk

            # Determine risk percentage based on volatility
            if is_volatile:
                risk_percent = min(1, remaining_risk)  # 1% in volatile conditions
            else:
                risk_percent = min(2, remaining_risk)  # 2% in normal conditions

            # Ensure risk is positive
            risk_percent = max(0, risk_percent)

            logger.info(f"Calculated risk percentage: {risk_percent}% (volatile: {is_volatile}, total risk: {total_risk}%, remaining: {remaining_risk}%)")
            return risk_percent
        except Exception as e:
            logger.error(f"Error calculating risk percentage: {e}")
            return 0

    def calculate_total_risk(self):
        """
        Calculate total risk of all open positions

        Returns:
            float: Total risk percentage (0-100)
        """
        try:
            total_risk = sum(position.get('risk_percent', 0) for position in self.positions.values())
            return total_risk
        except Exception as e:
            logger.error(f"Error calculating total risk: {e}")
            return 0

    def can_open_position(self, symbol, is_volatile):
        """
        Check if a new position can be opened

        Args:
            symbol (str): Trading pair symbol
            is_volatile (bool): Whether the market is volatile

        Returns:
            bool: True if a new position can be opened, False otherwise
        """
        try:
            # Check if trading is frozen
            if self.is_trading_frozen():
                logger.warning("Trading is frozen due to consecutive losses")
                return False

            # Check if trading is suspended due to volatility
            if self.is_volatility_suspended():
                logger.warning("Trading is suspended due to high volatility")
                return False

            # Check if maximum number of positions is reached
            if len(self.positions) >= self.max_positions:
                logger.warning(f"Maximum number of positions reached ({self.max_positions})")
                return False

            # Check if position for this symbol already exists
            if symbol in self.positions:
                logger.warning(f"Position for {symbol} already exists")
                return False

            # Check if available balance is sufficient
            available_balance = self.get_available_balance()
            if available_balance < 10:
                logger.warning(f"Available balance ({available_balance:.2f} USDT) is below minimum (10 USDT)")
                return False

            # Calculate risk percentage
            risk_percent = self.calculate_risk_percent(is_volatile)
            if risk_percent <= 0:
                logger.warning(f"Risk percentage ({risk_percent}%) is too low")
                return False

            return True
        except Exception as e:
            logger.error(f"Error checking if position can be opened: {e}")
            return False

    def is_trading_frozen(self):
        """
        Check if trading is frozen due to consecutive losses

        Returns:
            bool: True if trading is frozen, False otherwise
        """
        try:
            if self.trading_freeze_until is None:
                return False

            current_time = datetime.datetime.now()
            return current_time < self.trading_freeze_until
        except Exception as e:
            logger.error(f"Error checking if trading is frozen: {e}")
            return True  # Conservative approach: assume frozen if error

    def is_volatility_suspended(self):
        """
        Check if trading is suspended due to high volatility

        Returns:
            bool: True if trading is suspended, False otherwise
        """
        try:
            if self.volatility_suspension_until is None:
                return False

            current_time = datetime.datetime.now()
            return current_time < self.volatility_suspension_until
        except Exception as e:
            logger.error(f"Error checking if trading is suspended due to volatility: {e}")
            return True  # Conservative approach: assume suspended if error

    def suspend_trading_volatility(self, minutes=5):
        """
        Suspend trading due to high volatility

        Args:
            minutes (int): Number of minutes to suspend trading
        """
        try:
            current_time = datetime.datetime.now()
            self.volatility_suspension_until = current_time + datetime.timedelta(minutes=minutes)
            logger.warning(f"Trading suspended due to high volatility until {self.volatility_suspension_until}")
        except Exception as e:
            logger.error(f"Error suspending trading due to volatility: {e}")

    def freeze_trading_consecutive_losses(self, hours=1):
        """
        Freeze trading due to consecutive losses

        Args:
            hours (int): Number of hours to freeze trading
        """
        try:
            current_time = datetime.datetime.now()
            self.trading_freeze_until = current_time + datetime.timedelta(hours=hours)
            logger.warning(f"Trading frozen due to consecutive losses until {self.trading_freeze_until}")
        except Exception as e:
            logger.error(f"Error freezing trading due to consecutive losses: {e}")

    def open_position(self, symbol, side, entry_price, amount, market_data, risk_percent, stop_loss_percent, is_stacked_entry=False, stack_percentage=50):
        """
        Open a new position with stacking entry support

        Args:
            symbol (str): Trading pair symbol
            side (str): Position side ('buy' or 'sell')
            entry_price (float): Entry price
            amount (float): Position amount in USDT
            market_data (dict): Market data
            risk_percent (float): Risk percentage (0-100)
            stop_loss_percent (float): Stop loss percentage (0-100)
            is_stacked_entry (bool): Whether this is a stacked entry
            stack_percentage (int): Percentage of total amount for stacked entry (1-100)

        Returns:
            dict: Position information
        """
        try:
            # Check if this is a stacked entry for an existing position
            if is_stacked_entry and symbol in self.positions:
                return self._add_to_position(symbol, entry_price, amount, stack_percentage)

            # Calculate quantity
            quantity = amount / entry_price

            # Calculate stop loss price - adjusted to 0.8-1.0% as requested
            if side == 'buy':
                recent_low = find_recent_low(market_data.get('low_prices', [entry_price * 0.99]))
                stop_loss_price = recent_low * (1 - 0.008)  # 0.8% below recent low (increased from 0.5%)
            else:  # sell
                recent_high = find_recent_high(market_data.get('high_prices', [entry_price * 1.01]))
                stop_loss_price = recent_high * (1 + 0.008)  # 0.8% above recent high (increased from 0.5%)

            # Calculate take profit prices - adjusted to 0.7-1.0% as requested
            # Use fixed percentage for scalping mode
            if side == 'buy':
                tp1_price = entry_price * 1.007  # 0.7% profit (reduced from 1R)
                tp2_price = entry_price * 1.015  # 1.5% profit
                tp3_price = entry_price * 1.025  # 2.5% profit
            else:  # sell
                tp1_price = entry_price * 0.993  # 0.7% profit (reduced from 1R)
                tp2_price = entry_price * 0.985  # 1.5% profit
                tp3_price = entry_price * 0.975  # 2.5% profit

            # Create position
            position = {
                'symbol': symbol,
                'side': side,
                'entry_price': entry_price,
                'quantity': quantity,
                'amount': amount,
                'entry_time': datetime.datetime.now().isoformat(),
                'stop_loss_price': stop_loss_price,
                'tp1_price': tp1_price,
                'tp2_price': tp2_price,
                'tp3_price': tp3_price,
                'risk_percent': risk_percent,
                'stop_loss_percent': stop_loss_percent,
                'status': 'open',
                'partial_exit': False,
                'breakeven_triggered': False,
                'trailing_stop_triggered': False,
                'current_stop_loss_price': stop_loss_price,
                'market_data': market_data,
                'stacked_entries': [],
                'is_initial_entry': True,
                'entry_confirmed': False,
                'total_quantity': quantity,
                'avg_entry_price': entry_price
            }

            # Add position to positions dictionary
            self.positions[symbol] = position

            # Update last trade time
            self.last_trade_time = datetime.datetime.now()

            logger.info(f"Opened {side.upper()} position for {symbol}: {quantity:.8f} at ${entry_price:.2f}")
            logger.info(f"Stop loss: ${stop_loss_price:.2f}, TP1: ${tp1_price:.2f}, TP2: ${tp2_price:.2f}, TP3: ${tp3_price:.2f}")

            return position
        except Exception as e:
            logger.error(f"Error opening position: {e}")
            return None

    def _add_to_position(self, symbol, entry_price, amount, percentage=50):
        """
        Add to an existing position (stacked entry)

        Args:
            symbol (str): Trading pair symbol
            entry_price (float): Entry price
            amount (float): Position amount in USDT
            percentage (int): Percentage of total amount (1-100)

        Returns:
            dict: Updated position information
        """
        try:
            if symbol not in self.positions:
                logger.warning(f"Cannot add to position for {symbol} - position not found")
                return None

            position = self.positions[symbol]
            side = position['side']

            # Calculate quantity for this entry
            quantity = amount / entry_price

            # Calculate new average entry price
            total_quantity = position['total_quantity'] + quantity
            avg_entry_price = ((position['avg_entry_price'] * position['total_quantity']) +
                              (entry_price * quantity)) / total_quantity

            # Record stacked entry
            stacked_entry = {
                'entry_price': entry_price,
                'quantity': quantity,
                'amount': amount,
                'entry_time': datetime.datetime.now().isoformat(),
                'percentage': percentage
            }

            # Update position
            position['stacked_entries'].append(stacked_entry)
            position['total_quantity'] = total_quantity
            position['avg_entry_price'] = avg_entry_price
            position['entry_confirmed'] = True

            # Recalculate take profit levels based on average entry price
            if side == 'buy':
                position['tp1_price'] = avg_entry_price * 1.007  # 0.7% profit
                position['tp2_price'] = avg_entry_price * 1.015  # 1.5% profit
                position['tp3_price'] = avg_entry_price * 1.025  # 2.5% profit
            else:  # sell
                position['tp1_price'] = avg_entry_price * 0.993  # 0.7% profit
                position['tp2_price'] = avg_entry_price * 0.985  # 1.5% profit
                position['tp3_price'] = avg_entry_price * 0.975  # 2.5% profit

            logger.info(f"Added to {side.upper()} position for {symbol}: {quantity:.8f} at ${entry_price:.2f}")
            logger.info(f"New average entry price: ${avg_entry_price:.2f}, total quantity: {total_quantity:.8f}")
            logger.info(f"Updated TP1: ${position['tp1_price']:.2f}, TP2: ${position['tp2_price']:.2f}, TP3: ${position['tp3_price']:.2f}")

            return position
        except Exception as e:
            logger.error(f"Error adding to position: {e}")
            return None

    def update_position(self, symbol, current_price, market_data):
        """
        Update position with current price and check for stop loss/take profit

        Args:
            symbol (str): Trading pair symbol
            current_price (float): Current price
            market_data (dict): Market data

        Returns:
            dict: Action to take ('none', 'close', 'partial_close')
        """
        try:
            if symbol not in self.positions:
                logger.warning(f"Position for {symbol} not found")
                return {'action': 'none'}

            position = self.positions[symbol]
            side = position['side']
            entry_price = position['entry_price']
            stop_loss_price = position['current_stop_loss_price']
            tp1_price = position['tp1_price']
            tp2_price = position['tp2_price']
            tp3_price = position['tp3_price']
            partial_exit = position['partial_exit']
            breakeven_triggered = position['breakeven_triggered']
            trailing_stop_triggered = position['trailing_stop_triggered']

            # Calculate profit/loss
            if side == 'buy':
                pnl_percent = (current_price - entry_price) / entry_price * 100

                # Check for stop loss
                if current_price <= stop_loss_price:
                    logger.info(f"Stop loss triggered for {symbol} at ${current_price:.2f} (entry: ${entry_price:.2f}, stop: ${stop_loss_price:.2f})")
                    return {'action': 'close', 'reason': 'stop_loss'}

                # Check for breakeven
                if not breakeven_triggered and current_price >= tp1_price:
                    # Move stop loss to entry price
                    position['current_stop_loss_price'] = entry_price
                    position['breakeven_triggered'] = True
                    logger.info(f"Breakeven triggered for {symbol} at ${current_price:.2f}, stop loss moved to entry price ${entry_price:.2f}")

                # Check for trailing stop - enable earlier as requested
                if not trailing_stop_triggered and current_price >= entry_price * 1.003:  # 0.3% above entry (reduced from 0.5%)
                    # Enable trailing stop
                    position['trailing_stop_triggered'] = True
                    logger.info(f"Trailing stop enabled for {symbol} at ${current_price:.2f}")

                # Update trailing stop if enabled - more aggressive trailing as requested
                if trailing_stop_triggered:
                    # Calculate new stop loss (0.2% below highest price since trailing stop was enabled)
                    new_stop_loss = current_price * 0.998  # 0.2% below current price (reduced from 0.25%)
                    if new_stop_loss > position['current_stop_loss_price']:
                        position['current_stop_loss_price'] = new_stop_loss
                        logger.info(f"Trailing stop updated for {symbol} to ${new_stop_loss:.2f}")

                # Check for partial exit at TP1
                if not partial_exit and current_price >= tp1_price:
                    logger.info(f"Take profit 1 reached for {symbol} at ${current_price:.2f}, executing partial exit")
                    position['partial_exit'] = True
                    return {'action': 'partial_close', 'reason': 'tp1', 'percentage': 50}

                # Check for full exit at TP2
                if partial_exit and current_price >= tp2_price:
                    logger.info(f"Take profit 2 reached for {symbol} at ${current_price:.2f}, executing full exit")
                    return {'action': 'close', 'reason': 'tp2'}

                # Check for negative trend (RSI or MACD)
                rsi_values = market_data.get('rsi_values', [])
                macd_line = market_data.get('macd_line', [])
                signal_line = market_data.get('signal_line', [])

                if len(rsi_values) > 0 and rsi_values[-1] > 70:
                    logger.info(f"RSI overbought for {symbol} at {rsi_values[-1]:.2f}, executing full exit")
                    return {'action': 'close', 'reason': 'rsi_overbought'}

                if len(macd_line) > 1 and len(signal_line) > 1:
                    if macd_line[-2] > signal_line[-2] and macd_line[-1] < signal_line[-1]:
                        logger.info(f"MACD bearish crossover for {symbol}, executing full exit")
                        return {'action': 'close', 'reason': 'macd_bearish'}

            else:  # sell
                pnl_percent = (entry_price - current_price) / entry_price * 100

                # Check for stop loss
                if current_price >= stop_loss_price:
                    logger.info(f"Stop loss triggered for {symbol} at ${current_price:.2f} (entry: ${entry_price:.2f}, stop: ${stop_loss_price:.2f})")
                    return {'action': 'close', 'reason': 'stop_loss'}

                # Check for breakeven
                if not breakeven_triggered and current_price <= tp1_price:
                    # Move stop loss to entry price
                    position['current_stop_loss_price'] = entry_price
                    position['breakeven_triggered'] = True
                    logger.info(f"Breakeven triggered for {symbol} at ${current_price:.2f}, stop loss moved to entry price ${entry_price:.2f}")

                # Check for trailing stop - enable earlier as requested
                if not trailing_stop_triggered and current_price <= entry_price * 0.997:  # 0.3% below entry (reduced from 0.5%)
                    # Enable trailing stop
                    position['trailing_stop_triggered'] = True
                    logger.info(f"Trailing stop enabled for {symbol} at ${current_price:.2f}")

                # Update trailing stop if enabled - more aggressive trailing as requested
                if trailing_stop_triggered:
                    # Calculate new stop loss (0.2% above lowest price since trailing stop was enabled)
                    new_stop_loss = current_price * 1.002  # 0.2% above current price (reduced from 0.25%)
                    if new_stop_loss < position['current_stop_loss_price']:
                        position['current_stop_loss_price'] = new_stop_loss
                        logger.info(f"Trailing stop updated for {symbol} to ${new_stop_loss:.2f}")

                # Check for partial exit at TP1
                if not partial_exit and current_price <= tp1_price:
                    logger.info(f"Take profit 1 reached for {symbol} at ${current_price:.2f}, executing partial exit")
                    position['partial_exit'] = True
                    return {'action': 'partial_close', 'reason': 'tp1', 'percentage': 50}

                # Check for full exit at TP2
                if partial_exit and current_price <= tp2_price:
                    logger.info(f"Take profit 2 reached for {symbol} at ${current_price:.2f}, executing full exit")
                    return {'action': 'close', 'reason': 'tp2'}

                # Check for negative trend (RSI or MACD)
                rsi_values = market_data.get('rsi_values', [])
                macd_line = market_data.get('macd_line', [])
                signal_line = market_data.get('signal_line', [])

                if len(rsi_values) > 0 and rsi_values[-1] < 30:
                    logger.info(f"RSI oversold for {symbol} at {rsi_values[-1]:.2f}, executing full exit")
                    return {'action': 'close', 'reason': 'rsi_oversold'}

                if len(macd_line) > 1 and len(signal_line) > 1:
                    if macd_line[-2] < signal_line[-2] and macd_line[-1] > signal_line[-1]:
                        logger.info(f"MACD bullish crossover for {symbol}, executing full exit")
                        return {'action': 'close', 'reason': 'macd_bullish'}

            # Update position with current PnL
            position['current_price'] = current_price
            position['pnl_percent'] = pnl_percent

            logger.info(f"Updated {side.upper()} position for {symbol}: current price ${current_price:.2f}, PnL: {pnl_percent:.2f}%")

            return {'action': 'none'}
        except Exception as e:
            logger.error(f"Error updating position: {e}")
            return {'action': 'none'}

    def close_position(self, symbol, current_price, reason):
        """
        Close a position

        Args:
            symbol (str): Trading pair symbol
            current_price (float): Current price
            reason (str): Reason for closing position

        Returns:
            dict: Closed position information
        """
        try:
            if symbol not in self.positions:
                logger.warning(f"Position for {symbol} not found")
                return None

            position = self.positions[symbol]
            side = position['side']
            entry_price = position['entry_price']
            quantity = position['quantity']

            # Calculate profit/loss
            if side == 'buy':
                pnl_percent = (current_price - entry_price) / entry_price * 100
                pnl_amount = (current_price - entry_price) * quantity
            else:  # sell
                pnl_percent = (entry_price - current_price) / entry_price * 100
                pnl_amount = (entry_price - current_price) * quantity

            # Update position with exit information
            position['exit_price'] = current_price
            position['exit_time'] = datetime.datetime.now().isoformat()
            position['pnl_percent'] = pnl_percent
            position['pnl_amount'] = pnl_amount
            position['exit_reason'] = reason
            position['status'] = 'closed'

            # Add position to history
            self.position_history.append(position)

            # Remove position from open positions
            del self.positions[symbol]

            # Update consecutive losses counter
            if pnl_percent < 0:
                self.consecutive_losses += 1
                logger.warning(f"Consecutive losses: {self.consecutive_losses}")

                # Freeze trading if two consecutive losses
                if self.consecutive_losses >= 2:
                    self.freeze_trading_consecutive_losses()
                    self.consecutive_losses = 0  # Reset counter
            else:
                self.consecutive_losses = 0  # Reset counter

                # Apply compounding if profitable
                if pnl_percent > 0:
                    # Retain 50% of profit and increase lot by 10% on next trade
                    profit_increase = 1 + (pnl_percent * 0.5 * 0.01)  # 50% of profit percentage
                    self.current_trade_amount = self.base_trade_amount * profit_increase
                    logger.info(f"Applied compounding: next trade amount increased to {self.current_trade_amount:.2f} USDT")

            # Save position history
            self._save_position_history()

            logger.info(f"Closed {side.upper()} position for {symbol}: {quantity:.8f} at ${current_price:.2f}, PnL: {pnl_percent:.2f}% (${pnl_amount:.2f}), reason: {reason}")

            return position
        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return None

    def partial_close_position(self, symbol, current_price, percentage, reason):
        """
        Partially close a position

        Args:
            symbol (str): Trading pair symbol
            current_price (float): Current price
            percentage (float): Percentage to close (0-100)
            reason (str): Reason for partially closing position

        Returns:
            dict: Partially closed position information
        """
        try:
            if symbol not in self.positions:
                logger.warning(f"Position for {symbol} not found")
                return None

            position = self.positions[symbol]
            side = position['side']
            entry_price = position['entry_price']
            quantity = position['quantity']

            # Calculate quantity to close
            close_quantity = quantity * (percentage / 100)
            remaining_quantity = quantity - close_quantity

            # Calculate profit/loss
            if side == 'buy':
                pnl_percent = (current_price - entry_price) / entry_price * 100
                pnl_amount = (current_price - entry_price) * close_quantity
            else:  # sell
                pnl_percent = (entry_price - current_price) / entry_price * 100
                pnl_amount = (entry_price - current_price) * close_quantity

            # Create partial close record
            partial_close = {
                'symbol': symbol,
                'side': side,
                'entry_price': entry_price,
                'exit_price': current_price,
                'quantity': close_quantity,
                'entry_time': position['entry_time'],
                'exit_time': datetime.datetime.now().isoformat(),
                'pnl_percent': pnl_percent,
                'pnl_amount': pnl_amount,
                'exit_reason': reason,
                'status': 'partially_closed',
                'percentage': percentage
            }

            # Add partial close to history
            self.position_history.append(partial_close)

            # Update position with remaining quantity
            position['quantity'] = remaining_quantity
            position['amount'] = remaining_quantity * entry_price

            # Move stop loss to entry price for remaining position
            position['current_stop_loss_price'] = entry_price

            # Save position history
            self._save_position_history()

            logger.info(f"Partially closed {side.upper()} position for {symbol}: {close_quantity:.8f} at ${current_price:.2f}, PnL: {pnl_percent:.2f}% (${pnl_amount:.2f}), reason: {reason}")
            logger.info(f"Remaining position: {remaining_quantity:.8f}, stop loss moved to entry price ${entry_price:.2f}")

            return partial_close
        except Exception as e:
            logger.error(f"Error partially closing position: {e}")
            return None

    def get_open_positions(self):
        """
        Get all open positions

        Returns:
            dict: Open positions
        """
        return self.positions

    def get_position_history(self):
        """
        Get position history

        Returns:
            list: Position history
        """
        return self.position_history

    def get_position_count(self):
        """
        Get number of open positions

        Returns:
            int: Number of open positions
        """
        return len(self.positions)

    def has_position(self, symbol):
        """
        Check if position exists for symbol

        Args:
            symbol (str): Trading pair symbol

        Returns:
            bool: True if position exists, False otherwise
        """
        return symbol in self.positions
