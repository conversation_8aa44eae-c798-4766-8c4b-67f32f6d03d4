import logging
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib
import os
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class MLMarketDetector:
    """
    Machine Learning-based Market Regime Detection System
    
    Classifies market conditions into:
    - Stable: Low volatility, predictable movements (ATR < 1.2%, Volume Change < 15%)
    - Volatile: High volatility, rapid price changes (ATR > 2.0%, Volume Change > 30%)
    - Unclear: Mixed signals, sideways movement (doesn't meet either threshold)
    """
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
        self.model_path = "models/market_detector.pkl"
        self.scaler_path = "models/market_scaler.pkl"
        self.training_data_path = "data/market_training_data.csv"
        
        # Enhanced market regime thresholds
        self.stable_atr_threshold = 0.012  # 1.2% ATR
        self.volatile_atr_threshold = 0.020  # 2.0% ATR
        self.stable_volume_threshold = 0.15  # 15% volume change
        self.volatile_volume_threshold = 0.30  # 30% volume change
        
        # Additional thresholds for enhanced classification
        self.stable_rsi_range = (40, 60)  # RSI range for stable markets
        self.volatile_rsi_extremes = (20, 80)  # RSI extremes for volatile markets
        self.stable_bb_position = (0.2, 0.8)  # Bollinger Band position for stable markets
        
        # Model parameters
        self.n_estimators = 100
        self.max_depth = 10
        self.min_samples_split = 5
        self.min_samples_leaf = 2
        
        # Load existing model if available
        self._load_model()
    
    def _load_model(self):
        """Load pre-trained model and scaler"""
        try:
            if os.path.exists(self.model_path) and os.path.exists(self.scaler_path):
                self.model = joblib.load(self.model_path)
                self.scaler = joblib.load(self.scaler_path)
                
                # Load feature names
                feature_names_path = "models/feature_names.json"
                if os.path.exists(feature_names_path):
                    with open(feature_names_path, 'r') as f:
                        self.feature_names = json.load(f)
                
                logger.info("Loaded existing ML market detection model with enhanced features")
            else:
                logger.info("No existing model found, will train new model")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            self.model = None

    def extract_features(self, market_data):
        """
        Extract 16 advanced market features for classification
        
        Args:
            market_data (dict): Market data containing OHLCV and indicators
            
        Returns:
            dict: Extracted features
        """
        try:
            features = {}
            
            # Price-based features
            features['price_change_1h'] = market_data.get('price_change_1h', 0)
            features['price_change_4h'] = market_data.get('price_change_4h', 0)
            features['price_change_24h'] = market_data.get('price_change_24h', 0)
            
            # Volatility features
            features['atr'] = market_data.get('atr', 0)
            features['atr_ratio'] = market_data.get('atr_ratio', 1.0)
            features['price_volatility'] = market_data.get('price_volatility', 0)
            
            # Volume features
            features['volume_ratio'] = market_data.get('volume_ratio', 1.0)
            features['volume_change'] = market_data.get('volume_change', 0)
            features['volume_volatility'] = market_data.get('volume_volatility', 0)
            
            # Technical indicator features
            features['rsi'] = market_data.get('rsi', 50)
            features['rsi_change'] = market_data.get('rsi_change', 0)
            features['bb_position'] = market_data.get('bb_position', 0.5)
            features['bb_width'] = market_data.get('bb_width', 0)
            
            # Trend features
            features['ema_trend'] = market_data.get('ema_trend', 0)
            features['macd_signal'] = market_data.get('macd_signal', 0)
            features['trend_strength'] = market_data.get('trend_strength', 0)
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return {}

    def classify_market_regime(self, market_data):
        """
        Classify market regime using rule-based and ML approaches
        
        Args:
            market_data (dict): Market data containing indicators
            
        Returns:
            dict: Market regime classification with confidence
        """
        try:
            # Extract features
            features = self.extract_features(market_data)
            
            if not features:
                return self._get_fallback_classification(market_data)
            
            # Rule-based classification (fallback)
            rule_based_regime = self._rule_based_classification(market_data)
            
            # ML-based classification (if model is available)
            ml_regime = None
            ml_confidence = 0.5
            
            if self.model is not None and self.feature_names:
                try:
                    # Prepare feature vector
                    feature_vector = []
                    for feature_name in self.feature_names:
                        feature_vector.append(features.get(feature_name, 0))
                    
                    # Scale features
                    feature_vector = np.array(feature_vector).reshape(1, -1)
                    feature_vector_scaled = self.scaler.transform(feature_vector)
                    
                    # Predict
                    prediction = self.model.predict(feature_vector_scaled)[0]
                    prediction_proba = self.model.predict_proba(feature_vector_scaled)[0]
                    
                    ml_regime = prediction
                    ml_confidence = max(prediction_proba)
                    
                except Exception as e:
                    logger.warning(f"ML prediction failed, using rule-based: {e}")
                    ml_regime = rule_based_regime['regime']
                    ml_confidence = rule_based_regime['confidence']
            
            # Combine rule-based and ML predictions
            if ml_regime is not None:
                # Use ML prediction if confidence is high
                if ml_confidence > 0.7:
                    final_regime = ml_regime
                    final_confidence = ml_confidence
                    method = "ml_high_confidence"
                # Use rule-based if ML confidence is low
                elif rule_based_regime['confidence'] > 0.6:
                    final_regime = rule_based_regime['regime']
                    final_confidence = rule_based_regime['confidence']
                    method = "rule_based_fallback"
                # Average both predictions
                else:
                    if ml_regime == rule_based_regime['regime']:
                        final_regime = ml_regime
                        final_confidence = (ml_confidence + rule_based_regime['confidence']) / 2
                        method = "consensus"
                    else:
                        # Conflict - use higher confidence
                        if ml_confidence > rule_based_regime['confidence']:
                            final_regime = ml_regime
                            final_confidence = ml_confidence
                            method = "ml_conflict_resolution"
                        else:
                            final_regime = rule_based_regime['regime']
                            final_confidence = rule_based_regime['confidence']
                            method = "rule_conflict_resolution"
            else:
                final_regime = rule_based_regime['regime']
                final_confidence = rule_based_regime['confidence']
                method = "rule_based_only"
            
            # Generate risk adjustment recommendations
            risk_adjustments = self._generate_risk_adjustments(final_regime, final_confidence)
            
            result = {
                'regime': final_regime,
                'confidence': final_confidence,
                'method': method,
                'rule_based': rule_based_regime,
                'ml_prediction': {'regime': ml_regime, 'confidence': ml_confidence} if ml_regime else None,
                'risk_adjustments': risk_adjustments,
                'features': features,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Market regime classified as {final_regime} with {final_confidence:.2f} confidence using {method}")
            return result
            
        except Exception as e:
            logger.error(f"Error classifying market regime: {e}")
            return self._get_fallback_classification(market_data)

    def _rule_based_classification(self, market_data):
        """
        Rule-based market regime classification
        
        Args:
            market_data (dict): Market data
            
        Returns:
            dict: Classification result
        """
        try:
            atr = market_data.get('atr', 0)
            volume_change = abs(market_data.get('volume_change', 0))
            rsi = market_data.get('rsi', 50)
            bb_position = market_data.get('bb_position', 0.5)
            
            # Stable market conditions
            stable_score = 0
            if atr < self.stable_atr_threshold:
                stable_score += 0.3
            if volume_change < self.stable_volume_threshold:
                stable_score += 0.2
            if self.stable_rsi_range[0] <= rsi <= self.stable_rsi_range[1]:
                stable_score += 0.2
            if self.stable_bb_position[0] <= bb_position <= self.stable_bb_position[1]:
                stable_score += 0.2
            if market_data.get('trend_strength', 0) > 0.5:
                stable_score += 0.1
            
            # Volatile market conditions
            volatile_score = 0
            if atr > self.volatile_atr_threshold:
                volatile_score += 0.4
            if volume_change > self.volatile_volume_threshold:
                volatile_score += 0.3
            if rsi < self.volatile_rsi_extremes[0] or rsi > self.volatile_rsi_extremes[1]:
                volatile_score += 0.2
            if bb_position < 0.1 or bb_position > 0.9:
                volatile_score += 0.1
            
            # Determine regime
            if stable_score >= 0.6:
                regime = 'stable'
                confidence = min(0.9, stable_score)
            elif volatile_score >= 0.5:
                regime = 'volatile'
                confidence = min(0.9, volatile_score)
            else:
                regime = 'unclear'
                confidence = 0.5
            
            return {
                'regime': regime,
                'confidence': confidence,
                'stable_score': stable_score,
                'volatile_score': volatile_score
            }
            
        except Exception as e:
            logger.error(f"Error in rule-based classification: {e}")
            return {'regime': 'unclear', 'confidence': 0.5}

    def _generate_risk_adjustments(self, regime, confidence):
        """
        Generate risk adjustment recommendations based on market regime
        
        Args:
            regime (str): Market regime
            confidence (float): Classification confidence
            
        Returns:
            dict: Risk adjustment recommendations
        """
        try:
            adjustments = {
                'position_size_multiplier': 1.0,
                'stop_loss_multiplier': 1.0,
                'take_profit_multiplier': 1.0,
                'max_positions': 3,
                'confidence_threshold': 0.55
            }
            
            if regime == 'stable':
                # Stable markets - normal to slightly increased risk
                adjustments['position_size_multiplier'] = 1.0 if confidence < 0.8 else 1.1
                adjustments['stop_loss_multiplier'] = 1.0
                adjustments['take_profit_multiplier'] = 1.0
                adjustments['max_positions'] = 3
                adjustments['confidence_threshold'] = 0.55
                
            elif regime == 'volatile':
                # Volatile markets - reduced risk
                adjustments['position_size_multiplier'] = 0.5 if confidence > 0.7 else 0.7
                adjustments['stop_loss_multiplier'] = 0.8  # Tighter stops
                adjustments['take_profit_multiplier'] = 1.2  # Higher targets
                adjustments['max_positions'] = 2
                adjustments['confidence_threshold'] = 0.65
                
            else:  # unclear
                # Unclear markets - conservative approach
                adjustments['position_size_multiplier'] = 0.7
                adjustments['stop_loss_multiplier'] = 0.9
                adjustments['take_profit_multiplier'] = 1.1
                adjustments['max_positions'] = 2
                adjustments['confidence_threshold'] = 0.60
            
            return adjustments
            
        except Exception as e:
            logger.error(f"Error generating risk adjustments: {e}")
            return {
                'position_size_multiplier': 0.7,
                'stop_loss_multiplier': 1.0,
                'take_profit_multiplier': 1.0,
                'max_positions': 2,
                'confidence_threshold': 0.60
            }

    def _get_fallback_classification(self, market_data):
        """
        Fallback classification when other methods fail

        Args:
            market_data (dict): Market data

        Returns:
            dict: Fallback classification
        """
        return {
            'regime': 'unclear',
            'confidence': 0.5,
            'method': 'fallback',
            'rule_based': {'regime': 'unclear', 'confidence': 0.5},
            'ml_prediction': None,
            'risk_adjustments': self._generate_risk_adjustments('unclear', 0.5),
            'features': {},
            'timestamp': datetime.now().isoformat()
        }

    def train_model(self, training_data=None):
        """
        Train the ML model with historical data

        Args:
            training_data (DataFrame): Training data with features and labels

        Returns:
            dict: Training results
        """
        try:
            if training_data is None:
                # Generate synthetic training data if none provided
                training_data = self._generate_synthetic_training_data()

            if training_data.empty:
                logger.warning("No training data available")
                return {'success': False, 'error': 'No training data'}

            # Prepare features and labels
            feature_columns = [col for col in training_data.columns if col != 'regime']
            X = training_data[feature_columns]
            y = training_data['regime']

            # Store feature names
            self.feature_names = feature_columns.tolist()

            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )

            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)

            # Train model
            self.model = RandomForestClassifier(
                n_estimators=self.n_estimators,
                max_depth=self.max_depth,
                min_samples_split=self.min_samples_split,
                min_samples_leaf=self.min_samples_leaf,
                random_state=42,
                class_weight='balanced'
            )

            self.model.fit(X_train_scaled, y_train)

            # Evaluate model
            y_pred = self.model.predict(X_test_scaled)
            accuracy = accuracy_score(y_test, y_pred)

            # Save model
            self._save_model()

            logger.info(f"Model trained successfully with accuracy: {accuracy:.3f}")

            return {
                'success': True,
                'accuracy': accuracy,
                'feature_count': len(feature_columns),
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'classification_report': classification_report(y_test, y_pred, output_dict=True)
            }

        except Exception as e:
            logger.error(f"Error training model: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_synthetic_training_data(self, n_samples=1000):
        """
        Generate synthetic training data for initial model training

        Args:
            n_samples (int): Number of samples to generate

        Returns:
            DataFrame: Synthetic training data
        """
        try:
            np.random.seed(42)
            data = []

            for _ in range(n_samples):
                # Generate features based on regime
                regime = np.random.choice(['stable', 'volatile', 'unclear'], p=[0.4, 0.3, 0.3])

                if regime == 'stable':
                    # Stable market characteristics
                    atr = np.random.normal(0.008, 0.002)  # Low volatility
                    volume_change = np.random.normal(0.05, 0.05)  # Low volume change
                    rsi = np.random.normal(50, 10)  # Neutral RSI
                    bb_position = np.random.normal(0.5, 0.15)  # Middle BB position

                elif regime == 'volatile':
                    # Volatile market characteristics
                    atr = np.random.normal(0.025, 0.008)  # High volatility
                    volume_change = np.random.normal(0.4, 0.2)  # High volume change
                    rsi = np.random.choice([np.random.normal(25, 5), np.random.normal(75, 5)])  # Extreme RSI
                    bb_position = np.random.choice([np.random.normal(0.1, 0.05), np.random.normal(0.9, 0.05)])  # Extreme BB

                else:  # unclear
                    # Unclear market characteristics
                    atr = np.random.normal(0.015, 0.005)  # Medium volatility
                    volume_change = np.random.normal(0.2, 0.1)  # Medium volume change
                    rsi = np.random.normal(50, 15)  # Wide RSI range
                    bb_position = np.random.normal(0.5, 0.25)  # Wide BB range

                # Generate other features
                sample = {
                    'price_change_1h': np.random.normal(0, atr/4),
                    'price_change_4h': np.random.normal(0, atr),
                    'price_change_24h': np.random.normal(0, atr*2),
                    'atr': max(0.001, atr),
                    'atr_ratio': np.random.normal(1.0, 0.3),
                    'price_volatility': max(0.001, atr * np.random.normal(1.0, 0.2)),
                    'volume_ratio': np.random.normal(1.0, 0.5),
                    'volume_change': volume_change,
                    'volume_volatility': abs(np.random.normal(0.2, 0.1)),
                    'rsi': max(0, min(100, rsi)),
                    'rsi_change': np.random.normal(0, 5),
                    'bb_position': max(0, min(1, bb_position)),
                    'bb_width': max(0.001, atr * np.random.normal(2.0, 0.5)),
                    'ema_trend': np.random.normal(0, 0.01),
                    'macd_signal': np.random.normal(0, 0.001),
                    'trend_strength': np.random.uniform(0, 1),
                    'regime': regime
                }

                data.append(sample)

            df = pd.DataFrame(data)
            logger.info(f"Generated {len(df)} synthetic training samples")
            return df

        except Exception as e:
            logger.error(f"Error generating synthetic data: {e}")
            return pd.DataFrame()

    def _save_model(self):
        """Save trained model and scaler"""
        try:
            os.makedirs("models", exist_ok=True)

            # Save model and scaler
            joblib.dump(self.model, self.model_path)
            joblib.dump(self.scaler, self.scaler_path)

            # Save feature names
            feature_names_path = "models/feature_names.json"
            with open(feature_names_path, 'w') as f:
                json.dump(self.feature_names, f)

            logger.info("Model, scaler, and feature names saved successfully")

        except Exception as e:
            logger.error(f"Error saving model: {e}")

    def generate_daily_market_regime_report(self, classifications_history):
        """
        Generate daily market regime report

        Args:
            classifications_history (list): List of classification results from the day

        Returns:
            dict: Daily report
        """
        try:
            if not classifications_history:
                return {'error': 'No classification data available'}

            # Analyze regime distribution
            regimes = [c.get('regime', 'unclear') for c in classifications_history]
            regime_counts = {
                'stable': regimes.count('stable'),
                'volatile': regimes.count('volatile'),
                'unclear': regimes.count('unclear')
            }

            total_classifications = len(regimes)
            regime_percentages = {
                regime: (count / total_classifications) * 100
                for regime, count in regime_counts.items()
            }

            # Calculate average confidence
            confidences = [c.get('confidence', 0.5) for c in classifications_history]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.5

            # Analyze method usage
            methods = [c.get('method', 'unknown') for c in classifications_history]
            method_counts = {}
            for method in methods:
                method_counts[method] = method_counts.get(method, 0) + 1

            # Risk adjustment analysis
            risk_adjustments = [c.get('risk_adjustments', {}) for c in classifications_history]
            avg_position_multiplier = np.mean([
                ra.get('position_size_multiplier', 1.0) for ra in risk_adjustments
            ])

            # Generate recommendations
            dominant_regime = max(regime_counts, key=regime_counts.get)
            recommendations = self._generate_daily_recommendations(
                dominant_regime, avg_confidence, regime_percentages
            )

            report = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'total_classifications': total_classifications,
                'regime_distribution': regime_counts,
                'regime_percentages': regime_percentages,
                'dominant_regime': dominant_regime,
                'average_confidence': avg_confidence,
                'method_usage': method_counts,
                'average_position_multiplier': avg_position_multiplier,
                'recommendations': recommendations,
                'timestamp': datetime.now().isoformat()
            }

            # Save report
            self._save_daily_report(report)

            logger.info(f"Generated daily market regime report: {dominant_regime} dominant ({regime_percentages[dominant_regime]:.1f}%)")
            return report

        except Exception as e:
            logger.error(f"Error generating daily report: {e}")
            return {'error': str(e)}

    def _generate_daily_recommendations(self, dominant_regime, avg_confidence, regime_percentages):
        """
        Generate trading recommendations based on daily analysis

        Args:
            dominant_regime (str): Most frequent regime
            avg_confidence (float): Average classification confidence
            regime_percentages (dict): Regime distribution percentages

        Returns:
            dict: Trading recommendations
        """
        recommendations = {
            'trading_approach': 'conservative',
            'position_sizing': 'normal',
            'risk_management': 'standard',
            'strategy_focus': 'balanced'
        }

        if dominant_regime == 'stable' and regime_percentages['stable'] > 60:
            recommendations.update({
                'trading_approach': 'trend_following',
                'position_sizing': 'normal_to_aggressive',
                'risk_management': 'standard',
                'strategy_focus': 'momentum_and_breakouts'
            })
        elif dominant_regime == 'volatile' and regime_percentages['volatile'] > 50:
            recommendations.update({
                'trading_approach': 'scalping',
                'position_sizing': 'reduced',
                'risk_management': 'tight_stops',
                'strategy_focus': 'quick_profits'
            })
        elif regime_percentages['unclear'] > 50:
            recommendations.update({
                'trading_approach': 'range_trading',
                'position_sizing': 'conservative',
                'risk_management': 'wide_stops',
                'strategy_focus': 'support_resistance'
            })

        # Adjust based on confidence
        if avg_confidence < 0.6:
            recommendations['position_sizing'] = 'reduced'
            recommendations['risk_management'] = 'conservative'

        return recommendations

    def _save_daily_report(self, report):
        """Save daily report to file"""
        try:
            os.makedirs("reports", exist_ok=True)

            report_file = f"reports/market_regime_report_{report['date']}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)

            logger.info(f"Daily market regime report saved to {report_file}")

        except Exception as e:
            logger.error(f"Error saving daily report: {e}")
