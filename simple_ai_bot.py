"""
Simple AI Trading Bot
A production-ready AI trading bot that works with multiple AI services
"""

import os
import sys
import time
import json
import logging
import datetime
import random
import argparse
import traceback
import signal
from dotenv import load_dotenv
from ai_services.market_analyzer import AIMarketAnalyzer
from exchange.binance_api import BinanceAPI
from exchange.isolated_margin_manager import IsolatedMarginManager
from exchange.margin_monitor import MarginMonitor
from exchange.position_sizer import AdvancedPositionSizer
from data.historical_data_loader import Historical<PERSON>ata<PERSON>oader
from core.memory_guard import MemoryGuard
from core.state_tracker import StateTracker
from security.key_manager import KeyManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/simple_ai_bot.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("simple_ai_bot")

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Use the real BinanceAPI for production trading
# The simulated version is kept as a comment for reference

# class SimpleBinanceAPI:
#     """
#     Simplified Binance API client for testing
#     """
#     ...

# Real BinanceAPI is imported from exchange.binance_api

class SimpleAIConnector:
    """
    Simplified AI connector for testing
    """

    def __init__(self, service_name):
        self.service_name = service_name
        logger.info(f"SimpleAIConnector initialized for {service_name}")

    def analyze_market(self, data):
        """
        Simulate AI market analysis
        """
        # Generate random sentiment
        sentiment = random.choice(["bullish", "bearish", "neutral"])

        # Generate random support and resistance levels
        current_price = data.get("price", 60000)
        support_levels = [current_price * (1 - random.uniform(0.05, 0.2)) for _ in range(2)]
        resistance_levels = [current_price * (1 + random.uniform(0.05, 0.2)) for _ in range(2)]

        # Generate random recommendation
        if sentiment == "bullish":
            recommendation = random.choices(["buy", "hold"], weights=[0.7, 0.3])[0]
        elif sentiment == "bearish":
            recommendation = random.choices(["sell", "hold"], weights=[0.7, 0.3])[0]
        else:
            recommendation = "hold"

        # Generate random confidence
        confidence = random.randint(50, 95)

        # Generate explanation
        explanations = {
            "bullish": [
                "Price is showing strong upward momentum with increasing volume.",
                "Recent price action has broken through key resistance levels.",
                "Market sentiment is positive with strong buying pressure."
            ],
            "bearish": [
                "Price is showing downward momentum with increasing selling volume.",
                "Recent price action has broken below key support levels.",
                "Market sentiment is negative with strong selling pressure."
            ],
            "neutral": [
                "Price is consolidating within a range with no clear direction.",
                "Volume is decreasing, indicating lack of conviction from buyers or sellers.",
                "Market is waiting for a catalyst to determine the next move."
            ]
        }

        explanation = random.choice(explanations[sentiment])

        # Create analysis result
        analysis = {
            "sentiment": sentiment,
            "support_levels": support_levels,
            "resistance_levels": resistance_levels,
            "recommendation": recommendation,
            "confidence": confidence,
            "explanation": explanation
        }

        logger.info(f"{self.service_name} analysis: {recommendation} with {confidence}% confidence")

        return {
            "service": self.service_name,
            "analysis": analysis,
            "timestamp": datetime.datetime.now().isoformat()
        }

class SimpleSignalAggregator:
    """
    Simplified signal aggregator for testing
    """

    def __init__(self):
        # Initialize AI connectors
        self.openai_connector = SimpleAIConnector("openai")
        self.deepseek_connector = SimpleAIConnector("deepseek")
        self.qwen_connector = SimpleAIConnector("qwen")

        # Initialize model evaluator
        from ai_services.model_evaluator import AIModelEvaluator
        self.model_evaluator = AIModelEvaluator(base_weights={
            "openai": 0.30,
            "deepseek": 0.35,
            "qwen": 0.35
        })

        # Get weights from model evaluator
        self.weights = self.model_evaluator.get_current_weights()

        # Configure confidence thresholds
        self.confidence_threshold = 0.7  # 70% confidence required for a signal
        self.min_sources = 2  # At least 2 sources must agree

        logger.info("SimpleSignalAggregator initialized")

    def analyze_market(self, market_data):
        """
        Analyze market data using multiple AI services

        Args:
            market_data (dict): Market data to analyze

        Returns:
            dict: Aggregated signal
        """
        logger.info("Starting market analysis with multiple AI services")

        # Collect signals from all sources
        signals = {}

        # Get OpenAI analysis
        openai_result = self.openai_connector.analyze_market(market_data)
        signals["openai"] = openai_result

        # Get DeepSeek analysis
        deepseek_result = self.deepseek_connector.analyze_market(market_data)
        signals["deepseek"] = deepseek_result

        # Get Qwen analysis
        qwen_result = self.qwen_connector.analyze_market(market_data)
        signals["qwen"] = qwen_result

        # Aggregate signals
        aggregated_signal = self._aggregate_signals(signals, market_data)

        return aggregated_signal

    def _aggregate_signals(self, signals, market_data):
        """
        Aggregate signals from multiple sources

        Args:
            signals (dict): Signals from multiple sources
            market_data (dict): Market data

        Returns:
            dict: Aggregated signal
        """
        # Check if models disagree
        recommendations_set = set()
        for source in signals:
            if "analysis" in signals[source]:
                recommendation = signals[source]["analysis"].get("recommendation", "hold").lower()
                recommendations_set.add(recommendation)

        # If models disagree, use debate mode weights
        if len(recommendations_set) > 1 and len(signals) > 1:
            logger.info("Models disagree. Using debate mode weights.")
            debate_weights = self.model_evaluator.get_debate_weights(signals)
            self.weights = debate_weights
        else:
            # Use current weights from model evaluator
            self.weights = self.model_evaluator.get_current_weights()

        # Count recommendations
        recommendations = {"buy": 0, "sell": 0, "hold": 0}
        confidence_sum = 0
        source_count = 0

        # Process AI signals
        for source in ["openai", "deepseek", "qwen"]:
            if source in signals and "analysis" in signals[source]:
                analysis = signals[source]["analysis"]
                recommendation = analysis.get("recommendation", "hold").lower()
                confidence = analysis.get("confidence", 50) / 100.0  # Convert to 0-1 scale

                # Add weighted recommendation
                recommendations[recommendation] += self.weights[source] * confidence
                confidence_sum += self.weights[source] * confidence
                source_count += 1

                # Record prediction for evaluation
                self.model_evaluator.record_prediction(
                    source,
                    recommendation,
                    confidence
                )

        # Normalize recommendations
        if confidence_sum > 0:
            for rec in recommendations:
                recommendations[rec] /= confidence_sum

        # Determine final recommendation
        final_recommendation = max(recommendations, key=recommendations.get)
        final_confidence = recommendations[final_recommendation]

        # Check if confidence threshold is met
        if final_confidence < self.confidence_threshold or source_count < self.min_sources:
            final_recommendation = "hold"
            reason = f"Insufficient confidence ({final_confidence:.2f}) or sources ({source_count})"
        else:
            reason = f"Aggregated from {source_count} sources with {final_confidence:.2f} confidence"

        # Verify predictions when we have a final recommendation
        if final_recommendation != "hold":
            self.model_evaluator.verify_latest_predictions(final_recommendation)

        # Create final signal
        symbol = market_data.get("symbol", "BTC/USDT")
        current_price = market_data.get("price", 0)

        aggregated_signal = {
            "symbol": symbol,
            "recommendation": final_recommendation,
            "confidence": final_confidence,
            "price": current_price,
            "timestamp": datetime.datetime.now().isoformat(),
            "reason": reason,
            "source_count": source_count,
            "recommendations": recommendations,
            "signals": signals
        }

        logger.info(f"Aggregated signal: {final_recommendation} with {final_confidence:.2f} confidence")
        return aggregated_signal

    def generate_discussion(self, signals):
        """
        Generate a discussion between AI models about the market

        Args:
            signals (dict): Signals from multiple sources

        Returns:
            str: Discussion text
        """
        # Generate discussion
        discussion = "AI Models Discussion:\n\n"

        # Add introduction
        discussion += "The following is a discussion between AI models about the current market conditions:\n\n"

        # Check if we need to enter debate mode (models disagree)
        recommendations = set()
        for source in signals:
            if "analysis" in signals[source]:
                recommendation = signals[source]["analysis"].get("recommendation", "hold").lower()
                recommendations.add(recommendation)

        # Get model weights for display
        weights = self.weights

        # Add each AI's analysis
        for source in ["openai", "deepseek", "qwen"]:
            if source in signals and "analysis" in signals[source]:
                analysis = signals[source]["analysis"]
                weight_pct = int(weights.get(source, 0) * 100)
                model_name = f"{source.upper()} (Weight: {weight_pct}%)"
                recommendation = analysis.get("recommendation", "hold").upper()
                confidence = analysis.get("confidence", 50)
                explanation = analysis.get("explanation", "No explanation provided.")

                discussion += f"{model_name}: I recommend {recommendation} with {confidence}% confidence. {explanation}\n\n"

        # Enhanced debate mode if models disagree
        if len(recommendations) > 1 and len(signals) > 1:
            discussion += "\n=== AI DEBATE MODE ACTIVATED ===\n"
            discussion += "Models disagree on the recommendation. Initiating detailed analysis debate.\n\n"

            # Get historical accuracy for each model
            performance_history = self.model_evaluator.get_performance_history()
            recent_days = sorted(performance_history.keys())[-5:] if performance_history else []

            if recent_days:
                discussion += "Recent accuracy of each model:\n"
                for source in ["openai", "deepseek", "qwen"]:
                    accuracies = []
                    for day in recent_days:
                        if day in performance_history and "accuracy" in performance_history[day]:
                            if source in performance_history[day]["accuracy"]:
                                accuracies.append(performance_history[day]["accuracy"][source])

                    if accuracies:
                        avg_accuracy = sum(accuracies) / len(accuracies)
                        discussion += f"- {source.upper()}: {avg_accuracy:.2%} accuracy over last {len(accuracies)} days\n"

                discussion += "\n"

            # Add detailed arguments from each model
            discussion += "Detailed arguments:\n\n"

            for source in ["openai", "deepseek", "qwen"]:
                if source in signals and "analysis" in signals[source]:
                    analysis = signals[source]["analysis"]
                    recommendation = analysis.get("recommendation", "hold").upper()
                    explanation = analysis.get("explanation", "No explanation provided.")

                    discussion += f"{source.upper()} argues for {recommendation}:\n"
                    discussion += f"- {explanation}\n"
                    discussion += f"- Based on {source.upper()}'s analysis of market conditions and technical indicators.\n\n"

            # Add debate conclusion with weighted decision
            discussion += "Debate conclusion:\n"

            # Calculate weighted recommendation
            weighted_rec = {"buy": 0, "sell": 0, "hold": 0}
            for source in ["openai", "deepseek", "qwen"]:
                if source in signals and "analysis" in signals[source]:
                    rec = signals[source]["analysis"].get("recommendation", "hold").lower()
                    conf = signals[source]["analysis"].get("confidence", 50) / 100.0
                    weight = weights.get(source, 0)
                    weighted_rec[rec] += weight * conf

            # Normalize and find highest
            total_weight = sum(weighted_rec.values())
            if total_weight > 0:
                for rec in weighted_rec:
                    weighted_rec[rec] /= total_weight

                final_rec = max(weighted_rec, key=weighted_rec.get)
                final_conf = weighted_rec[final_rec] * 100

                discussion += f"After weighing all arguments, the final recommendation is {final_rec.upper()} with {final_conf:.1f}% confidence.\n"
                discussion += f"This decision gives more weight to models with better historical performance.\n"
            else:
                discussion += "Unable to reach a conclusion due to insufficient data.\n"

            discussion += "\n=== END OF DEBATE ===\n"

        # Add conclusion
        discussion += "\nFinal Conclusion:\n"

        # Calculate weighted recommendation
        weighted_rec = {"buy": 0, "sell": 0, "hold": 0}
        for source in ["openai", "deepseek", "qwen"]:
            if source in signals and "analysis" in signals[source]:
                rec = signals[source]["analysis"].get("recommendation", "hold").lower()
                conf = signals[source]["analysis"].get("confidence", 50) / 100.0
                weight = weights.get(source, 0)
                weighted_rec[rec] += weight * conf

        # Normalize and find highest
        total_weight = sum(weighted_rec.values())
        if total_weight > 0:
            for rec in weighted_rec:
                weighted_rec[rec] /= total_weight

            final_rec = max(weighted_rec, key=weighted_rec.get)
            final_conf = weighted_rec[final_rec] * 100

            discussion += f"Based on weighted analysis, the final recommendation is {final_rec.upper()} with {final_conf:.1f}% confidence.\n"
        else:
            discussion += "No recommendations available.\n"

        return discussion

class SimpleResourceMonitor:
    """
    Simplified resource monitor for testing
    """

    def __init__(self):
        logger.info("SimpleResourceMonitor initialized")

    def check_system_health(self):
        """
        Simulate system health check
        """
        # Simulate healthy system most of the time
        is_healthy = random.random() < 0.95

        if is_healthy:
            logger.info("System health check passed")
        else:
            logger.warning("System health check failed")

        return is_healthy

    def check_api_health(self, api_client):
        """
        Simulate API health check
        """
        # Simulate healthy API most of the time
        is_healthy = random.random() < 0.95

        if is_healthy:
            logger.info("API health check passed")
        else:
            logger.warning("API health check failed")

        return is_healthy

class DynamicCapitalManager:
    """
    Dynamic Capital Manager for the trading bot
    """

    def __init__(self, exchange, min_balance=7.0, max_risk_pct=5.0):
        """
        Initialize the capital manager

        Args:
            exchange: Exchange API client
            min_balance (float): Minimum balance required to trade (in USDT)
            max_risk_pct (float): Maximum total risk percentage across all positions
        """
        self.exchange = exchange
        self.min_balance = min_balance
        self.max_risk_pct = max_risk_pct

        # Get risk percentages from environment variables if available
        self.normal_risk_pct = float(os.getenv("NORMAL_RISK_PCT", "2.0"))  # 2% risk in normal conditions
        self.volatile_risk_pct = float(os.getenv("VOLATILE_RISK_PCT", "1.0"))  # 1% risk in volatile conditions

        logger.info(f"DynamicCapitalManager initialized with min_balance={min_balance} USDT, max_risk_pct={max_risk_pct}%")
        logger.info(f"Risk percentages: normal={self.normal_risk_pct}%, volatile={self.volatile_risk_pct}%")

    def get_available_balance(self, quote_currency="USDT"):
        """
        Get available balance for trading

        Args:
            quote_currency (str): Quote currency (e.g., USDT)

        Returns:
            float: Available balance
        """
        try:
            # Force refresh to get the most up-to-date balance
            balance = self.exchange.get_balance(force_refresh=True)
            available = balance.get(quote_currency, {}).get("free", 0.0)
            total = balance.get(quote_currency, {}).get("total", 0.0)
            used = balance.get(quote_currency, {}).get("used", 0.0)

            logger.info(f"Available balance: {available} {quote_currency} (Total: {total}, Used: {used})")
            return available
        except Exception as e:
            logger.error(f"Error getting available balance: {e}")
            return 0.0

    def can_trade(self, quote_currency="USDT"):
        """
        Check if trading is allowed based on available balance

        Args:
            quote_currency (str): Quote currency (e.g., USDT)

        Returns:
            bool: True if trading is allowed, False otherwise
        """
        available_balance = self.get_available_balance(quote_currency)
        can_trade = available_balance >= self.min_balance

        if not can_trade:
            logger.warning(f"Trading not allowed: Available balance ({available_balance} {quote_currency}) is below minimum ({self.min_balance} {quote_currency})")

        return can_trade

    def get_risk_percentage(self, market_data):
        """
        Get risk percentage based on market conditions

        Args:
            market_data (dict): Market data including indicators

        Returns:
            float: Risk percentage
        """
        try:
            # Extract historical data
            historical = market_data.get("historical", [])

            if not historical:
                logger.warning("No historical data available for volatility check")
                return self.volatile_risk_pct  # Conservative approach

            # Extract high, low, close prices
            high_prices = [candle.get("high", 0) for candle in historical]
            low_prices = [candle.get("low", 0) for candle in historical]
            close_prices = [candle.get("close", 0) for candle in historical]

            # Calculate ATR values (simplified)
            atr_values = self._calculate_atr(high_prices, low_prices, close_prices)

            # Check if market is volatile
            volatile = self._is_market_volatile(atr_values)

            if volatile:
                logger.info("Market is volatile, using reduced risk percentage")
                return self.volatile_risk_pct
            else:
                logger.info("Market is normal, using standard risk percentage")
                return self.normal_risk_pct
        except Exception as e:
            logger.error(f"Error determining risk percentage: {e}")
            return self.volatile_risk_pct  # Conservative approach

    def _calculate_atr(self, high_prices, low_prices, close_prices, period=14):
        """
        Calculate Average True Range (simplified)

        Args:
            high_prices (list): List of high prices
            low_prices (list): List of low prices
            close_prices (list): List of close prices
            period (int): ATR period

        Returns:
            list: ATR values
        """
        try:
            if len(high_prices) != len(low_prices) or len(high_prices) != len(close_prices):
                logger.error("High, low, and close price lists must be of the same length")
                return []

            # Calculate true range
            tr_values = []
            for i in range(len(close_prices)):
                if i == 0:
                    # For the first candle, TR is simply High - Low
                    tr = high_prices[i] - low_prices[i]
                else:
                    # For subsequent candles, TR is the greatest of:
                    # 1. Current High - Current Low
                    # 2. |Current High - Previous Close|
                    # 3. |Current Low - Previous Close|
                    tr1 = high_prices[i] - low_prices[i]
                    tr2 = abs(high_prices[i] - close_prices[i-1])
                    tr3 = abs(low_prices[i] - close_prices[i-1])
                    tr = max(tr1, tr2, tr3)

                tr_values.append(tr)

            # Calculate ATR using simple moving average
            atr_values = []
            for i in range(len(tr_values)):
                if i < period - 1:
                    # Not enough data for ATR calculation
                    atr_values.append(None)
                else:
                    # Calculate ATR as average of TR values
                    atr = sum(tr_values[i-period+1:i+1]) / period
                    atr_values.append(atr)

            return atr_values
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return []

    def _is_market_volatile(self, atr_values, lookback=5, threshold=1.5):
        """
        Check if the market is volatile based on ATR

        Args:
            atr_values (list): List of ATR values
            lookback (int): Number of periods to look back
            threshold (float): Threshold multiplier for volatility

        Returns:
            bool: True if market is volatile, False otherwise
        """
        try:
            if len(atr_values) < lookback + 1:
                return False

            # Get recent ATR values
            recent_atr = [atr for atr in atr_values[-lookback-1:] if atr is not None]

            if len(recent_atr) < 2:
                return False

            # Calculate average ATR for the lookback period
            avg_atr = sum(recent_atr[:-1]) / len(recent_atr[:-1])

            # Check if current ATR is significantly higher than average
            current_atr = recent_atr[-1]

            return current_atr > avg_atr * threshold
        except Exception as e:
            logger.error(f"Error checking market volatility: {e}")
            return True  # Conservative approach: assume high volatility if error

    def calculate_position_size(self, entry_price, stop_loss_price, market_data, open_positions=None, quote_currency="USDT"):
        """
        Calculate position size based on risk management rules

        Args:
            entry_price (float): Entry price
            stop_loss_price (float): Stop loss price
            market_data (dict): Market data including indicators
            open_positions (list): List of open positions
            quote_currency (str): Quote currency (e.g., USDT)

        Returns:
            float: Position size in base currency
        """
        try:
            # Check if trading is allowed
            if not self.can_trade(quote_currency):
                return 0.0

            # Get available balance
            available_balance = self.get_available_balance(quote_currency)

            # Calculate total risk from open positions
            if open_positions is None:
                open_positions = []

            total_risk_pct = self._calculate_total_risk(open_positions, available_balance)

            # Check if total risk exceeds maximum
            if total_risk_pct >= self.max_risk_pct:
                logger.warning(f"Total risk ({total_risk_pct:.2f}%) exceeds maximum ({self.max_risk_pct}%)")
                return 0.0

            # Calculate remaining risk capacity
            remaining_risk_pct = self.max_risk_pct - total_risk_pct

            # Get risk percentage based on market conditions
            risk_pct = self.get_risk_percentage(market_data)

            # Adjust risk percentage if it exceeds remaining capacity
            risk_pct = min(risk_pct, remaining_risk_pct)

            # Calculate position size
            position_size = self._calculate_position_size(available_balance, risk_pct, entry_price, stop_loss_price)

            logger.info(f"Calculated position size: {position_size:.8f} at entry price: ${entry_price:.2f}, stop loss: ${stop_loss_price:.2f}, risk: {risk_pct:.2f}%")
            return position_size
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0

    def _calculate_total_risk(self, positions, account_balance):
        """
        Calculate the total risk across all open positions

        Args:
            positions (list): List of position dictionaries
            account_balance (float): Total account balance

        Returns:
            float: Total risk as a percentage of account balance
        """
        try:
            if not positions or account_balance <= 0:
                return 0.0

            total_risk_amount = 0.0

            for position in positions:
                # Get position details
                entry_price = position.get("entry_price", 0)
                quantity = position.get("quantity", 0)
                stop_loss_pct = position.get("stop_loss_pct", 0.02)  # Default to 2%

                # Calculate position size
                position_size = entry_price * quantity

                # Calculate risk amount
                risk_amount = position_size * stop_loss_pct

                # Add to total risk
                total_risk_amount += risk_amount

            # Calculate risk as percentage of account balance
            total_risk_pct = (total_risk_amount / account_balance) * 100

            return total_risk_pct
        except Exception as e:
            logger.error(f"Error calculating total risk: {e}")
            return 0.0

    def _calculate_position_size(self, account_balance, risk_pct, entry_price, stop_loss_price):
        """
        Calculate position size based on risk percentage and intelligent allocation

        Args:
            account_balance (float): Account balance
            risk_pct (float): Risk percentage (0-100)
            entry_price (float): Entry price
            stop_loss_price (float): Stop loss price

        Returns:
            float: Position size in base currency
        """
        try:
            if account_balance <= 0 or entry_price <= 0 or stop_loss_price <= 0:
                return 0.0

            # Convert risk percentage to decimal
            risk_decimal = risk_pct / 100

            # Calculate risk amount
            risk_amount = account_balance * risk_decimal

            # Calculate price difference
            price_diff = abs(entry_price - stop_loss_price)

            # Calculate base position size
            if price_diff > 0:
                base_position_size = risk_amount / (price_diff / entry_price)
            else:
                return 0.0

            # Intelligent allocation based on account size
            import math  # Add math import at the top of the file if not already there

            if account_balance < 100:
                # Small account: Be more conservative
                position_size = base_position_size * 0.8
                logger.info(f"Small account detected (<100 USDT). Using conservative position sizing (80% of calculated size)")
            elif account_balance < 1000:
                # Medium account: Use standard position size
                position_size = base_position_size
                logger.info(f"Medium account detected (<1000 USDT). Using standard position sizing")
            else:
                # Large account: Scale position size based on account size
                # Use a logarithmic scale to prevent excessive position sizes
                scale_factor = 1.0 + (math.log10(account_balance / 1000) * 0.2)  # Max 20% increase per order of magnitude
                position_size = base_position_size * scale_factor
                logger.info(f"Large account detected (>{account_balance} USDT). Scaling position by factor {scale_factor:.2f}")

            # Ensure position size is not too large (max 10% of account regardless of risk calculation)
            max_position_value = account_balance * 0.1
            max_position_size = max_position_value / entry_price

            if position_size > max_position_size:
                logger.warning(f"Position size ({position_size:.8f}) exceeds maximum allowed (10% of account). Limiting to {max_position_size:.8f}")
                position_size = max_position_size

            # Round position size to appropriate precision based on price
            if entry_price < 10:
                position_size = round(position_size, 5)  # More precision for low-priced assets
            elif entry_price < 100:
                position_size = round(position_size, 4)
            elif entry_price < 1000:
                position_size = round(position_size, 3)
            else:
                position_size = round(position_size, 2)

            logger.info(f"Intelligent position sizing: {position_size:.8f} units at ${entry_price:.2f}")
            return position_size
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0

class SimpleAITradingBot:
    """
    Simplified AI trading bot for testing
    """

    def __init__(self, symbol="BTC/USDT", test_mode=True, use_testnet=False):
        """
        Initialize the trading bot

        Args:
            symbol (str): Trading pair symbol
            test_mode (bool): Whether to run in test mode (no real trades)
            use_testnet (bool): Whether to use Binance Testnet API
        """
        self.symbol = symbol
        self.test_mode = test_mode
        self.use_testnet = use_testnet

        # Initialize Key Manager for secure API key storage
        self.key_manager = KeyManager()

        # Load keys from environment variables if not already loaded
        if not self.key_manager.get_key("binance", "main"):
            self.key_manager.load_keys_from_env()

        # Get API keys based on whether we're using testnet or not
        if use_testnet:
            # Get Testnet API keys
            api_key_data = self.key_manager.get_key("binance_testnet", "main")
            if not api_key_data:
                logger.error("Binance Testnet API keys not found. Please set BINANCE_TESTNET_API_KEY and BINANCE_TESTNET_SECRET_KEY in .env file.")
                sys.exit(1)
            logger.info("Using Binance Testnet API keys")
        else:
            # Get regular API keys
            api_key_data = self.key_manager.get_key("binance", "main")
            if not api_key_data:
                logger.error("Binance API keys not found. Please set BINANCE_API_KEY and BINANCE_API_SECRET in .env file.")
                sys.exit(1)

        # Extract key and secret from the key data
        api_key = api_key_data.get("key")
        api_secret = api_key_data.get("secret")

        if not api_key or not api_secret:
            logger.error("Binance API keys are incomplete. Please check your .env file.")
            sys.exit(1)

        # Initialize API with real implementation
        self.exchange = BinanceAPI(api_key, api_secret, use_testnet=use_testnet)

        # Initialize Isolated Margin Manager
        self.margin_manager = IsolatedMarginManager(self.exchange)

        # Initialize Margin Monitor
        warning_threshold = float(os.getenv("MARGIN_WARNING_THRESHOLD", "75"))
        critical_threshold = float(os.getenv("MARGIN_CRITICAL_THRESHOLD", "90"))
        self.margin_monitor = MarginMonitor(
            self.exchange,
            self.margin_manager,
            warning_threshold=warning_threshold,
            critical_threshold=critical_threshold
        )

        # Register callbacks for margin events
        self.margin_monitor.register_callback('critical', self._handle_critical_margin)
        self.margin_monitor.register_callback('warning', self._handle_warning_margin)

        # Initialize Historical Data Loader
        self.historical_data_loader = HistoricalDataLoader(self.exchange)

        # Initialize Advanced Position Sizer
        self.position_sizer = AdvancedPositionSizer(self.exchange, self.margin_monitor)

        # Initialize AI market analyzer
        self.ai_market_analyzer = AIMarketAnalyzer(self.key_manager)

        # Initialize Memory Guard for resource monitoring
        cpu_threshold = int(os.getenv("CPU_THRESHOLD", "95"))  # Optimized for i3-2310M CPU
        ram_threshold = int(os.getenv("RAM_THRESHOLD", "95"))  # Optimized for 8GB RAM
        self.memory_guard = MemoryGuard(cpu_threshold, ram_threshold, parent_bot=self)
        self.memory_guard.start_monitoring()

        # Initialize State Tracker for crash recovery
        self.state_tracker = StateTracker()
        self.state_tracker.start_auto_save()

        # Register memory guard alerts with state tracker
        self.memory_guard.register_alert_callback(self._handle_resource_alert)

        # Start margin monitoring
        self.margin_monitor.add_symbol(self.symbol)
        self.margin_monitor.start_monitoring()
        logger.info(f"Margin monitoring started for {self.symbol}")

        # Initialize capital manager
        min_balance = float(os.getenv("MIN_BALANCE_USDT", "7.0"))
        max_risk_pct = float(os.getenv("MAX_RISK_PCT", "5.0"))
        self.capital_manager = DynamicCapitalManager(self.exchange, min_balance, max_risk_pct)

        # Initialize trading parameters
        self.check_interval = 60  # seconds
        self.trade_amount = float(os.getenv("TRADE_AMOUNT_USDT", "20"))  # USDT
        self.stop_loss = 0.02  # 2%
        self.take_profit = 0.015  # 1.5% (reduced from 3%)
        self.max_trades_per_day = int(os.getenv("MAX_TRADES_PER_DAY", "50"))  # Increased from 5 to 50 by default

        # Initialize SL/TP management parameters
        self.tp1_r_multiplier = 1.0  # Take profit 1 at 1R (distance equal to the trade's risk)
        self.tp2_r_multiplier = 2.0  # Take profit 2 at 2R (double the distance)
        self.tp3_r_multiplier = 3.0  # Take profit 3 at 3R (triple the distance)
        self.breakeven_r_threshold = 1.0  # Move stop to breakeven when 1R is reached
        self.trailing_stop_activation_pct = 0.005  # 0.5% price movement to activate trailing stop
        self.trailing_stop_step_pct = 0.0025  # 0.25% step for trailing stop

        # Initialize smart exit parameters
        self.enable_smart_exit = True  # Enable smart exit rules
        self.tp1_exit_percentage = 0.5  # Sell 50% at TP1
        self.rsi_exit_threshold_high = 70  # RSI high threshold for exit
        self.rsi_exit_threshold_low = 30  # RSI low threshold for exit
        self.macd_exit_threshold = 0  # MACD threshold for exit

        # Initialize parallel position management parameters
        self.max_positions = 3  # Maximum number of open positions at the same time
        self.max_positions_per_symbol = 1  # Maximum number of positions per symbol
        self.max_total_risk_pct = 5.0  # Maximum total risk across all positions (%)

        # Initialize compounding mode parameters
        self.compounding_mode = True  # Enable compounding mode
        self.profit_retention_pct = 50  # Retain 50% of profit
        self.lot_increase_pct = 10  # Increase lot by 10% on next trade after winning
        self.base_trade_amount = float(os.getenv("TRADE_AMOUNT_USDT", "20"))  # Base USDT amount for trading
        self.current_trade_amount = self.base_trade_amount  # Current trade amount (will be adjusted in compounding mode)

        # Initialize Ultimate Safety Rules parameters
        self.mandatory_stop_loss = True  # Mandatory stop loss activation with every trade
        self.consecutive_losses_limit = 2  # Trading freeze after two consecutive losses
        self.trading_freeze_duration = 60  # Trading freeze duration in minutes (1 hour)
        self.signal_update_interval = 60  # Update signals every minute (in seconds)

        # Initialize state variables
        self.current_position = None  # Current position being managed (for backward compatibility)
        self.open_positions = []  # List to track all open positions
        self.position_symbols = set()  # Set of symbols with open positions
        self.trade_history = []
        self.daily_trade_count = 0
        self.last_trade_date = None
        self.last_high_price = 0  # For trailing stop (long positions)
        self.last_low_price = 0  # For trailing stop (short positions)
        self.last_market_data = None  # Store the last market data for smart exit checks
        self.force_trade = False  # Flag to force a trade for testing
        self.last_trade_was_winner = False  # Flag to track if the last trade was a winner
        self.consecutive_losses = 0  # Counter for consecutive losses
        self.trading_frozen_until = None  # Timestamp until which trading is frozen
        self.last_signal_update = None  # Timestamp of the last signal update

        logger.info(f"SimpleAITradingBot initialized for {symbol} in {'TEST' if test_mode else 'LIVE'} mode")

    def check_price(self):
        """
        Check current price

        Returns:
            float: Current price
        """
        ticker = self.exchange.get_ticker(self.symbol)
        price = ticker['last']
        logger.info(f"Current price for {self.symbol}: ${price:.2f}")
        return price

    def get_account_balance(self, currency="USDT"):
        """
        Get total account balance for a currency

        Args:
            currency (str): Currency symbol

        Returns:
            float: Total account balance
        """
        try:
            balance = self.exchange.get_balance()
            total = balance.get(currency, {}).get("total", 0)

            logger.info(f"Total account balance: {total} {currency}")
            return total
        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return 0.0

    def calculate_stop_loss(self, price, side, market_data=None):
        """
        Calculate stop loss price

        For buy positions: Stop loss is set at 0.5% below the recent low
        For sell positions: Stop loss is set at 0.5% above the recent high

        Args:
            price (float): Entry price
            side (str): Trade side (buy or sell)
            market_data (dict): Market data

        Returns:
            float: Stop loss price
        """
        # Default stop loss (fallback)
        default_stop_loss = price * (1 - self.stop_loss) if side == "buy" else price * (1 + self.stop_loss)

        # If no market data, use default stop loss
        if not market_data:
            return default_stop_loss

        # Get historical data
        historical = market_data.get("historical", [])

        # If no historical data, use default stop loss
        if not historical or len(historical) < 10:
            return default_stop_loss

        # Get recent price data (last 20 candles for better low/high detection)
        recent_data = historical[-20:]

        if side == "buy":
            # Find the recent low
            recent_lows = [candle.get("low", 0) for candle in recent_data]
            recent_low = min(recent_lows) if recent_lows else price * (1 - self.stop_loss)

            # Set stop loss 0.5% below the recent low
            stop_loss_price = recent_low * 0.995

            logger.info(f"Buy stop loss: ${stop_loss_price:.2f} (0.5% below recent low of ${recent_low:.2f})")
            return stop_loss_price
        else:
            # Find the recent high
            recent_highs = [candle.get("high", 0) for candle in recent_data]
            recent_high = max(recent_highs) if recent_highs else price * (1 + self.stop_loss)

            # Set stop loss 0.5% above the recent high
            stop_loss_price = recent_high * 1.005

            logger.info(f"Sell stop loss: ${stop_loss_price:.2f} (0.5% above recent high of ${recent_high:.2f})")
            return stop_loss_price

    def check_smart_exit_conditions(self, market_data, side):
        """
        Check if smart exit conditions are met based on indicators

        Args:
            market_data (dict): Market data including indicators
            side (str): Position side (buy or sell)

        Returns:
            tuple: (exit_signal, reason)
        """
        try:
            if not self.enable_smart_exit or not market_data:
                return False, None

            # Extract historical data
            historical = market_data.get("historical", [])

            if not historical or len(historical) < 26:  # Need at least 26 candles for MACD
                return False, None

            # Extract price data
            closes = [candle.get("close", 0) for candle in historical]

            # Calculate RSI
            rsi_values = []
            if len(closes) >= 14:
                # Calculate price changes
                changes = [closes[i] - closes[i-1] for i in range(1, len(closes))]

                # Calculate gains and losses
                gains = [max(0, change) for change in changes]
                losses = [max(0, -change) for change in changes]

                # Calculate average gain and loss
                period = 14
                avg_gain = sum(gains[:period]) / period
                avg_loss = sum(losses[:period]) / period

                # Calculate RSI
                for i in range(period, len(changes)):
                    avg_gain = (avg_gain * 13 + gains[i]) / 14
                    avg_loss = (avg_loss * 13 + losses[i]) / 14

                    if avg_loss == 0:
                        rsi = 100
                    else:
                        rs = avg_gain / avg_loss
                        rsi = 100 - (100 / (1 + rs))

                    rsi_values.append(rsi)

            # Calculate MACD
            macd_line = []
            signal_line = []
            histogram = []

            if len(closes) >= 26:
                # Calculate EMAs
                ema12 = []
                ema26 = []

                # Calculate EMA12
                alpha_12 = 2 / (12 + 1)
                ema = sum(closes[:12]) / 12

                for price in closes[12:]:
                    ema = price * alpha_12 + ema * (1 - alpha_12)
                    ema12.append(ema)

                # Calculate EMA26
                alpha_26 = 2 / (26 + 1)
                ema = sum(closes[:26]) / 26

                for price in closes[26:]:
                    ema = price * alpha_26 + ema * (1 - alpha_26)
                    ema26.append(ema)

                # Calculate MACD line
                for i in range(len(ema26)):
                    if i < len(ema12):
                        macd = ema12[i] - ema26[i]
                        macd_line.append(macd)

                # Calculate signal line
                if len(macd_line) >= 9:
                    alpha_9 = 2 / (9 + 1)
                    ema = sum(macd_line[:9]) / 9

                    for macd in macd_line[9:]:
                        ema = macd * alpha_9 + ema * (1 - alpha_9)
                        signal_line.append(ema)

                    # Calculate histogram
                    for i in range(len(signal_line)):
                        if i < len(macd_line) - 9:
                            hist = macd_line[i + 9] - signal_line[i]
                            histogram.append(hist)

            # Check exit conditions based on position side
            exit_signal = False
            reason = None

            # Get the latest indicator values
            current_rsi = rsi_values[-1] if rsi_values else 50
            current_macd = macd_line[-1] if macd_line else 0
            current_signal = signal_line[-1] if signal_line else 0
            current_histogram = histogram[-1] if histogram else 0

            # For testing smart exit rules, force an exit signal after a few iterations
            if self.test_mode and len(self.trade_history) > 0 and self.current_position is not None:
                # For testing, just force the exit after a few iterations
                # We don't need to calculate the time difference
                time_diff = 20  # Just set a value greater than 10 to force the exit

                # Force exit after 10 seconds
                if time_diff > 10:
                    logger.info("TEST MODE: Forcing a smart exit signal for testing")
                    exit_signal = True
                    reason = "TEST_MODE_FORCED_EXIT"

            if side == "buy":
                # Check RSI overbought condition
                if current_rsi > self.rsi_exit_threshold_high:
                    exit_signal = True
                    reason = f"RSI overbought ({current_rsi:.2f} > {self.rsi_exit_threshold_high})"

                # Check MACD bearish crossover
                if len(macd_line) >= 2 and len(signal_line) >= 2:
                    if macd_line[-2] > signal_line[-2] and macd_line[-1] < signal_line[-1]:
                        exit_signal = True
                        reason = "MACD bearish crossover"

                # Check MACD histogram turning negative
                if current_histogram < self.macd_exit_threshold and histogram[-2] > self.macd_exit_threshold:
                    exit_signal = True
                    reason = "MACD histogram turned negative"

            elif side == "sell":
                # Check RSI oversold condition
                if current_rsi < self.rsi_exit_threshold_low:
                    exit_signal = True
                    reason = f"RSI oversold ({current_rsi:.2f} < {self.rsi_exit_threshold_low})"

                # Check MACD bullish crossover
                if len(macd_line) >= 2 and len(signal_line) >= 2:
                    if macd_line[-2] < signal_line[-2] and macd_line[-1] > signal_line[-1]:
                        exit_signal = True
                        reason = "MACD bullish crossover"

                # Check MACD histogram turning positive
                if current_histogram > self.macd_exit_threshold and histogram[-2] < self.macd_exit_threshold:
                    exit_signal = True
                    reason = "MACD histogram turned positive"

            return exit_signal, reason

        except Exception as e:
            logger.error(f"Error checking smart exit conditions: {e}")
            return False, None

    def get_market_data(self):
        """
        Get market data for analysis

        Returns:
            dict: Market data
        """
        # Get current price
        ticker = self.exchange.get_ticker(self.symbol)

        # Use historical data loader to ensure we have enough data
        min_candles = 250  # Minimum candles needed for reliable indicators (EMA200, ATR, etc.)
        ohlcv = self.historical_data_loader.ensure_minimum_candles(
            self.symbol,
            timeframe='1h',
            min_candles=min_candles
        )

        # Validate data quality
        if not self.historical_data_loader.validate_data_quality(ohlcv, min_candles):
            logger.warning(f"Historical data quality validation failed. Using fallback method.")
            # Fallback to direct API call
            ohlcv = self.exchange.get_ohlcv(self.symbol, timeframe='1h', limit=min_candles)

        # Log data availability
        logger.info(f"Using {len(ohlcv)} historical candles for analysis")

        # Calculate basic indicators
        closes = [candle['close'] for candle in ohlcv]
        highs = [candle['high'] for candle in ohlcv]
        lows = [candle['low'] for candle in ohlcv]
        volumes = [candle['volume'] for candle in ohlcv]

        # Simple Moving Averages
        sma_5 = sum(closes[-5:]) / 5 if len(closes) >= 5 else None
        sma_10 = sum(closes[-10:]) / 10 if len(closes) >= 10 else None
        sma_20 = sum(closes[-20:]) / 20 if len(closes) >= 20 else None

        # Calculate EMA50 and EMA200 manually
        ema50 = None
        ema200 = None
        is_uptrend = False
        is_downtrend = False
        is_flat = True
        trend_signal = "hold"

        # Only calculate EMAs if we have enough data
        if len(closes) >= 200:
            # Calculate EMA50
            ema50_values = []
            alpha_50 = 2 / (50 + 1)
            ema = sum(closes[:50]) / 50  # Simple average for first value
            ema50_values.append(ema)

            for price in closes[50:]:
                ema = price * alpha_50 + ema * (1 - alpha_50)
                ema50_values.append(ema)

            # Calculate EMA200
            ema200_values = []
            alpha_200 = 2 / (200 + 1)
            ema = sum(closes[:200]) / 200  # Simple average for first value
            ema200_values.append(ema)

            for price in closes[200:]:
                ema = price * alpha_200 + ema * (1 - alpha_200)
                ema200_values.append(ema)

            # Get the latest values
            ema50 = ema50_values[-1] if ema50_values else None
            ema200 = ema200_values[-1] if ema200_values else None

            # Calculate EMA50 slope (simplified)
            ema50_slope = 0
            if len(ema50_values) >= 5:
                ema50_slope = (ema50_values[-1] - ema50_values[-5]) / 5

            # Check if EMA50 is flat
            is_flat = abs(ema50_slope) < 0.05 if ema50_slope is not None else True

            # Check trend conditions
            if ema50 is not None and ema200 is not None:
                is_uptrend = ema50 > ema200 and ema50_slope > 0
                is_downtrend = ema50 < ema200 and ema50_slope < 0

            # Determine trend signal
            if is_flat:
                trend_signal = "hold"
            elif is_uptrend:
                trend_signal = "buy"
            elif is_downtrend:
                trend_signal = "sell"
            else:
                trend_signal = "hold"

        # Prepare market data
        market_data = {
            "symbol": self.symbol,
            "price": ticker['last'],
            "timestamp": datetime.datetime.now().isoformat(),
            "historical": ohlcv,
            "indicators": {
                "sma_5": sma_5,
                "sma_10": sma_10,
                "sma_20": sma_20,
                "ema50": ema50,
                "ema200": ema200,
                "ema50_slope": ema50_slope if 'ema50_slope' in locals() else 0,
                "is_uptrend": is_uptrend,
                "is_downtrend": is_downtrend,
                "is_flat": is_flat,
                "trend_signal": trend_signal
            }
        }

        logger.info(f"Trend analysis: {trend_signal} (Uptrend: {is_uptrend}, Downtrend: {is_downtrend}, Flat: {is_flat})")

        # Store the market data for smart exit checks
        self.last_market_data = market_data

        # Check smart entry conditions (simplified version)
        try:
            # Extract price data
            historical = market_data.get("historical", [])
            closes = [candle.get("close", 0) for candle in historical]
            highs = [candle.get("high", 0) for candle in historical]
            lows = [candle.get("low", 0) for candle in historical]
            volumes = [candle.get("volume", 0) for candle in historical]

            # Calculate RSI
            rsi_values = []
            if len(closes) >= 14:
                # Calculate price changes
                changes = [closes[i] - closes[i-1] for i in range(1, len(closes))]

                # Calculate gains and losses
                gains = [max(0, change) for change in changes]
                losses = [max(0, -change) for change in changes]

                # Calculate average gain and loss
                period = 14
                avg_gain = sum(gains[:period]) / period
                avg_loss = sum(losses[:period]) / period

                # Calculate RSI
                for i in range(period, len(changes)):
                    avg_gain = (avg_gain * 13 + gains[i]) / 14
                    avg_loss = (avg_loss * 13 + losses[i]) / 14

                    if avg_loss == 0:
                        rsi = 100
                    else:
                        rs = avg_gain / avg_loss
                        rsi = 100 - (100 / (1 + rs))

                    rsi_values.append(rsi)

            # Calculate MACD
            macd_line = []
            signal_line = []
            histogram = []

            if len(closes) >= 26:
                # Calculate EMAs
                ema12 = []
                ema26 = []

                # Calculate EMA12
                alpha_12 = 2 / (12 + 1)
                ema = sum(closes[:12]) / 12

                for price in closes[12:]:
                    ema = price * alpha_12 + ema * (1 - alpha_12)
                    ema12.append(ema)

                # Calculate EMA26
                alpha_26 = 2 / (26 + 1)
                ema = sum(closes[:26]) / 26

                for price in closes[26:]:
                    ema = price * alpha_26 + ema * (1 - alpha_26)
                    ema26.append(ema)

                # Calculate MACD line
                for i in range(len(ema26)):
                    if i < len(ema12):
                        macd = ema12[i] - ema26[i]
                        macd_line.append(macd)

                # Calculate signal line
                if len(macd_line) >= 9:
                    alpha_9 = 2 / (9 + 1)
                    ema = sum(macd_line[:9]) / 9

                    for macd in macd_line[9:]:
                        ema = macd * alpha_9 + ema * (1 - alpha_9)
                        signal_line.append(ema)

                    # Calculate histogram
                    for i in range(len(signal_line)):
                        if i < len(macd_line) - 9:
                            hist = macd_line[i + 9] - signal_line[i]
                            histogram.append(hist)

            # Check volume
            volume_above_average = False
            if len(volumes) >= 20:
                avg_volume = sum(volumes[-21:-1]) / 20
                volume_above_average = volumes[-1] > avg_volume * 1.1

            # Check ATR
            atr_values = []
            if len(highs) >= 14 and len(lows) >= 14 and len(closes) >= 14:
                tr_values = []

                for i in range(len(closes)):
                    if i == 0:
                        tr = highs[i] - lows[i]
                    else:
                        tr1 = highs[i] - lows[i]
                        tr2 = abs(highs[i] - closes[i-1])
                        tr3 = abs(lows[i] - closes[i-1])
                        tr = max(tr1, tr2, tr3)

                    tr_values.append(tr)

                # Calculate ATR
                for i in range(len(tr_values)):
                    if i < 14:
                        atr_values.append(None)
                    else:
                        atr = sum(tr_values[i-14:i]) / 14
                        atr_values.append(atr)

            # Check conditions
            # Buy conditions
            ema_condition_buy = is_uptrend

            rsi_condition_buy = False
            if len(rsi_values) >= 3:
                rsi_breaking_up = rsi_values[-3] < 30 and 30 <= rsi_values[-1] <= 40 and rsi_values[-1] > rsi_values[-2]
                rsi_condition_buy = rsi_breaking_up

            macd_condition_buy = False
            if len(macd_line) >= 2 and len(signal_line) >= 2 and len(histogram) >= 1:
                bullish_crossover = macd_line[-2] < signal_line[-2] and macd_line[-1] > signal_line[-1]
                positive_histogram = histogram[-1] > 0
                macd_condition_buy = bullish_crossover and positive_histogram

            atr_condition = True
            if len(atr_values) >= 20:
                avg_atr = sum([a for a in atr_values[-21:-1] if a is not None]) / 20
                atr_high = atr_values[-1] is not None and atr_values[-1] > avg_atr * 1.5
                atr_condition = not atr_high

            # Sell conditions
            ema_condition_sell = is_downtrend

            rsi_condition_sell = False
            if len(rsi_values) >= 3:
                rsi_breaking_down = rsi_values[-3] > 70 and 60 <= rsi_values[-1] <= 70 and rsi_values[-1] < rsi_values[-2]
                rsi_condition_sell = rsi_breaking_down

            macd_condition_sell = False
            if len(macd_line) >= 2 and len(signal_line) >= 2 and len(histogram) >= 1:
                bearish_crossover = macd_line[-2] > signal_line[-2] and macd_line[-1] < signal_line[-1]
                negative_histogram = histogram[-1] < 0
                macd_condition_sell = bearish_crossover and negative_histogram

            # Compile conditions
            buy_conditions = {
                "ema50_gt_ema200": ema_condition_buy,
                "rsi_breaking_up": rsi_condition_buy,
                "bullish_macd_crossover": macd_condition_buy,
                "volume_above_average": volume_above_average,
                "atr_not_high": atr_condition
            }

            sell_conditions = {
                "ema50_lt_ema200": ema_condition_sell,
                "rsi_breaking_down": rsi_condition_sell,
                "bearish_macd_crossover": macd_condition_sell,
                "volume_above_average": volume_above_average,
                "atr_not_high": atr_condition
            }

            # Calculate scores based on 3 out of 5 indicators (60% instead of requiring all 5)
            buy_score = 0
            sell_score = 0

            # Count conditions met
            buy_conditions_met = sum(1 for condition in buy_conditions.values() if condition)
            sell_conditions_met = sum(1 for condition in sell_conditions.values() if condition)

            # Calculate scores (3 out of 5 = 60%)
            if len(buy_conditions) > 0:
                buy_score = (buy_conditions_met / len(buy_conditions)) * 100

            if len(sell_conditions) > 0:
                sell_score = (sell_conditions_met / len(sell_conditions)) * 100

            # Determine signals (require only 3 out of 5 indicators = 60%)
            # For uptrends, reduce threshold to 60% (3 out of 5 indicators)
            buy_threshold = 60 if is_uptrend else 70
            sell_threshold = 70  # Keep sell threshold strict

            buy_signal = buy_score >= buy_threshold
            sell_signal = sell_score >= sell_threshold

            logger.info(f"Buy conditions met: {buy_conditions_met}/5 (threshold: {buy_threshold}%)")
            logger.info(f"Sell conditions met: {sell_conditions_met}/5 (threshold: {sell_threshold}%)")

            # Create entry conditions
            entry_conditions = {
                "buy_conditions": buy_conditions,
                "sell_conditions": sell_conditions,
                "buy_score": buy_score,
                "sell_score": sell_score,
                "buy_signal": buy_signal,
                "sell_signal": sell_signal
            }

            # Add entry conditions to market data
            market_data["entry_conditions"] = entry_conditions

            # Log entry conditions
            logger.info(f"Smart entry conditions: Buy score: {buy_score:.1f}%, Sell score: {sell_score:.1f}%")
            logger.info(f"Smart entry signals: Buy: {buy_signal}, Sell: {sell_signal}")

            # Log individual conditions
            if buy_score > 0:
                logger.info(f"Buy conditions: {', '.join([k for k, v in buy_conditions.items() if v])}")

            if sell_score > 0:
                logger.info(f"Sell conditions: {', '.join([k for k, v in sell_conditions.items() if v])}")
        except Exception as e:
            logger.error(f"Error checking smart entry conditions: {e}")
            market_data["entry_conditions"] = {
                "buy_conditions": {},
                "sell_conditions": {},
                "buy_score": 0,
                "sell_score": 0,
                "buy_signal": False,
                "sell_signal": False
            }

        return market_data

    def analyze_market(self):
        """
        Analyze market using multiple AI services with account rotation

        Returns:
            dict: Aggregated signal
        """
        # Get market data
        market_data = self.get_market_data()

        # Use AI market analyzer with multiple AI services and account rotation
        aggregated_signal = self.ai_market_analyzer.analyze_market(market_data)

        # For testing smart exit rules, force a buy signal in the first iteration
        if self.test_mode and len(self.trade_history) == 0:
            logger.info("TEST MODE: Forcing a BUY signal for testing smart exit rules")

            # Override the entry conditions to allow the trade
            market_data["indicators"]["is_uptrend"] = True
            market_data["indicators"]["is_downtrend"] = False
            market_data["indicators"]["is_flat"] = False

            market_data["entry_conditions"] = {
                "buy_conditions": {"ema50_gt_ema200": True, "rsi_breaking_up": True, "bullish_macd_crossover": True, "volume_above_average": True, "atr_not_high": True},
                "sell_conditions": {},
                "buy_score": 100,
                "sell_score": 0,
                "buy_signal": True,
                "sell_signal": False
            }

            # Store the updated market data
            self.last_market_data = market_data

            # Create a buy signal
            aggregated_signal = {
                "recommendation": "buy",
                "confidence": 0.85,
                "price": market_data.get("price", 0),
                "timestamp": datetime.datetime.now().isoformat()
            }

        return aggregated_signal

    def execute_trade(self, market_signal):
        """
        Execute a trade based on the signal

        Args:
            market_signal (dict): Trading signal

        Returns:
            dict: Order information
        """
        recommendation = market_signal.get("recommendation", "hold")
        confidence = market_signal.get("confidence", 0)
        price = market_signal.get("price", 0)

        # Check if we should execute a trade
        if recommendation == "hold":
            logger.info("Signal recommends HOLD. No trade executed.")
            return None

        # Check if we've reached the daily trade limit
        current_date = datetime.datetime.now().date()
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date

        if self.daily_trade_count >= self.max_trades_per_day:
            logger.warning(f"Daily trade limit reached ({self.max_trades_per_day}). No trade executed.")
            return None

        # Check if we already have the maximum number of positions
        if len(self.open_positions) >= self.max_positions:
            logger.warning(f"Maximum number of positions reached ({self.max_positions}). No trade executed.")
            return None

        # Check if we already have a position for this symbol
        if self.symbol in self.position_symbols:
            logger.warning(f"Already have an open position for {self.symbol}. No trade executed.")
            return None

        # Check if total risk across all positions would exceed the maximum
        total_risk_pct = self.capital_manager._calculate_total_risk(self.open_positions, self.capital_manager.get_available_balance())
        if total_risk_pct >= self.max_total_risk_pct:
            logger.warning(f"Total risk across all positions ({total_risk_pct:.2f}%) would exceed maximum ({self.max_total_risk_pct}%). No trade executed.")
            return None

        # Check if we have enough balance to trade
        if not self.capital_manager.can_trade():
            logger.warning("Insufficient balance to trade. No trade executed.")
            return None

        # Get market data for risk calculation and trend analysis
        market_data = self.get_market_data()

        # Check trend conditions
        is_uptrend = market_data.get("indicators", {}).get("is_uptrend", False)
        is_downtrend = market_data.get("indicators", {}).get("is_downtrend", False)
        is_flat = market_data.get("indicators", {}).get("is_flat", True)

        # Check smart entry conditions
        entry_conditions = market_data.get("entry_conditions", {})
        buy_signal = entry_conditions.get("buy_signal", False)
        sell_signal = entry_conditions.get("sell_signal", False)
        buy_score = entry_conditions.get("buy_score", 0)
        sell_score = entry_conditions.get("sell_score", 0)

        # Check if AI confidence is high enough to bypass indicator checks
        ai_confidence = market_signal.get("confidence", 0)
        high_ai_confidence = ai_confidence >= 0.8  # 80% or higher confidence

        if high_ai_confidence:
            logger.info(f"AI confidence is high ({ai_confidence:.2f} ≥ 0.8): Bypassing indicator checks")

        # Only execute BUY if in uptrend and smart entry conditions are met (or high AI confidence)
        if recommendation == "buy":
            # For testing or high AI confidence, bypass the trend and entry conditions checks
            if self.test_mode and len(self.trade_history) == 0:
                logger.info("TEST MODE: Bypassing trend and entry conditions checks for testing")
            elif high_ai_confidence:
                logger.info(f"High AI confidence ({ai_confidence:.2f}): Executing BUY despite indicator conditions")
            else:
                if not is_uptrend:
                    logger.warning("BUY signal ignored: Not in uptrend according to EMA50/EMA200")
                    return None
                elif not buy_signal:
                    logger.warning(f"BUY signal ignored: Smart entry conditions not met (score: {buy_score:.1f}%)")
                    return None
        # Only execute SELL if in downtrend and smart entry conditions are met (or high AI confidence)
        elif recommendation == "sell":
            # For testing or high AI confidence, bypass the trend and entry conditions checks
            if self.test_mode and len(self.trade_history) == 0:
                logger.info("TEST MODE: Bypassing trend and entry conditions checks for testing")
            elif high_ai_confidence:
                logger.info(f"High AI confidence ({ai_confidence:.2f}): Executing SELL despite indicator conditions")
            else:
                if not is_downtrend:
                    logger.warning("SELL signal ignored: Not in downtrend according to EMA50/EMA200")
                    return None
                elif not sell_signal:
                    logger.warning(f"SELL signal ignored: Smart entry conditions not met (score: {sell_score:.1f}%)")
                    return None

        # Ignore trading if EMA50 is flat (unless high AI confidence)
        if is_flat and not (self.test_mode and len(self.trade_history) == 0) and not high_ai_confidence:
            logger.warning(f"{recommendation.upper()} signal ignored: EMA50 is flat")
            return None
        elif is_flat and high_ai_confidence:
            logger.info(f"EMA50 is flat but proceeding with {recommendation.upper()} due to high AI confidence ({ai_confidence:.2f})")

        # Execute the trade
        if recommendation == "buy":
            # Calculate stop loss price based on recent low
            from analysis.indicators import find_recent_low
            historical = market_data.get("historical", [])
            low_prices = [candle.get("low", 0) for candle in historical]
            recent_low = find_recent_low(low_prices)
            stop_loss_price = recent_low * 0.995  # 0.5% below recent low

            # Ensure mandatory stop loss activation
            if self.mandatory_stop_loss and (stop_loss_price <= 0 or stop_loss_price >= price):
                # If stop loss calculation failed or is invalid, use default stop loss
                stop_loss_price = price * (1 - self.stop_loss)
                logger.warning(f"Invalid stop loss price calculated. Using default stop loss at ${stop_loss_price:.2f} ({self.stop_loss*100}% below entry price)")

            # Validate stop loss (mandatory safety rule)
            if stop_loss_price <= 0 or stop_loss_price >= price:
                logger.error(f"Invalid stop loss price: ${stop_loss_price:.2f}. Trade execution aborted.")
                return None

            # Calculate position size using advanced position sizer
            position_size = self.position_sizer.calculate_position_size(
                self.symbol,
                price,
                stop_loss_price,
                account_balance=self.get_account_balance(),
                market_data=market_data
            )

            if position_size <= 0:
                logger.warning("Position size calculation resulted in zero or negative size. No trade executed.")
                return None

            # Get actual USDT balance
            available_balance = self.capital_manager.get_available_balance("USDT")

            # Calculate trade amount based on available balance
            # Use a percentage of available balance (10-20% depending on account size)
            if available_balance < 100:
                balance_percentage = 0.1  # 10% for small accounts
            elif available_balance < 1000:
                balance_percentage = 0.15  # 15% for medium accounts
            else:
                balance_percentage = 0.2  # 20% for large accounts

            # Calculate base trade amount from balance
            base_trade_amount = available_balance * balance_percentage

            # Apply compounding if enabled
            if self.compounding_mode and self.last_trade_was_winner:
                # Increase lot size by 10% after a winning trade
                trade_amount = base_trade_amount * (1 + self.lot_increase_pct / 100)
                logger.info(f"Compounding mode: Increasing lot size by {self.lot_increase_pct}% after winning trade")
            else:
                trade_amount = base_trade_amount

            # Ensure minimum trade amount
            min_trade_amount = float(os.getenv("MIN_TRADE_AMOUNT_USDT", "10.0"))
            if trade_amount < min_trade_amount:
                trade_amount = min_trade_amount
                logger.info(f"Trade amount below minimum. Using minimum trade amount: ${min_trade_amount:.2f}")

            # Ensure maximum trade amount (30% of balance)
            max_trade_amount = available_balance * 0.3
            if trade_amount > max_trade_amount:
                trade_amount = max_trade_amount
                logger.info(f"Trade amount above maximum. Limiting to 30% of balance: ${max_trade_amount:.2f}")

            logger.info(f"Using trade amount: ${trade_amount:.2f} (Available balance: ${available_balance:.2f})")

            # Store the current trade amount for this trade
            self.current_position_trade_amount = trade_amount
            self.current_trade_amount = trade_amount  # Update the current trade amount

            logger.info(f"Executing BUY for {self.symbol} at ${price:.2f} with position size {position_size:.8f}")
            order = self.exchange.create_market_buy_order(self.symbol, trade_amount)

            # Calculate risk (R) value
            risk_value = price - stop_loss_price

            # Calculate take profit targets
            tp1_price = price + (risk_value * self.tp1_r_multiplier)
            tp2_price = price + (risk_value * self.tp2_r_multiplier)
            tp3_price = price + (risk_value * self.tp3_r_multiplier)

            # Calculate breakeven price
            breakeven_price = price

            # Initialize trailing stop variables
            trailing_stop_price = stop_loss_price
            trailing_stop_activated = False

            # Initialize the last high price for trailing stop
            self.last_high_price = price

            # Record position
            self.current_position = {
                "symbol": self.symbol,
                "side": "buy",
                "entry_price": price,
                "quantity": order.get("amount", trade_amount / price),
                "timestamp": datetime.datetime.now().isoformat(),
                "order_id": order.get("id", "test"),
                "signal": market_signal,
                "initial_stop_loss_price": stop_loss_price,
                "stop_loss_price": stop_loss_price,
                "stop_loss_pct": (price - stop_loss_price) / price,
                "risk_value": risk_value,
                "tp1_price": tp1_price,
                "tp2_price": tp2_price,
                "tp3_price": tp3_price,
                "breakeven_price": breakeven_price,
                "trailing_stop_price": trailing_stop_price,
                "trailing_stop_activated": trailing_stop_activated,
                "reached_tp1": False,
                "reached_tp2": False,
                "reached_breakeven": False
            }

            logger.info(f"Stop loss set at: ${stop_loss_price:.2f} (0.5% below recent low)")
            logger.info(f"Take profit targets: TP1: ${tp1_price:.2f} (1R), TP2: ${tp2_price:.2f} (2R), TP3: ${tp3_price:.2f} (3R)")

            # Add to open positions list
            self.open_positions.append(self.current_position)

            # Add symbol to the set of position symbols
            self.position_symbols.add(self.symbol)

            self.daily_trade_count += 1
            logger.info(f"BUY position opened: {self.current_position['quantity']:.8f} {self.symbol} at ${price:.2f}")
            logger.info(f"Total open positions: {len(self.open_positions)}/{self.max_positions}")
            return order

        elif recommendation == "sell":
            # Calculate stop loss price based on recent high
            from analysis.indicators import find_recent_high
            historical = market_data.get("historical", [])
            high_prices = [candle.get("high", 0) for candle in historical]
            recent_high = find_recent_high(high_prices)
            stop_loss_price = recent_high * 1.005  # 0.5% above recent high

            # Ensure mandatory stop loss activation
            if self.mandatory_stop_loss and (stop_loss_price <= 0 or stop_loss_price <= price):
                # If stop loss calculation failed or is invalid, use default stop loss
                stop_loss_price = price * (1 + self.stop_loss)
                logger.warning(f"Invalid stop loss price calculated. Using default stop loss at ${stop_loss_price:.2f} ({self.stop_loss*100}% above entry price)")

            # Validate stop loss (mandatory safety rule)
            if stop_loss_price <= 0 or stop_loss_price <= price:
                logger.error(f"Invalid stop loss price: ${stop_loss_price:.2f}. Trade execution aborted.")
                return None

            # Calculate position size using advanced position sizer
            position_size = self.position_sizer.calculate_position_size(
                self.symbol,
                price,
                stop_loss_price,
                account_balance=self.get_account_balance(),
                market_data=market_data
            )

            if position_size <= 0:
                logger.warning("Position size calculation resulted in zero or negative size. No trade executed.")
                return None

            # Get balance
            balance = self.exchange.get_balance()
            base_currency = self.symbol.split('/')[0]
            available_amount = balance.get(base_currency, {}).get("free", 0)

            # Get actual USDT balance
            available_balance = self.capital_manager.get_available_balance("USDT")

            # Calculate trade amount based on available balance
            # Use a percentage of available balance (10-20% depending on account size)
            if available_balance < 100:
                balance_percentage = 0.1  # 10% for small accounts
            elif available_balance < 1000:
                balance_percentage = 0.15  # 15% for medium accounts
            else:
                balance_percentage = 0.2  # 20% for large accounts

            # Calculate base trade amount from balance
            base_trade_amount = available_balance * balance_percentage

            # Apply compounding if enabled
            if self.compounding_mode and self.last_trade_was_winner:
                # Increase lot size by 10% after a winning trade
                trade_amount = base_trade_amount * (1 + self.lot_increase_pct / 100)
                logger.info(f"Compounding mode: Increasing lot size by {self.lot_increase_pct}% after winning trade")
            else:
                trade_amount = base_trade_amount

            # Ensure minimum trade amount
            min_trade_amount = float(os.getenv("MIN_TRADE_AMOUNT_USDT", "10.0"))
            if trade_amount < min_trade_amount:
                trade_amount = min_trade_amount
                logger.info(f"Trade amount below minimum. Using minimum trade amount: ${min_trade_amount:.2f}")

            # Ensure maximum trade amount (30% of balance)
            max_trade_amount = available_balance * 0.3
            if trade_amount > max_trade_amount:
                trade_amount = max_trade_amount
                logger.info(f"Trade amount above maximum. Limiting to 30% of balance: ${max_trade_amount:.2f}")

            logger.info(f"Using trade amount: ${trade_amount:.2f} (Available balance: ${available_balance:.2f})")

            # Store the current trade amount for this trade
            self.current_position_trade_amount = trade_amount
            self.current_trade_amount = trade_amount  # Update the current trade amount

            # Calculate sell amount based on position size and available amount
            sell_amount = min(position_size, available_amount)

            if sell_amount <= 0:
                logger.warning(f"Insufficient {base_currency} balance for SELL order")
                return None

            logger.info(f"Executing SELL for {self.symbol} at ${price:.2f} with position size {sell_amount:.8f}")
            order = self.exchange.create_market_sell_order(self.symbol, sell_amount)

            # Calculate risk (R) value
            risk_value = stop_loss_price - price

            # Calculate take profit targets
            tp1_price = price - (risk_value * self.tp1_r_multiplier)
            tp2_price = price - (risk_value * self.tp2_r_multiplier)
            tp3_price = price - (risk_value * self.tp3_r_multiplier)

            # Calculate breakeven price
            breakeven_price = price

            # Initialize trailing stop variables
            trailing_stop_price = stop_loss_price
            trailing_stop_activated = False

            # Initialize the last low price for trailing stop
            self.last_low_price = price

            # Record position
            self.current_position = {
                "symbol": self.symbol,
                "side": "sell",
                "entry_price": price,
                "quantity": order.get("amount", sell_amount),
                "timestamp": datetime.datetime.now().isoformat(),
                "order_id": order.get("id", "test"),
                "signal": market_signal,
                "initial_stop_loss_price": stop_loss_price,
                "stop_loss_price": stop_loss_price,
                "stop_loss_pct": (stop_loss_price - price) / price,
                "risk_value": risk_value,
                "tp1_price": tp1_price,
                "tp2_price": tp2_price,
                "tp3_price": tp3_price,
                "breakeven_price": breakeven_price,
                "trailing_stop_price": trailing_stop_price,
                "trailing_stop_activated": trailing_stop_activated,
                "reached_tp1": False,
                "reached_tp2": False,
                "reached_breakeven": False
            }

            logger.info(f"Stop loss set at: ${stop_loss_price:.2f} (0.5% above recent high)")
            logger.info(f"Take profit targets: TP1: ${tp1_price:.2f} (1R), TP2: ${tp2_price:.2f} (2R), TP3: ${tp3_price:.2f} (3R)")

            # Add to open positions list
            self.open_positions.append(self.current_position)

            # Add symbol to the set of position symbols
            self.position_symbols.add(self.symbol)

            self.daily_trade_count += 1
            logger.info(f"SELL position opened: {self.current_position['quantity']:.8f} {self.symbol} at ${price:.2f}")
            logger.info(f"Total open positions: {len(self.open_positions)}/{self.max_positions}")
            return order

        return None

    def manage_positions(self, current_price):
        """
        Manage open positions (check stop loss and take profit)

        Args:
            current_price (float): Current price

        Returns:
            dict: Close order information if position was closed, None otherwise
        """
        if self.current_position is None:
            return None

        entry_price = self.current_position["entry_price"]
        side = self.current_position["side"]
        quantity = self.current_position["quantity"]

        # Get position details
        initial_stop_loss_price = self.current_position.get("initial_stop_loss_price")
        stop_loss_price = self.current_position.get("stop_loss_price", initial_stop_loss_price)
        risk_value = self.current_position.get("risk_value", 0)
        tp1_price = self.current_position.get("tp1_price")
        tp2_price = self.current_position.get("tp2_price")
        tp3_price = self.current_position.get("tp3_price")
        breakeven_price = self.current_position.get("breakeven_price", entry_price)
        trailing_stop_price = self.current_position.get("trailing_stop_price", stop_loss_price)
        trailing_stop_activated = self.current_position.get("trailing_stop_activated", False)
        reached_tp1 = self.current_position.get("reached_tp1", False)
        reached_tp2 = self.current_position.get("reached_tp2", False)
        reached_breakeven = self.current_position.get("reached_breakeven", False)

        if side == "buy":
            # Calculate profit/loss percentage
            pnl_percent = (current_price - entry_price) / entry_price * 100

            # Calculate R-multiple (how many times the initial risk)
            r_multiple = (current_price - entry_price) / risk_value if risk_value > 0 else 0

            logger.info(f"Current position: BUY at ${entry_price:.2f}, current P&L: {pnl_percent:.2f}% ({r_multiple:.2f}R)")

            # Update last high price for trailing stop
            if current_price > self.last_high_price:
                self.last_high_price = current_price

            # Check for smart exit conditions
            if self.enable_smart_exit and self.last_market_data:
                exit_signal, reason = self.check_smart_exit_conditions(self.last_market_data, side)

                if exit_signal and quantity > 0:
                    logger.info(f"Smart exit triggered: {reason}")

                    # Execute full exit
                    order = self.exchange.create_market_sell_order(self.symbol, quantity)

                    # Record trade in history
                    self.trade_history.append({
                        "entry": self.current_position,
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": f"smart_exit_{reason}",
                            "pnl_percent": pnl_percent,
                            "r_multiple": r_multiple,
                            "order_id": order.get("id", "test")
                        }
                    })

                    # Get the symbol from the position before removing it
                    position_symbol = self.current_position.get("symbol", self.symbol)

                    # Remove from open positions list
                    if self.current_position in self.open_positions:
                        self.open_positions.remove(self.current_position)

                    # Remove from tracked symbols
                    if position_symbol in self.position_symbols:
                        self.position_symbols.remove(position_symbol)

                    # Apply compounding mode if enabled
                    if self.compounding_mode and pnl_percent > 0:
                        # This was a winning trade
                        self.last_trade_was_winner = True

                        # Reset consecutive losses counter
                        self.consecutive_losses = 0

                        # Calculate profit amount
                        trade_amount = self.current_position.get("current_position_trade_amount", self.current_trade_amount)
                        profit_amount = trade_amount * (pnl_percent / 100)

                        # Retain 50% of profit and increase lot size
                        retained_profit = profit_amount * (self.profit_retention_pct / 100)

                        # Update current trade amount
                        self.current_trade_amount = self.current_trade_amount + retained_profit

                        logger.info(f"Compounding mode: Retaining {self.profit_retention_pct}% of profit (${retained_profit:.2f})")
                        logger.info(f"New base trade amount: ${self.current_trade_amount:.2f}")
                    else:
                        # This was a losing trade or compounding is disabled
                        self.last_trade_was_winner = False

                        # If it's a losing trade, increment consecutive losses counter
                        if pnl_percent < 0:
                            self.consecutive_losses += 1
                            logger.warning(f"Consecutive losses: {self.consecutive_losses}")

                            # Check if we need to freeze trading
                            if self.consecutive_losses >= self.consecutive_losses_limit:
                                self.freeze_trading()

                    # Clear current position
                    self.current_position = None

                    logger.info(f"Position closed with {pnl_percent:.2f}% {'profit' if pnl_percent > 0 else 'loss'}")
                    logger.info(f"Total open positions: {len(self.open_positions)}/{self.max_positions}")
                    return order

            # Check if TP1 is reached
            if not reached_tp1 and tp1_price is not None and current_price >= tp1_price:
                self.current_position["reached_tp1"] = True
                reached_tp1 = True
                logger.info(f"TP1 reached at ${current_price:.2f} (1R profit)")

                # Partial take profit at TP1 (close 50% of position)
                if quantity > 0:
                    partial_quantity = quantity * self.tp1_exit_percentage
                    logger.info(f"Taking partial profit: Selling {partial_quantity:.8f} at ${current_price:.2f}")

                    # Execute partial sell
                    partial_order = self.exchange.create_market_sell_order(self.symbol, partial_quantity)

                    # Update position quantity
                    self.current_position["quantity"] = quantity - partial_quantity
                    quantity = self.current_position["quantity"]

                    # Move stop loss to breakeven
                    self.current_position["stop_loss_price"] = entry_price
                    stop_loss_price = entry_price
                    logger.info(f"Moving stop loss to breakeven: ${entry_price:.2f}")

                    # Record partial profit in history
                    self.trade_history.append({
                        "entry": {**self.current_position, "quantity": partial_quantity},
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "partial_take_profit_tp1",
                            "pnl_percent": pnl_percent,
                            "r_multiple": r_multiple,
                            "order_id": partial_order.get("id", "test")
                        }
                    })

            # Check if TP2 is reached
            if reached_tp1 and not reached_tp2 and tp2_price is not None and current_price >= tp2_price:
                self.current_position["reached_tp2"] = True
                reached_tp2 = True
                logger.info(f"TP2 reached at ${current_price:.2f} (2R profit)")

                # Partial take profit at TP2 (close 1/2 of remaining position)
                if quantity > 0:
                    partial_quantity = quantity / 2
                    logger.info(f"Taking partial profit: Selling {partial_quantity:.8f} at ${current_price:.2f}")

                    # Execute partial sell
                    partial_order = self.exchange.create_market_sell_order(self.symbol, partial_quantity)

                    # Update position quantity
                    self.current_position["quantity"] = quantity - partial_quantity
                    quantity = self.current_position["quantity"]

                    # Record partial profit in history
                    self.trade_history.append({
                        "entry": {**self.current_position, "quantity": partial_quantity},
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "partial_take_profit_tp2",
                            "pnl_percent": pnl_percent,
                            "r_multiple": r_multiple,
                            "order_id": partial_order.get("id", "test")
                        }
                    })

            # Check if TP3 is reached
            if reached_tp2 and tp3_price is not None and current_price >= tp3_price:
                logger.info(f"TP3 reached at ${current_price:.2f} (3R profit)")

                # Close remaining position at TP3
                if quantity > 0:
                    logger.info(f"Taking final profit: Selling {quantity:.8f} at ${current_price:.2f}")

                    # Execute final sell
                    order = self.exchange.create_market_sell_order(self.symbol, quantity)

                    # Record trade in history
                    self.trade_history.append({
                        "entry": self.current_position,
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "take_profit_tp3",
                            "pnl_percent": pnl_percent,
                            "r_multiple": r_multiple,
                            "order_id": order.get("id", "test")
                        }
                    })

                    # Get the symbol from the position before removing it
                    position_symbol = self.current_position.get("symbol", self.symbol)

                    # Remove from open positions list
                    if self.current_position in self.open_positions:
                        self.open_positions.remove(self.current_position)

                    # Remove from tracked symbols
                    if position_symbol in self.position_symbols:
                        self.position_symbols.remove(position_symbol)

                    # Clear current position
                    self.current_position = None

                    logger.info(f"Position closed with {pnl_percent:.2f}% profit ({r_multiple:.2f}R)")
                    logger.info(f"Total open positions: {len(self.open_positions)}/{self.max_positions}")
                    return order

            # Check if breakeven threshold is reached
            if not reached_breakeven and r_multiple >= self.breakeven_r_threshold:
                self.current_position["reached_breakeven"] = True
                reached_breakeven = True

                # Move stop loss to breakeven
                self.current_position["stop_loss_price"] = breakeven_price
                stop_loss_price = breakeven_price

                logger.info(f"Moving stop loss to breakeven: ${breakeven_price:.2f}")

            # Check if trailing stop should be activated
            # When the price moves 0.5% in favor of the trade, activate trailing stop
            price_movement_pct = (current_price - entry_price) / entry_price
            if not trailing_stop_activated and price_movement_pct >= self.trailing_stop_activation_pct:
                self.current_position["trailing_stop_activated"] = True
                trailing_stop_activated = True
                logger.info(f"Trailing stop activated at ${current_price:.2f} (price moved {price_movement_pct*100:.2f}% in favor)")

            # Update trailing stop if activated
            if trailing_stop_activated:
                # Calculate new trailing stop level
                # Move the stop by 0.25% when price moves 0.5% favorably
                ideal_trailing_stop = self.last_high_price * (1 - self.trailing_stop_step_pct)

                # Only move stop loss up, never down
                if ideal_trailing_stop > trailing_stop_price:
                    self.current_position["trailing_stop_price"] = ideal_trailing_stop
                    trailing_stop_price = ideal_trailing_stop
                    logger.info(f"Trailing stop updated to ${trailing_stop_price:.2f} (0.25% below high of ${self.last_high_price:.2f})")

            # Check stop loss (use trailing stop if activated)
            stop_loss_triggered = current_price <= stop_loss_price

            if stop_loss_triggered and quantity > 0:
                # Stop loss triggered
                logger.info(f"Stop loss triggered at ${current_price:.2f}: {pnl_percent:.2f}% {r_multiple:.2f}R")

                order = self.exchange.create_market_sell_order(self.symbol, quantity)

                # Record trade in history
                self.trade_history.append({
                    "entry": self.current_position,
                    "exit": {
                        "price": current_price,
                        "timestamp": datetime.datetime.now().isoformat(),
                        "reason": "stop_loss",
                        "pnl_percent": pnl_percent,
                        "r_multiple": r_multiple,
                        "order_id": order.get("id", "test")
                    }
                })

                # Get the symbol from the position before removing it
                position_symbol = self.current_position.get("symbol", self.symbol)

                # Remove from open positions list
                if self.current_position in self.open_positions:
                    self.open_positions.remove(self.current_position)

                # Remove from tracked symbols
                if position_symbol in self.position_symbols:
                    self.position_symbols.remove(position_symbol)

                # Clear current position
                self.current_position = None

                logger.info(f"Position closed with {pnl_percent:.2f}% {'profit' if pnl_percent > 0 else 'loss'}")
                logger.info(f"Total open positions: {len(self.open_positions)}/{self.max_positions}")
                return order

        elif side == "sell":
            # For short positions, profit/loss is reversed
            pnl_percent = (entry_price - current_price) / entry_price * 100

            # Calculate R-multiple (how many times the initial risk)
            r_multiple = (entry_price - current_price) / risk_value if risk_value > 0 else 0

            logger.info(f"Current position: SELL at ${entry_price:.2f}, current P&L: {pnl_percent:.2f}% ({r_multiple:.2f}R)")

            # Update last low price for trailing stop
            if current_price < self.last_low_price or self.last_low_price == 0:
                self.last_low_price = current_price

            # Check for smart exit conditions
            if self.enable_smart_exit and self.last_market_data:
                exit_signal, reason = self.check_smart_exit_conditions(self.last_market_data, side)

                if exit_signal and quantity > 0:
                    logger.info(f"Smart exit triggered: {reason}")

                    # Execute full exit
                    order = self.exchange.create_market_buy_order(self.symbol, quantity * current_price)

                    # Record trade in history
                    self.trade_history.append({
                        "entry": self.current_position,
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": f"smart_exit_{reason}",
                            "pnl_percent": pnl_percent,
                            "r_multiple": r_multiple,
                            "order_id": order.get("id", "test")
                        }
                    })

                    # Get the symbol from the position before removing it
                    position_symbol = self.current_position.get("symbol", self.symbol)

                    # Remove from open positions list
                    if self.current_position in self.open_positions:
                        self.open_positions.remove(self.current_position)

                    # Remove from tracked symbols
                    if position_symbol in self.position_symbols:
                        self.position_symbols.remove(position_symbol)

                    # Apply compounding mode if enabled
                    if self.compounding_mode and pnl_percent > 0:
                        # This was a winning trade
                        self.last_trade_was_winner = True

                        # Reset consecutive losses counter
                        self.consecutive_losses = 0

                        # Calculate profit amount
                        trade_amount = self.current_position.get("current_position_trade_amount", self.current_trade_amount)
                        profit_amount = trade_amount * (pnl_percent / 100)

                        # Retain 50% of profit and increase lot size
                        retained_profit = profit_amount * (self.profit_retention_pct / 100)

                        # Update current trade amount
                        self.current_trade_amount = self.current_trade_amount + retained_profit

                        logger.info(f"Compounding mode: Retaining {self.profit_retention_pct}% of profit (${retained_profit:.2f})")
                        logger.info(f"New base trade amount: ${self.current_trade_amount:.2f}")
                    else:
                        # This was a losing trade or compounding is disabled
                        self.last_trade_was_winner = False

                        # If it's a losing trade, increment consecutive losses counter
                        if pnl_percent < 0:
                            self.consecutive_losses += 1
                            logger.warning(f"Consecutive losses: {self.consecutive_losses}")

                            # Check if we need to freeze trading
                            if self.consecutive_losses >= self.consecutive_losses_limit:
                                self.freeze_trading()

                    # Clear current position
                    self.current_position = None

                    logger.info(f"Position closed with {pnl_percent:.2f}% {'profit' if pnl_percent > 0 else 'loss'}")
                    logger.info(f"Total open positions: {len(self.open_positions)}/{self.max_positions}")
                    return order

            # Check if TP1 is reached
            if not reached_tp1 and tp1_price is not None and current_price <= tp1_price:
                self.current_position["reached_tp1"] = True
                reached_tp1 = True
                logger.info(f"TP1 reached at ${current_price:.2f} (1R profit)")

                # Partial take profit at TP1 (close 50% of position)
                if quantity > 0:
                    partial_quantity = quantity * self.tp1_exit_percentage
                    logger.info(f"Taking partial profit: Buying back {partial_quantity:.8f} at ${current_price:.2f}")

                    # Execute partial buy
                    partial_order = self.exchange.create_market_buy_order(self.symbol, partial_quantity * current_price)

                    # Update position quantity
                    self.current_position["quantity"] = quantity - partial_quantity
                    quantity = self.current_position["quantity"]

                    # Move stop loss to breakeven
                    self.current_position["stop_loss_price"] = entry_price
                    stop_loss_price = entry_price
                    logger.info(f"Moving stop loss to breakeven: ${entry_price:.2f}")

                    # Record partial profit in history
                    self.trade_history.append({
                        "entry": {**self.current_position, "quantity": partial_quantity},
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "partial_take_profit_tp1",
                            "pnl_percent": pnl_percent,
                            "r_multiple": r_multiple,
                            "order_id": partial_order.get("id", "test")
                        }
                    })

            # Check if TP2 is reached
            if reached_tp1 and not reached_tp2 and tp2_price is not None and current_price <= tp2_price:
                self.current_position["reached_tp2"] = True
                reached_tp2 = True
                logger.info(f"TP2 reached at ${current_price:.2f} (2R profit)")

                # Partial take profit at TP2 (close 1/2 of remaining position)
                if quantity > 0:
                    partial_quantity = quantity / 2
                    logger.info(f"Taking partial profit: Buying back {partial_quantity:.8f} at ${current_price:.2f}")

                    # Execute partial buy
                    partial_order = self.exchange.create_market_buy_order(self.symbol, partial_quantity * current_price)

                    # Update position quantity
                    self.current_position["quantity"] = quantity - partial_quantity
                    quantity = self.current_position["quantity"]

                    # Record partial profit in history
                    self.trade_history.append({
                        "entry": {**self.current_position, "quantity": partial_quantity},
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "partial_take_profit_tp2",
                            "pnl_percent": pnl_percent,
                            "r_multiple": r_multiple,
                            "order_id": partial_order.get("id", "test")
                        }
                    })

            # Check if TP3 is reached
            if reached_tp2 and tp3_price is not None and current_price <= tp3_price:
                logger.info(f"TP3 reached at ${current_price:.2f} (3R profit)")

                # Close remaining position at TP3
                if quantity > 0:
                    logger.info(f"Taking final profit: Buying back {quantity:.8f} at ${current_price:.2f}")

                    # Execute final buy
                    order = self.exchange.create_market_buy_order(self.symbol, quantity * current_price)

                    # Record trade in history
                    self.trade_history.append({
                        "entry": self.current_position,
                        "exit": {
                            "price": current_price,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "reason": "take_profit_tp3",
                            "pnl_percent": pnl_percent,
                            "r_multiple": r_multiple,
                            "order_id": order.get("id", "test")
                        }
                    })

                    # Get the symbol from the position before removing it
                    position_symbol = self.current_position.get("symbol", self.symbol)

                    # Remove from open positions list
                    if self.current_position in self.open_positions:
                        self.open_positions.remove(self.current_position)

                    # Remove from tracked symbols
                    if position_symbol in self.position_symbols:
                        self.position_symbols.remove(position_symbol)

                    # Apply compounding mode if enabled
                    if self.compounding_mode and pnl_percent > 0:
                        # This was a winning trade
                        self.last_trade_was_winner = True

                        # Reset consecutive losses counter
                        self.consecutive_losses = 0

                        # Calculate profit amount
                        trade_amount = self.current_position.get("current_position_trade_amount", self.current_trade_amount)
                        profit_amount = trade_amount * (pnl_percent / 100)

                        # Retain 50% of profit and increase lot size
                        retained_profit = profit_amount * (self.profit_retention_pct / 100)

                        # Update current trade amount
                        self.current_trade_amount = self.current_trade_amount + retained_profit

                        logger.info(f"Compounding mode: Retaining {self.profit_retention_pct}% of profit (${retained_profit:.2f})")
                        logger.info(f"New base trade amount: ${self.current_trade_amount:.2f}")
                    else:
                        # This was a losing trade or compounding is disabled
                        self.last_trade_was_winner = False

                        # If it's a losing trade, increment consecutive losses counter
                        if pnl_percent < 0:
                            self.consecutive_losses += 1
                            logger.warning(f"Consecutive losses: {self.consecutive_losses}")

                            # Check if we need to freeze trading
                            if self.consecutive_losses >= self.consecutive_losses_limit:
                                self.freeze_trading()

                    # Clear current position
                    self.current_position = None

                    logger.info(f"Position closed with {pnl_percent:.2f}% profit ({r_multiple:.2f}R)")
                    logger.info(f"Total open positions: {len(self.open_positions)}/{self.max_positions}")
                    return order

            # Check if breakeven threshold is reached
            if not reached_breakeven and r_multiple >= self.breakeven_r_threshold:
                self.current_position["reached_breakeven"] = True
                reached_breakeven = True

                # Move stop loss to breakeven
                self.current_position["stop_loss_price"] = breakeven_price
                stop_loss_price = breakeven_price

                logger.info(f"Moving stop loss to breakeven: ${breakeven_price:.2f}")

            # Check if trailing stop should be activated
            # When the price moves 0.5% in favor of the trade, activate trailing stop
            price_movement_pct = (entry_price - current_price) / entry_price
            if not trailing_stop_activated and price_movement_pct >= self.trailing_stop_activation_pct:
                self.current_position["trailing_stop_activated"] = True
                trailing_stop_activated = True
                logger.info(f"Trailing stop activated at ${current_price:.2f} (price moved {price_movement_pct*100:.2f}% in favor)")

            # Update trailing stop if activated
            if trailing_stop_activated:
                # Calculate new trailing stop level
                # Move the stop by 0.25% when price moves 0.5% favorably
                ideal_trailing_stop = self.last_low_price * (1 + self.trailing_stop_step_pct)

                # Only move stop loss down, never up
                if ideal_trailing_stop < trailing_stop_price:
                    self.current_position["trailing_stop_price"] = ideal_trailing_stop
                    trailing_stop_price = ideal_trailing_stop
                    logger.info(f"Trailing stop updated to ${trailing_stop_price:.2f} (0.25% above low of ${self.last_low_price:.2f})")

            # Check stop loss (use trailing stop if activated)
            stop_loss_triggered = current_price >= stop_loss_price

            if stop_loss_triggered and quantity > 0:
                # Stop loss triggered
                logger.info(f"Stop loss triggered at ${current_price:.2f}: {pnl_percent:.2f}% {r_multiple:.2f}R")

                order = self.exchange.create_market_buy_order(self.symbol, quantity * current_price)

                # Record trade in history
                self.trade_history.append({
                    "entry": self.current_position,
                    "exit": {
                        "price": current_price,
                        "timestamp": datetime.datetime.now().isoformat(),
                        "reason": "stop_loss",
                        "pnl_percent": pnl_percent,
                        "r_multiple": r_multiple,
                        "order_id": order.get("id", "test")
                    }
                })

                # Get the symbol from the position before removing it
                position_symbol = self.current_position.get("symbol", self.symbol)

                # Remove from open positions list
                if self.current_position in self.open_positions:
                    self.open_positions.remove(self.current_position)

                # Remove from tracked symbols
                if position_symbol in self.position_symbols:
                    self.position_symbols.remove(position_symbol)

                # Apply compounding mode if enabled
                if self.compounding_mode and pnl_percent > 0:
                    # This was a winning trade
                    self.last_trade_was_winner = True

                    # Reset consecutive losses counter
                    self.consecutive_losses = 0

                    # Calculate profit amount
                    trade_amount = self.current_position.get("current_position_trade_amount", self.current_trade_amount)
                    profit_amount = trade_amount * (pnl_percent / 100)

                    # Retain 50% of profit and increase lot size
                    retained_profit = profit_amount * (self.profit_retention_pct / 100)

                    # Update current trade amount
                    self.current_trade_amount = self.current_trade_amount + retained_profit

                    logger.info(f"Compounding mode: Retaining {self.profit_retention_pct}% of profit (${retained_profit:.2f})")
                    logger.info(f"New base trade amount: ${self.current_trade_amount:.2f}")
                else:
                    # This was a losing trade or compounding is disabled
                    self.last_trade_was_winner = False

                    # If it's a losing trade, increment consecutive losses counter
                    if pnl_percent < 0:
                        self.consecutive_losses += 1
                        logger.warning(f"Consecutive losses: {self.consecutive_losses}")

                        # Check if we need to freeze trading
                        if self.consecutive_losses >= self.consecutive_losses_limit:
                            self.freeze_trading()

                # Clear current position
                self.current_position = None

                logger.info(f"Position closed with {pnl_percent:.2f}% {'profit' if pnl_percent > 0 else 'loss'}")
                logger.info(f"Total open positions: {len(self.open_positions)}/{self.max_positions}")
                return order

        return None

    def is_trading_frozen(self):
        """
        Check if trading is frozen due to consecutive losses

        Returns:
            bool: True if trading is frozen, False otherwise
        """
        try:
            if self.trading_frozen_until is None:
                return False

            current_time = datetime.datetime.now()

            if current_time < self.trading_frozen_until:
                time_remaining = (self.trading_frozen_until - current_time).total_seconds() / 60
                logger.warning(f"Trading is frozen for {time_remaining:.1f} more minutes due to consecutive losses")
                return True
            else:
                # Reset trading freeze if the freeze period has expired
                self.trading_frozen_until = None
                logger.info("Trading freeze period has expired. Trading is now allowed.")
                return False
        except Exception as e:
            logger.error(f"Error checking if trading is frozen: {e}")
            return False  # Conservative approach: allow trading if error

    def freeze_trading(self):
        """
        Freeze trading for the specified duration due to consecutive losses
        """
        try:
            current_time = datetime.datetime.now()
            self.trading_frozen_until = current_time + datetime.timedelta(minutes=self.trading_freeze_duration)
            logger.warning(f"Trading frozen for {self.trading_freeze_duration} minutes due to {self.consecutive_losses} consecutive losses")
            logger.warning(f"Trading will resume at {self.trading_frozen_until}")
        except Exception as e:
            logger.error(f"Error freezing trading: {e}")

    def save_trade_history(self):
        """
        Save trade history to file
        """
        try:
            # Create logs directory if it doesn't exist
            os.makedirs("logs", exist_ok=True)

            # Get current date in YYYYMMDD format
            current_date = datetime.datetime.now().strftime('%Y%m%d')
            history_file = f"logs/trade_history_{current_date}.json"

            with open(history_file, 'w') as f:
                json.dump(self.trade_history, f, indent=4)

            logger.info(f"Trade history saved to {history_file}")
        except Exception as e:
            logger.error(f"Error saving trade history: {e}")

    def run(self, max_iterations=10):
        """
        Run the trading bot with crash recovery

        Args:
            max_iterations (int or float): Maximum number of iterations to run
        """
        logger.info(f"Starting SimpleAITradingBot in {'TEST' if self.test_mode else 'LIVE'} mode")
        logger.info(f"Trading {self.symbol} with dynamic trade amount based on balance")
        logger.info(f"Stop Loss: {self.stop_loss*100}%, Take Profit: {self.take_profit*100}%")

        # Initialize crash recovery variables
        max_consecutive_errors = int(os.getenv("MAX_CONSECUTIVE_ERRORS", "3"))
        consecutive_errors = 0
        error_cooldown = int(os.getenv("ERROR_COOLDOWN", "30"))  # seconds
        last_error_time = None

        # Set up signal handlers for graceful shutdown
        def signal_handler(sig, frame):
            logger.warning(f"Received signal {sig}, initiating graceful shutdown...")
            self._save_bot_state("shutdown_by_signal")
            self.save_trade_history()
            sys.exit(0)

        # Register signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Create a state file to track bot state for crash recovery
        self._save_bot_state("starting")

        # Check for crash recovery
        recovery_performed = self._handle_crash_recovery()

        try:
            iteration = 1
            running = True

            while running:
                try:
                    # Display iteration info
                    if max_iterations == float('inf'):
                        logger.info(f"Iteration {iteration}/∞")
                    else:
                        logger.info(f"Iteration {iteration}/{max_iterations}")

                    # Check system health
                    if not self.memory_guard.check_system_health():
                        logger.warning("System health check failed. Waiting before next iteration.")
                        time.sleep(5)
                        continue

                    # Check API health
                    if not self.memory_guard.check_api_health(self.exchange):
                        logger.warning("API health check failed. Waiting before next iteration.")
                        time.sleep(5)
                        continue

                    # Check current price
                    current_price = self.check_price()

                    # Get fresh market data for position management
                    if self.current_position is not None:
                        self.last_market_data = self.get_market_data()

                    # Manage open positions
                    if self.current_position is not None:
                        self.manage_positions(current_price)

                    # Check if trading is frozen due to consecutive losses
                    if self.is_trading_frozen():
                        logger.warning("Trading is frozen due to consecutive losses. No new trades will be opened.")
                    # Check if we can open a new position (if we have less than max positions)
                    elif len(self.open_positions) < self.max_positions and self.symbol not in self.position_symbols:
                        # Check for low liquidity time (2:00 AM - 5:00 AM)
                        from analysis.indicators import is_low_liquidity_time
                        if is_low_liquidity_time():
                            logger.warning("Low liquidity time detected (2:00 AM - 5:00 AM). No new trades will be opened.")
                        else:
                            # Check for sudden volatility
                            market_data = self.get_market_data()
                            from analysis.volatility_detector import VolatilityDetector
                            volatility_detector = VolatilityDetector()
                            volatility_info = volatility_detector.check_volatility(self.symbol, market_data)

                            if volatility_info.get("is_volatile", False):
                                logger.warning(f"High volatility detected: {volatility_info.get('reason')}. No new trades will be opened.")
                            else:
                                # Check for false candles
                                from analysis.indicators import is_false_candle
                                historical = market_data.get("historical", [])
                                if len(historical) >= 2:
                                    open_prices = [candle.get("open", 0) for candle in historical]
                                    high_prices = [candle.get("high", 0) for candle in historical]
                                    low_prices = [candle.get("low", 0) for candle in historical]
                                    close_prices = [candle.get("close", 0) for candle in historical]

                                    if is_false_candle(open_prices, high_prices, low_prices, close_prices):
                                        logger.warning("False candle detected (>3% movement within a minute with immediate reversal). No new trades will be opened.")
                                    else:
                                        # Analyze market
                                        market_signal = self.analyze_market()

                                        # Execute trade if signal is generated
                                        self.execute_trade(market_signal)
                                else:
                                    # Not enough data to check for false candles
                                    # Analyze market
                                    market_signal = self.analyze_market()

                                    # Execute trade if signal is generated
                                    self.execute_trade(market_signal)
                    elif len(self.open_positions) >= self.max_positions:
                        logger.info(f"Maximum number of positions reached ({len(self.open_positions)}/{self.max_positions}). Not looking for new trades.")
                    elif self.symbol in self.position_symbols:
                        logger.info(f"Already have a position for {self.symbol}. Not looking for new trades on this symbol.")

                    # Save trade history periodically
                    if iteration % 5 == 0:
                        self.save_trade_history()

                    # Update entry and exit signals every minute
                    current_time = datetime.datetime.now()
                    if self.last_signal_update is None or (current_time - self.last_signal_update).total_seconds() >= self.signal_update_interval:
                        # Update market data and signals
                        if self.current_position is not None:
                            logger.info("Updating entry and exit signals based on live indicators...")
                            market_data = self.get_market_data()
                            self.last_market_data = market_data

                            # Check for smart exit conditions
                            if self.enable_smart_exit:
                                side = self.current_position.get("side", "buy")
                                exit_signal, reason = self.check_smart_exit_conditions(market_data, side)
                                if exit_signal:
                                    logger.info(f"Smart exit signal detected: {reason}")
                                    # The actual exit will be handled in the next manage_positions call

                        # Update timestamp
                        self.last_signal_update = current_time

                    # Save bot state after each successful iteration
                    self._save_bot_state(f"iteration_{iteration}")

                    # Reset consecutive errors counter after successful iteration
                    consecutive_errors = 0

                    # Check if we should continue
                    if max_iterations != float('inf') and iteration >= max_iterations:
                        running = False
                    else:
                        # Wait for next iteration
                        logger.info(f"Waiting {self.check_interval} seconds until next check...")
                        time.sleep(self.check_interval)
                        iteration += 1

                except Exception as loop_error:
                    # Handle exceptions within the main loop
                    logger.error(f"Error in main loop: {loop_error}")

                    # Record error for crash recovery
                    current_time = time.time()

                    # Check if this is a new error or a continuation of previous errors
                    if last_error_time is None or (current_time - last_error_time) > error_cooldown:
                        consecutive_errors = 1
                    else:
                        consecutive_errors += 1

                    last_error_time = current_time

                    # Save error state
                    self._save_bot_state(f"error_in_loop_{consecutive_errors}")

                    # Implement recovery strategy based on error count
                    if consecutive_errors >= max_consecutive_errors:
                        logger.critical(f"Too many consecutive errors ({consecutive_errors}). Performing emergency shutdown.")
                        self._save_bot_state("emergency_shutdown")

                        # Force memory cleanup before exit
                        if hasattr(self, 'memory_guard'):
                            self.memory_guard.force_memory_cleanup()

                        # Close any open positions if in test mode
                        if self.test_mode and self.current_position is not None:
                            try:
                                logger.warning("Attempting to close positions before emergency shutdown")
                                self.close_position(self.current_position, "Emergency shutdown")
                            except Exception as close_error:
                                logger.error(f"Failed to close position during emergency shutdown: {close_error}")

                        running = False
                    else:
                        # Wait longer between retries as error count increases
                        retry_wait = error_cooldown * consecutive_errors
                        logger.warning(f"Waiting {retry_wait} seconds before retry (error {consecutive_errors}/{max_consecutive_errors})...")
                        time.sleep(retry_wait)

                        # Try to optimize resources before continuing
                        if hasattr(self, 'memory_guard'):
                            self.memory_guard.optimize_resources()

            logger.info(f"Completed {max_iterations} iterations")

        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
            self._save_bot_state("stopped_by_user")
        except Exception as e:
            error_msg = f"Error: {e}"
            logger.error(error_msg)

            # Save error state
            self._save_bot_state(f"fatal_error")

            # Force memory cleanup before exit
            if hasattr(self, 'memory_guard'):
                self.memory_guard.force_memory_cleanup()

        finally:
            # Save trade history
            self.save_trade_history()

            # Stop margin monitoring
            if hasattr(self, 'margin_monitor'):
                self.margin_monitor.stop_monitoring()
                logger.info("Margin monitoring stopped")

            logger.info("Bot shutting down")

    def _handle_resource_alert(self, alert_type, value):
        """
        Handle resource alerts from MemoryGuard

        Args:
            alert_type (str): Type of alert ('cpu', 'ram', 'recovery_mode')
            value: Alert value
        """
        try:
            logger.warning(f"Resource alert: {alert_type} = {value}")

            # Record the alert in state tracker
            self.state_tracker.add_error(f"resource_{alert_type}", f"{alert_type.upper()} usage critical: {value}",
                                        {"type": alert_type, "value": value})

            # Take action based on alert type
            if alert_type == 'cpu' and value > 95:
                logger.critical(f"CPU usage critically high ({value}%). Taking emergency action.")
                self._handle_critical_resource_usage('cpu')

            elif alert_type == 'ram' and value > 95:
                logger.critical(f"RAM usage critically high ({value}%). Taking emergency action.")
                self._handle_critical_resource_usage('ram')

            elif alert_type == 'recovery_mode' and value:
                logger.warning("Entering resource recovery mode")
                self.state_tracker.update_status("resource_recovery_mode")

                # Pause non-essential operations
                self._pause_non_essential_operations()
        except Exception as e:
            logger.error(f"Error handling resource alert: {e}")

    def _handle_critical_resource_usage(self, resource_type):
        """
        Handle critical resource usage

        Args:
            resource_type (str): Type of resource ('cpu' or 'ram')
        """
        try:
            # Update state
            self.state_tracker.update_status(f"critical_{resource_type}_usage")

            # Force memory cleanup
            self.memory_guard.force_memory_cleanup()

            # Pause non-essential operations
            self._pause_non_essential_operations()

            # Create a backup of the current state
            self.state_tracker.create_backup(f"critical_{resource_type}")

            logger.warning(f"Critical {resource_type} usage handled")
        except Exception as e:
            logger.error(f"Error handling critical resource usage: {e}")

    def _handle_critical_margin(self, symbol, margin_ratio):
        """
        Handle critical margin events

        Args:
            symbol (str): Trading pair symbol
            margin_ratio (float): Current margin ratio
        """
        logger.critical(f"CRITICAL MARGIN ALERT: {symbol} margin ratio at {margin_ratio:.2f}%")

        # Save state for potential recovery
        self.state_tracker.update_status(f"critical_margin_{symbol}")

        # Close positions to prevent liquidation
        try:
            if symbol in self.position_symbols:
                logger.critical(f"Emergency closing position for {symbol} to prevent liquidation")
                self.close_position(symbol, "emergency_margin")
        except Exception as e:
            logger.error(f"Error closing position during critical margin event: {e}")

        # Notify user (would be implemented in a real system)
        logger.critical("URGENT: Critical margin level reached. Emergency position closure initiated.")

    def _handle_warning_margin(self, symbol, margin_ratio):
        """
        Handle warning margin events

        Args:
            symbol (str): Trading pair symbol
            margin_ratio (float): Current margin ratio
        """
        logger.warning(f"MARGIN WARNING: {symbol} margin ratio at {margin_ratio:.2f}%")

        # Save state
        self.state_tracker.update_status(f"warning_margin_{symbol}")

        # Reduce position size if possible
        try:
            if symbol in self.position_symbols:
                # Find the position
                position = next((p for p in self.open_positions if p.get("symbol") == symbol), None)

                if position:
                    # Calculate a safer position size (reduce by 25%)
                    current_size = position.get("size", 0)
                    reduced_size = current_size * 0.75

                    logger.warning(f"Reducing position size for {symbol} from {current_size} to {reduced_size}")

                    # This would call the exchange to reduce position size
                    # In a real implementation, this would use the margin_manager to adjust the position
                    pass
        except Exception as e:
            logger.error(f"Error reducing position during margin warning: {e}")

    def _pause_non_essential_operations(self):
        """
        Pause non-essential operations during resource recovery
        """
        try:
            # Reduce check interval to give system time to recover
            original_interval = self.check_interval
            self.check_interval = max(self.check_interval * 2, 120)  # At least 2 minutes

            logger.info(f"Increased check interval from {original_interval}s to {self.check_interval}s")

            # Disable compounding mode temporarily
            self.compounding_mode = False

            # Reduce parallel positions
            self.max_positions = 1

            logger.info("Non-essential operations paused")
        except Exception as e:
            logger.error(f"Error pausing non-essential operations: {e}")

    def _resume_normal_operations(self):
        """
        Resume normal operations after recovery
        """
        try:
            # Restore original check interval
            self.check_interval = int(os.getenv("CHECK_INTERVAL", "60"))

            # Re-enable compounding mode if it was enabled in settings
            self.compounding_mode = os.getenv("COMPOUNDING_MODE", "true").lower() == "true"

            # Restore parallel positions
            self.max_positions = int(os.getenv("MAX_POSITIONS", "3"))

            logger.info("Normal operations resumed")
            self.state_tracker.update_status("normal_operation")
        except Exception as e:
            logger.error(f"Error resuming normal operations: {e}")

    def _save_bot_state(self, state):
        """
        Save bot state to file for crash recovery

        Args:
            state (str): Current bot state
        """
        try:
            # Update state tracker
            self.state_tracker.update_status(state)

            # Update performance metrics
            if hasattr(self, 'memory_guard'):
                cpu_usage = self.memory_guard.get_current_cpu_usage()
                ram_usage = self.memory_guard.get_current_ram_usage()
                self.state_tracker.update_performance(cpu_usage, ram_usage)

            # Update positions in state tracker
            for position in self.open_positions:
                position_id = position.get("id")
                if position_id:
                    # Check if position already exists in state tracker
                    existing_positions = self.state_tracker.get_open_positions()
                    position_exists = any(p.get("id") == position_id for p in existing_positions)

                    if not position_exists:
                        # Add new position
                        self.state_tracker.add_position(position)

            # Update settings in state tracker
            settings = {
                "symbol": self.symbol,
                "test_mode": self.test_mode,
                "use_testnet": self.use_testnet,
                "check_interval": self.check_interval,
                "trade_amount": self.trade_amount,
                "stop_loss": self.stop_loss,
                "take_profit": self.take_profit,
                "max_trades_per_day": self.max_trades_per_day,
                "compounding_mode": self.compounding_mode,
                "max_positions": self.max_positions
            }
            self.state_tracker.update_settings(settings)

            # Create logs directory if it doesn't exist
            os.makedirs("logs", exist_ok=True)

            # Create state file (legacy format for backward compatibility)
            state_file = "logs/bot_state.json"

            # Convert position_symbols set to list for JSON serialization
            position_symbols_list = list(self.position_symbols) if isinstance(self.position_symbols, set) else self.position_symbols

            # Prepare state data
            state_data = {
                "timestamp": datetime.datetime.now().isoformat(),
                "state": state,
                "symbol": self.symbol,
                "test_mode": self.test_mode,
                "open_positions": self.open_positions,
                "position_symbols": position_symbols_list,
                "consecutive_losses": self.consecutive_losses,
                "trading_frozen_until": self.trading_frozen_until.isoformat() if self.trading_frozen_until else None
            }

            # Save state to file
            with open(state_file, 'w') as f:
                json.dump(state_data, f, indent=4, default=lambda o: str(o))

            logger.debug(f"Bot state saved: {state}")
        except Exception as e:
            logger.error(f"Error saving bot state: {e}")

    def _handle_crash_recovery(self):
        """
        Handle crash recovery on startup

        Returns:
            bool: True if recovery was successful, False otherwise
        """
        try:
            logger.info("Checking for crash recovery...")

            # Get current state
            state = self.state_tracker.get_state()

            # Check if there was a previous crash
            last_status = state.get("status", "")
            if "critical" in last_status or "error" in last_status:
                logger.warning(f"Detected previous crash with status: {last_status}")

                # Restore settings
                settings = state.get("settings", {})
                if settings:
                    # Restore basic settings
                    if "symbol" in settings:
                        self.symbol = settings["symbol"]
                    if "check_interval" in settings:
                        self.check_interval = settings["check_interval"]
                    if "trade_amount" in settings:
                        self.trade_amount = settings["trade_amount"]
                    if "stop_loss" in settings:
                        self.stop_loss = settings["stop_loss"]
                    if "take_profit" in settings:
                        self.take_profit = settings["take_profit"]

                    logger.info(f"Restored settings from previous state")

                # Restore open positions
                open_positions = self.state_tracker.get_open_positions()
                if open_positions:
                    logger.warning(f"Found {len(open_positions)} open positions from previous session")

                    # Restore positions
                    self.open_positions = open_positions
                    self.position_symbols = set(p.get("symbol", "").split("/")[0] for p in open_positions if "symbol" in p)

                    logger.info(f"Restored {len(open_positions)} open positions")

                # Update state
                self.state_tracker.update_status("recovered_from_crash")

                # Create a backup of the recovery state
                self.state_tracker.create_backup("crash_recovery")

                logger.info("Crash recovery completed successfully")
                return True
            else:
                logger.info("No crash recovery needed")
                return False
        except Exception as e:
            logger.error(f"Error during crash recovery: {e}")
            return False

def main():
    """
    Main function
    """
    # Load environment variables
    load_dotenv()

    # Import BotConfig to verify Binance-only configuration
    from core.config import BotConfig

    # Verify that only Binance API is configured
    if not BotConfig.verify_binance_only():
        logger.critical("Coinbase API references found in configuration. This bot uses Binance API only.")
        logger.critical("Please remove any Coinbase API keys or references from your configuration.")
        print("\n⚠️ ERROR: Coinbase API references found in configuration.")
        print("This trading bot uses Binance API only. Please remove any Coinbase API keys or references.")
        print("For security, the bot will not start until this is resolved.\n")
        return

    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="AI Trading Bot with Isolated Margin Support")
    parser.add_argument("--symbol", type=str, default="BTC/USDT", help="Trading pair symbol")
    parser.add_argument("--test", action="store_true", help="Run in test mode (no real trades)")
    parser.add_argument("--testnet", action="store_true", help="Use Binance Testnet API")
    parser.add_argument("--live", action="store_true", help="Run in live mode (use real money)")
    parser.add_argument("--iterations", type=int, default=10, help="Number of iterations to run (0 for infinite)")
    parser.add_argument("--force-trade", action="store_true", help="Force a trade for testing")
    parser.add_argument("--cpu-threshold", type=int, default=85, help="CPU usage threshold percentage")
    parser.add_argument("--ram-threshold", type=int, default=75, help="RAM usage threshold percentage")
    parser.add_argument("--check-api", action="store_true", help="Check Binance API connection and exit")
    args = parser.parse_args()

    # If --check-api is specified, test the Binance API connection and exit
    if args.check_api:
        print("Testing Binance API connection...")
        try:
            # Get API keys
            api_key = os.getenv("BINANCE_API_KEY")
            api_secret = os.getenv("BINANCE_API_SECRET")

            # Initialize Binance API
            from exchange.binance_api import BinanceAPI
            binance_api = BinanceAPI(api_key, api_secret, use_testnet=args.testnet)

            # Test API endpoints
            results = binance_api.test_binance_api_endpoints()

            # Print results
            print("\nBinance API Connection Test Results:")
            for endpoint, success in results.items():
                if success:
                    print(f"✅ {endpoint.upper()} endpoint: Connected")
                else:
                    print(f"❌ {endpoint.upper()} endpoint: Failed")

            # Check API latency
            latency = binance_api.check_api_latency()
            print(f"\nAPI latency: {latency:.2f}ms")

            if all(results.values()):
                print("\n✅ All Binance API tests passed. You're good to go!")
            else:
                print("\n❌ Some Binance API tests failed. Please check your configuration.")

            return
        except Exception as e:
            print(f"\n❌ Error testing Binance API: {e}")
            return

    # Check for live mode confirmation
    if args.live and not args.test:
        print("WARNING: You are about to run the bot in LIVE mode with REAL MONEY!")
        print(f"Trading pair: {args.symbol}")
        print(f"Iterations: {'Infinite' if args.iterations == 0 else args.iterations}")
        confirmation = input("Are you sure you want to continue? (yes/no): ")
        if confirmation.lower() != "yes":
            print("Live trading cancelled.")
            return

    # Default to test mode if neither --test nor --live is specified
    test_mode = not args.live if args.live else True

    # Get API keys based on whether we're using testnet or not
    api_key = None
    api_secret = None

    if args.testnet:
        # Use testnet API keys
        api_key = os.getenv("BINANCE_TESTNET_API_KEY")
        api_secret = os.getenv("BINANCE_TESTNET_SECRET_KEY")
        logger.info("Using Binance Testnet API keys")
    else:
        # Use regular API keys
        api_key = os.getenv("BINANCE_API_KEY")
        api_secret = os.getenv("BINANCE_API_SECRET")

    # Initialize and run bot
    bot = SimpleAITradingBot(symbol=args.symbol, test_mode=test_mode, use_testnet=args.testnet)

    # Set force trade flag if specified
    if args.force_trade:
        bot.force_trade = True

    # Set resource monitoring thresholds
    if hasattr(bot, 'memory_guard'):
        bot.memory_guard.cpu_threshold = args.cpu_threshold
        bot.memory_guard.ram_threshold = args.ram_threshold
        logger.info(f"Resource monitoring thresholds set to CPU: {args.cpu_threshold}%, RAM: {args.ram_threshold}%")

    # Run the bot with specified iterations (0 means infinite)
    max_iterations = args.iterations if args.iterations > 0 else float('inf')
    bot.run(max_iterations=max_iterations)

if __name__ == "__main__":
    main()
