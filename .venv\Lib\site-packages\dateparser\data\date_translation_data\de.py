info = {
    "name": "de",
    "date_order": "DMY",
    "january": [
        "jan",
        "januar",
        "<PERSON><PERSON><PERSON>"
    ],
    "february": [
        "feb",
        "februar",
        "Feber"
    ],
    "march": [
        "mär",
        "märz",
        "Mrz"
    ],
    "april": [
        "apr",
        "april"
    ],
    "may": [
        "mai"
    ],
    "june": [
        "jun",
        "juni"
    ],
    "july": [
        "jul",
        "juli"
    ],
    "august": [
        "aug",
        "august"
    ],
    "september": [
        "sep",
        "september",
        "Sept"
    ],
    "october": [
        "okt",
        "oktober"
    ],
    "november": [
        "nov",
        "november"
    ],
    "december": [
        "dez",
        "dezember"
    ],
    "monday": [
        "mo",
        "montag",
        "Mon"
    ],
    "tuesday": [
        "di",
        "dienstag",
        "Die"
    ],
    "wednesday": [
        "mi",
        "mittwoch",
        "Mit"
    ],
    "thursday": [
        "do",
        "donnerstag",
        "<PERSON>"
    ],
    "friday": [
        "fr",
        "freitag",
        "Fre"
    ],
    "saturday": [
        "sa",
        "samstag",
        "Sam"
    ],
    "sunday": [
        "so",
        "sonntag",
        "Son"
    ],
    "am": [
        "vorm"
    ],
    "pm": [
        "nachm"
    ],
    "year": [
        "j",
        "jahr",
        "Jahre",
        "Jahren"
    ],
    "month": [
        "m",
        "monat",
        "Monate",
        "Monaten"
    ],
    "week": [
        "w",
        "woche",
        "Wochen"
    ],
    "day": [
        "tag",
        "Tage",
        "Tagen"
    ],
    "hour": [
        "std",
        "stunde",
        "Stunden"
    ],
    "minute": [
        "min",
        "minute",
        "Minuten"
    ],
    "second": [
        "sek",
        "sekunde",
        "Sekunden"
    ],
    "relative-type": {
        "0 day ago": [
            "heute"
        ],
        "0 hour ago": [
            "in dieser stunde"
        ],
        "0 minute ago": [
            "in dieser minute"
        ],
        "0 month ago": [
            "diesen monat"
        ],
        "0 second ago": [
            "jetzt"
        ],
        "0 week ago": [
            "diese woche"
        ],
        "0 year ago": [
            "dieses jahr"
        ],
        "1 day ago": [
            "gestern"
        ],
        "1 month ago": [
            "letzten monat"
        ],
        "1 week ago": [
            "letzte woche"
        ],
        "1 year ago": [
            "letztes jahr"
        ],
        "in 1 day": [
            "morgen"
        ],
        "in 1 month": [
            "nächsten monat"
        ],
        "in 1 week": [
            "nächste woche"
        ],
        "in 1 year": [
            "nächstes jahr"
        ],
        "2 day ago": [
            "vorgestern"
        ],
        "in 2 day": [
            "übermorgen"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "vor (\\d+[.,]?\\d*) tag",
            "vor (\\d+[.,]?\\d*) tagen"
        ],
        "\\1 hour ago": [
            "vor (\\d+[.,]?\\d*) std",
            "vor (\\d+[.,]?\\d*) stunde",
            "vor (\\d+[.,]?\\d*) stunden",
            "vor (\\d+[.,]?\\d*)\\s*h"
        ],
        "\\1 minute ago": [
            "vor (\\d+[.,]?\\d*) m",
            "vor (\\d+[.,]?\\d*) min",
            "vor (\\d+[.,]?\\d*) minute",
            "vor (\\d+[.,]?\\d*) minuten",
            "vor (\\d+[.,]?\\d*)\\s*m"
        ],
        "\\1 month ago": [
            "vor (\\d+[.,]?\\d*) monat",
            "vor (\\d+[.,]?\\d*) monaten"
        ],
        "\\1 second ago": [
            "vor (\\d+[.,]?\\d*) s",
            "vor (\\d+[.,]?\\d*) sek",
            "vor (\\d+[.,]?\\d*) sekunde",
            "vor (\\d+[.,]?\\d*) sekunden",
            "vor (\\d+[.,]?\\d*)\\s*s"
        ],
        "\\1 week ago": [
            "vor (\\d+[.,]?\\d*) wo",
            "vor (\\d+[.,]?\\d*) woche",
            "vor (\\d+[.,]?\\d*) wochen"
        ],
        "\\1 year ago": [
            "vor (\\d+[.,]?\\d*) jahr",
            "vor (\\d+[.,]?\\d*) jahren"
        ],
        "in \\1 day": [
            "in (\\d+[.,]?\\d*) tag",
            "in (\\d+[.,]?\\d*) tagen"
        ],
        "in \\1 hour": [
            "in (\\d+[.,]?\\d*) std",
            "in (\\d+[.,]?\\d*) stunde",
            "in (\\d+[.,]?\\d*) stunden"
        ],
        "in \\1 minute": [
            "in (\\d+[.,]?\\d*) m",
            "in (\\d+[.,]?\\d*) min",
            "in (\\d+[.,]?\\d*) minute",
            "in (\\d+[.,]?\\d*) minuten"
        ],
        "in \\1 month": [
            "in (\\d+[.,]?\\d*) monat",
            "in (\\d+[.,]?\\d*) monaten"
        ],
        "in \\1 second": [
            "in (\\d+[.,]?\\d*) s",
            "in (\\d+[.,]?\\d*) sek",
            "in (\\d+[.,]?\\d*) sekunde",
            "in (\\d+[.,]?\\d*) sekunden"
        ],
        "in \\1 week": [
            "in (\\d+[.,]?\\d*) wo",
            "in (\\d+[.,]?\\d*) woche",
            "in (\\d+[.,]?\\d*) wochen"
        ],
        "in \\1 year": [
            "in (\\d+[.,]?\\d*) jahr",
            "in (\\d+[.,]?\\d*) jahren"
        ]
    },
    "locale_specific": {
        "de-AT": {
            "name": "de-AT",
            "january": [
                "jän",
                "jänner"
            ]
        },
        "de-BE": {
            "name": "de-BE"
        },
        "de-CH": {
            "name": "de-CH"
        },
        "de-IT": {
            "name": "de-IT",
            "january": [
                "jän",
                "jänner"
            ]
        },
        "de-LI": {
            "name": "de-LI"
        },
        "de-LU": {
            "name": "de-LU"
        }
    },
    "skip": [
        "etwa",
        "uhr",
        "um",
        "und",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "vor"
    ],
    "in": [
        "in",
        "im"
    ],
    "simplifications": [
        {
            "einer": "1"
        },
        {
            "einem": "1"
        },
        {
            "ein": "1"
        },
        {
            "zwei": "2"
        },
        {
            "drei": "3"
        },
        {
            "vier": "4"
        },
        {
            "fünf": "5"
        },
        {
            "sechs": "6"
        },
        {
            "sieben": "7"
        },
        {
            "acht": "8"
        },
        {
            "neun": "9"
        },
        {
            "zehn": "10"
        },
        {
            "elf": "11"
        },
        {
            "zwölf": "12"
        }
    ]
}
