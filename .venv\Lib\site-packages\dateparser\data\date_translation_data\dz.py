info = {
    "name": "dz",
    "date_order": "YMD",
    "january": [
        "ཟླ་༡",
        "ཟླ་དངཔ་",
        "སྤྱི་ཟླ་དངཔ་"
    ],
    "february": [
        "ཟླ་༢",
        "ཟླ་གཉིས་པ་",
        "སྤྱི་ཟླ་གཉིས་པ་"
    ],
    "march": [
        "ཟླ་༣",
        "ཟླ་གསུམ་པ་",
        "སྤྱི་ཟླ་གསུམ་པ་"
    ],
    "april": [
        "ཟླ་༤",
        "ཟླ་བཞི་པ་",
        "སྤྱི་ཟླ་བཞི་པ"
    ],
    "may": [
        "ཟླ་༥",
        "ཟླ་ལྔ་པ་",
        "སྤྱི་ཟླ་ལྔ་པ་"
    ],
    "june": [
        "ཟླ་༦",
        "ཟླ་དྲུག་པ",
        "སྤྱི་ཟླ་དྲུག་པ"
    ],
    "july": [
        "ཟླ་༧",
        "ཟླ་བདུན་པ་",
        "སྤྱི་ཟླ་བདུན་པ་"
    ],
    "august": [
        "ཟླ་༨",
        "ཟླ་བརྒྱད་པ་",
        "སྤྱི་ཟླ་བརྒྱད་པ་"
    ],
    "september": [
        "ཟླ་༩",
        "ཟླ་དགུ་པ་",
        "སྤྱི་ཟླ་དགུ་པ་"
    ],
    "october": [
        "ཟླ་༡༠",
        "ཟླ་བཅུ་པ་",
        "སྤྱི་ཟླ་བཅུ་པ་"
    ],
    "november": [
        "ཟླ་༡༡",
        "ཟླ་བཅུ་གཅིག་པ་",
        "སྤྱི་ཟླ་བཅུ་གཅིག་པ་"
    ],
    "december": [
        "ཟླ་༡༢",
        "ཟླ་བཅུ་གཉིས་པ་",
        "སྤྱི་ཟླ་བཅུ་གཉིས་པ་"
    ],
    "monday": [
        "གཟའ་མིག་དམར་",
        "མིར་"
    ],
    "tuesday": [
        "གཟའ་ལྷག་པ་",
        "ལྷག་"
    ],
    "wednesday": [
        "གཟའ་ཕུར་བུ་",
        "ཕུར་"
    ],
    "thursday": [
        "གཟའ་པ་སངས་",
        "སངས་"
    ],
    "friday": [
        "གཟའ་སྤེན་པ་",
        "སྤེན་"
    ],
    "saturday": [
        "གཟའ་ཉི་མ་",
        "ཉི་"
    ],
    "sunday": [
        "གཟའ་ཟླ་བ་",
        "ཟླ་"
    ],
    "am": [
        "སྔ་ཆ་"
    ],
    "pm": [
        "ཕྱི་ཆ་"
    ],
    "year": [
        "ལོ"
    ],
    "month": [
        "ཟླ་ཝ་"
    ],
    "week": [
        "བདུན་ཕྲག"
    ],
    "day": [
        "ཚེས་"
    ],
    "hour": [
        "ཆུ་ཚོད"
    ],
    "minute": [
        "སྐར་མ"
    ],
    "second": [
        "སྐར་ཆཱ་"
    ],
    "relative-type": {
        "0 day ago": [
            "ད་རིས་"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week"
        ],
        "0 year ago": [
            "this year"
        ],
        "1 day ago": [
            "ཁ་ཙ་"
        ],
        "1 month ago": [
            "last month"
        ],
        "1 week ago": [
            "last week"
        ],
        "1 year ago": [
            "last year"
        ],
        "in 1 day": [
            "ནངས་པ་"
        ],
        "in 1 month": [
            "next month"
        ],
        "in 1 week": [
            "next week"
        ],
        "in 1 year": [
            "next year"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "ཉིནམ་ (\\d+[.,]?\\d*) ཧེ་མ་"
        ],
        "\\1 hour ago": [
            "ཆུ་ཚོད་ (\\d+[.,]?\\d*) ཧེ་མ་"
        ],
        "\\1 minute ago": [
            "སྐར་མ་ (\\d+[.,]?\\d*) ཧེ་མ་"
        ],
        "\\1 month ago": [
            "ཟླཝ་ (\\d+[.,]?\\d*) ཧེ་མ་"
        ],
        "\\1 second ago": [
            "སྐར་ཆ་ (\\d+[.,]?\\d*) ཧེ་མ་"
        ],
        "\\1 week ago": [
            "བངུན་ཕྲག་ (\\d+[.,]?\\d*) ཧེ་མ་"
        ],
        "\\1 year ago": [
            "ལོ་འཁོར་ (\\d+[.,]?\\d*) ཧེ་མ་"
        ],
        "in \\1 day": [
            "ཉིནམ་ (\\d+[.,]?\\d*) ནང་"
        ],
        "in \\1 hour": [
            "ཆུ་ཚོད་ (\\d+[.,]?\\d*) ནང་"
        ],
        "in \\1 minute": [
            "སྐར་མ་ (\\d+[.,]?\\d*) ནང་"
        ],
        "in \\1 month": [
            "ཟླཝ་ (\\d+[.,]?\\d*) ནང་"
        ],
        "in \\1 second": [
            "སྐར་ཆ་ (\\d+[.,]?\\d*) ནང་"
        ],
        "in \\1 week": [
            "བངུན་ཕྲག་ (\\d+[.,]?\\d*) ནང་"
        ],
        "in \\1 year": [
            "ལོ་འཁོར་ (\\d+[.,]?\\d*) ནང་"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
