info = {
    "name": "ky",
    "date_order": "DM<PERSON>",
    "january": [
        "янв",
        "январь"
    ],
    "february": [
        "фев",
        "февраль"
    ],
    "march": [
        "мар",
        "март"
    ],
    "april": [
        "апр",
        "апрель"
    ],
    "may": [
        "май"
    ],
    "june": [
        "июн",
        "июнь"
    ],
    "july": [
        "июл",
        "июль"
    ],
    "august": [
        "авг",
        "август"
    ],
    "september": [
        "сен",
        "сентябрь"
    ],
    "october": [
        "окт",
        "октябрь"
    ],
    "november": [
        "ноя",
        "ноябрь"
    ],
    "december": [
        "дек",
        "декабрь"
    ],
    "monday": [
        "дүй",
        "дүйшөмбү"
    ],
    "tuesday": [
        "шейш",
        "шейшемби"
    ],
    "wednesday": [
        "шарш",
        "шаршемби"
    ],
    "thursday": [
        "бейш",
        "бейшемби"
    ],
    "friday": [
        "жума"
    ],
    "saturday": [
        "ишемби",
        "ишм"
    ],
    "sunday": [
        "жек",
        "жекшемби"
    ],
    "am": [
        "таңкы",
        "тң"
    ],
    "pm": [
        "тк",
        "түштөн кийинки"
    ],
    "year": [
        "ж",
        "жыл"
    ],
    "month": [
        "ай"
    ],
    "week": [
        "апт",
        "апта"
    ],
    "day": [
        "күн"
    ],
    "hour": [
        "саат",
        "ст"
    ],
    "minute": [
        "м",
        "мүн",
        "мүнөт"
    ],
    "second": [
        "сек",
        "секунд"
    ],
    "relative-type": {
        "0 day ago": [
            "бүгүн"
        ],
        "0 hour ago": [
            "ушул саатта"
        ],
        "0 minute ago": [
            "ушул мүнөттө"
        ],
        "0 month ago": [
            "бул айда"
        ],
        "0 second ago": [
            "азыр"
        ],
        "0 week ago": [
            "ушул апт",
            "ушул аптада"
        ],
        "0 year ago": [
            "быйыл"
        ],
        "1 day ago": [
            "кечээ"
        ],
        "1 month ago": [
            "өткөн айда"
        ],
        "1 week ago": [
            "өткөн апт",
            "өткөн аптада"
        ],
        "1 year ago": [
            "былтыр"
        ],
        "in 1 day": [
            "эртеӊ"
        ],
        "in 1 month": [
            "эмдиги айда"
        ],
        "in 1 week": [
            "келерки апт",
            "келерки аптада"
        ],
        "in 1 year": [
            "эмдиги жылы"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) күн мурун"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) с мурн",
            "(\\d+[.,]?\\d*) саат мурун"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) мүн мурн",
            "(\\d+[.,]?\\d*) мүн мурун",
            "(\\d+[.,]?\\d*) мүнөт мурун"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) ай мурн",
            "(\\d+[.,]?\\d*) ай мурун"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) сек мурн",
            "(\\d+[.,]?\\d*) сек мурун",
            "(\\d+[.,]?\\d*) секунд мурун"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) апт мурун",
            "(\\d+[.,]?\\d*) апта мурун"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) жыл мурун"
        ],
        "in \\1 day": [
            "(\\d+[.,]?\\d*) күн кийин",
            "(\\d+[.,]?\\d*) күндөн кийин"
        ],
        "in \\1 hour": [
            "(\\d+[.,]?\\d*) с кийн",
            "(\\d+[.,]?\\d*) саат кийин",
            "(\\d+[.,]?\\d*) сааттан кийин"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) мүн кийин",
            "(\\d+[.,]?\\d*) мүн кийн",
            "(\\d+[.,]?\\d*) мүнөттөн кийин"
        ],
        "in \\1 month": [
            "(\\d+[.,]?\\d*) айд кийин",
            "(\\d+[.,]?\\d*) айд кийн",
            "(\\d+[.,]?\\d*) айдан кийин"
        ],
        "in \\1 second": [
            "(\\d+[.,]?\\d*) сек кийин",
            "(\\d+[.,]?\\d*) сек кийн",
            "(\\d+[.,]?\\d*) секунддан кийин"
        ],
        "in \\1 week": [
            "(\\d+[.,]?\\d*) апт кийин",
            "(\\d+[.,]?\\d*) аптадан кийин"
        ],
        "in \\1 year": [
            "(\\d+[.,]?\\d*) жыл кийин",
            "(\\d+[.,]?\\d*) жылдан кийин"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
