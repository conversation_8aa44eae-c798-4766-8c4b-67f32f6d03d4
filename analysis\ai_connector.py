"""
AI Connector Module for Multiple AI Services
Handles connections to OpenAI, DeepSeek, and Qwen AI
"""

import os
import json
import time
import logging
import requests
import random
from datetime import datetime
from security.key_manager import KeyManager

# Set up logging
logger = logging.getLogger("ai_connector")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/analysis.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class AIConnector:
    """
    Base class for AI service connectors
    """
    
    def __init__(self, key_manager=None):
        """
        Initialize the AI connector
        
        Args:
            key_manager (KeyManager, optional): Key manager instance for API keys
        """
        self.key_manager = key_manager or KeyManager()
        self.retry_attempts = 3
        self.retry_delay = 2  # seconds
        self.circuit_breaker_threshold = 3  # consecutive failures
        self.circuit_breaker_timeout = 300  # seconds
        self.circuit_breaker = {
            "failures": 0,
            "last_failure": None,
            "open": False
        }
    
    def _check_circuit_breaker(self):
        """
        Check if circuit breaker is open
        
        Returns:
            bool: True if circuit breaker is open, False otherwise
        """
        if not self.circuit_breaker["open"]:
            return False
        
        # Check if circuit breaker timeout has passed
        if self.circuit_breaker["last_failure"] is not None:
            elapsed = (datetime.now() - self.circuit_breaker["last_failure"]).total_seconds()
            if elapsed > self.circuit_breaker_timeout:
                # Reset circuit breaker
                self.circuit_breaker["open"] = False
                self.circuit_breaker["failures"] = 0
                logger.info("Circuit breaker reset after timeout")
                return False
        
        return True
    
    def _handle_failure(self, error):
        """
        Handle API call failure and update circuit breaker
        
        Args:
            error: The error that occurred
        """
        self.circuit_breaker["failures"] += 1
        self.circuit_breaker["last_failure"] = datetime.now()
        
        if self.circuit_breaker["failures"] >= self.circuit_breaker_threshold:
            self.circuit_breaker["open"] = True
            logger.warning(f"Circuit breaker opened after {self.circuit_breaker_threshold} consecutive failures")
        
        logger.error(f"API call failed: {error}")
    
    def _handle_success(self):
        """
        Handle API call success and reset circuit breaker
        """
        self.circuit_breaker["failures"] = 0
        self.circuit_breaker["open"] = False
    
    def analyze_market(self, data):
        """
        Analyze market data using AI
        
        Args:
            data (dict): Market data to analyze
            
        Returns:
            dict: Analysis results
        """
        raise NotImplementedError("Subclasses must implement analyze_market")

class OpenAIConnector(AIConnector):
    """
    Connector for OpenAI API
    """
    
    def __init__(self, key_manager=None):
        """
        Initialize the OpenAI connector
        
        Args:
            key_manager (KeyManager, optional): Key manager instance for API keys
        """
        super().__init__(key_manager)
        self.service_name = "openai"
        self.model = "gpt-4"
        self.api_url = "https://api.openai.com/v1/chat/completions"
    
    def analyze_market(self, data):
        """
        Analyze market data using OpenAI
        
        Args:
            data (dict): Market data to analyze
            
        Returns:
            dict: Analysis results
        """
        if self._check_circuit_breaker():
            logger.warning("Circuit breaker is open. Skipping OpenAI analysis.")
            return {"error": "Circuit breaker open", "service": self.service_name}
        
        # Get API key using round-robin
        api_key_data = self.key_manager.get_next_key(self.service_name)
        if not api_key_data:
            logger.error("No OpenAI API key available")
            return {"error": "No API key available", "service": self.service_name}
        
        api_key = api_key_data["key"]
        
        # Prepare market data for analysis
        market_summary = self._prepare_market_data(data)
        
        # Prepare prompt for OpenAI
        prompt = f"""
        You are a cryptocurrency trading expert. Analyze the following market data and provide trading advice.
        
        Market Data:
        {market_summary}
        
        Please provide:
        1. Market sentiment (bullish, bearish, or neutral)
        2. Key support and resistance levels
        3. Trading recommendation (buy, sell, or hold)
        4. Confidence level (0-100%)
        5. Brief explanation (max 100 words)
        
        Format your response as a JSON object with the following structure:
        {{
            "sentiment": "bullish|bearish|neutral",
            "support_levels": [level1, level2],
            "resistance_levels": [level1, level2],
            "recommendation": "buy|sell|hold",
            "confidence": 75,
            "explanation": "Your explanation here"
        }}
        """
        
        # Prepare request
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a cryptocurrency trading expert."},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 500
        }
        
        # Make API call with retry logic
        for attempt in range(self.retry_attempts):
            try:
                response = requests.post(self.api_url, headers=headers, json=payload, timeout=30)
                response.raise_for_status()
                
                # Parse response
                response_data = response.json()
                content = response_data["choices"][0]["message"]["content"]
                
                # Extract JSON from content
                try:
                    analysis = json.loads(content)
                    self._handle_success()
                    return {
                        "service": self.service_name,
                        "analysis": analysis,
                        "timestamp": datetime.now().isoformat()
                    }
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON from OpenAI response: {content}")
                    # Try to extract JSON using regex or other methods
                    import re
                    json_match = re.search(r'({.*})', content.replace('\n', ''), re.DOTALL)
                    if json_match:
                        try:
                            analysis = json.loads(json_match.group(1))
                            self._handle_success()
                            return {
                                "service": self.service_name,
                                "analysis": analysis,
                                "timestamp": datetime.now().isoformat()
                            }
                        except json.JSONDecodeError:
                            pass
                    
                    # If all parsing attempts fail, return error
                    self._handle_failure("Failed to parse JSON from response")
                    return {"error": "Failed to parse response", "service": self.service_name}
            
            except requests.exceptions.RequestException as e:
                self._handle_failure(e)
                if attempt < self.retry_attempts - 1:
                    wait_time = self.retry_delay * (2 ** attempt)
                    logger.warning(f"Retrying OpenAI API call in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    return {"error": str(e), "service": self.service_name}
        
        return {"error": "Max retry attempts reached", "service": self.service_name}
    
    def _prepare_market_data(self, data):
        """
        Prepare market data for analysis
        
        Args:
            data (dict): Raw market data
            
        Returns:
            str: Formatted market data summary
        """
        try:
            # Extract relevant information from data
            symbol = data.get("symbol", "BTCUSDT")
            current_price = data.get("price", data.get("close", 0))
            
            # Format historical prices if available
            historical_prices = ""
            if "historical" in data and isinstance(data["historical"], list):
                historical = data["historical"][-10:]  # Last 10 data points
                historical_prices = "Historical prices (most recent first):\n"
                for i, point in enumerate(reversed(historical)):
                    price = point.get("close", 0)
                    timestamp = point.get("timestamp", "")
                    historical_prices += f"{i+1}. {timestamp}: ${price}\n"
            
            # Format indicators if available
            indicators = ""
            if "indicators" in data:
                ind = data["indicators"]
                indicators = "Technical Indicators:\n"
                for name, value in ind.items():
                    indicators += f"- {name}: {value}\n"
            
            # Combine all information
            summary = f"""
            Symbol: {symbol}
            Current Price: ${current_price}
            Timestamp: {datetime.now().isoformat()}
            
            {historical_prices}
            
            {indicators}
            """
            
            return summary
        
        except Exception as e:
            logger.error(f"Error preparing market data: {e}")
            return str(data)

class DeepSeekConnector(AIConnector):
    """
    Connector for DeepSeek API
    """
    
    def __init__(self, key_manager=None):
        """
        Initialize the DeepSeek connector
        
        Args:
            key_manager (KeyManager, optional): Key manager instance for API keys
        """
        super().__init__(key_manager)
        self.service_name = "deepseek"
        self.model = "deepseek-chat"
        self.api_url = "https://api.deepseek.com/v1/chat/completions"  # Example URL
    
    def analyze_market(self, data):
        """
        Analyze market data using DeepSeek
        
        Args:
            data (dict): Market data to analyze
            
        Returns:
            dict: Analysis results
        """
        if self._check_circuit_breaker():
            logger.warning("Circuit breaker is open. Skipping DeepSeek analysis.")
            return {"error": "Circuit breaker open", "service": self.service_name}
        
        # Get API key using round-robin
        api_key_data = self.key_manager.get_next_key(self.service_name)
        if not api_key_data:
            logger.error("No DeepSeek API key available")
            return {"error": "No API key available", "service": self.service_name}
        
        api_key = api_key_data["key"]
        
        # Prepare market data for analysis
        market_summary = self._prepare_market_data(data)
        
        # Prepare prompt for DeepSeek
        prompt = f"""
        You are a cryptocurrency trading expert. Analyze the following market data and provide trading advice.
        
        Market Data:
        {market_summary}
        
        Please provide:
        1. Market sentiment (bullish, bearish, or neutral)
        2. Key support and resistance levels
        3. Trading recommendation (buy, sell, or hold)
        4. Confidence level (0-100%)
        5. Brief explanation (max 100 words)
        
        Format your response as a JSON object with the following structure:
        {{
            "sentiment": "bullish|bearish|neutral",
            "support_levels": [level1, level2],
            "resistance_levels": [level1, level2],
            "recommendation": "buy|sell|hold",
            "confidence": 75,
            "explanation": "Your explanation here"
        }}
        """
        
        # Prepare request
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a cryptocurrency trading expert."},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 500
        }
        
        # Make API call with retry logic
        for attempt in range(self.retry_attempts):
            try:
                response = requests.post(self.api_url, headers=headers, json=payload, timeout=30)
                response.raise_for_status()
                
                # Parse response
                response_data = response.json()
                content = response_data["choices"][0]["message"]["content"]
                
                # Extract JSON from content
                try:
                    analysis = json.loads(content)
                    self._handle_success()
                    return {
                        "service": self.service_name,
                        "analysis": analysis,
                        "timestamp": datetime.now().isoformat()
                    }
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON from DeepSeek response: {content}")
                    # Try to extract JSON using regex or other methods
                    import re
                    json_match = re.search(r'({.*})', content.replace('\n', ''), re.DOTALL)
                    if json_match:
                        try:
                            analysis = json.loads(json_match.group(1))
                            self._handle_success()
                            return {
                                "service": self.service_name,
                                "analysis": analysis,
                                "timestamp": datetime.now().isoformat()
                            }
                        except json.JSONDecodeError:
                            pass
                    
                    # If all parsing attempts fail, return error
                    self._handle_failure("Failed to parse JSON from response")
                    return {"error": "Failed to parse response", "service": self.service_name}
            
            except requests.exceptions.RequestException as e:
                self._handle_failure(e)
                if attempt < self.retry_attempts - 1:
                    wait_time = self.retry_delay * (2 ** attempt)
                    logger.warning(f"Retrying DeepSeek API call in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    # If DeepSeek fails, try to fall back to Qwen
                    logger.warning("DeepSeek API failed. Falling back to Qwen AI.")
                    qwen_connector = QwenConnector(self.key_manager)
                    return qwen_connector.analyze_market(data)
        
        return {"error": "Max retry attempts reached", "service": self.service_name}
    
    def _prepare_market_data(self, data):
        """
        Prepare market data for analysis
        
        Args:
            data (dict): Raw market data
            
        Returns:
            str: Formatted market data summary
        """
        try:
            # Extract relevant information from data
            symbol = data.get("symbol", "BTCUSDT")
            current_price = data.get("price", data.get("close", 0))
            
            # Format historical prices if available
            historical_prices = ""
            if "historical" in data and isinstance(data["historical"], list):
                historical = data["historical"][-10:]  # Last 10 data points
                historical_prices = "Historical prices (most recent first):\n"
                for i, point in enumerate(reversed(historical)):
                    price = point.get("close", 0)
                    timestamp = point.get("timestamp", "")
                    historical_prices += f"{i+1}. {timestamp}: ${price}\n"
            
            # Format indicators if available
            indicators = ""
            if "indicators" in data:
                ind = data["indicators"]
                indicators = "Technical Indicators:\n"
                for name, value in ind.items():
                    indicators += f"- {name}: {value}\n"
            
            # Combine all information
            summary = f"""
            Symbol: {symbol}
            Current Price: ${current_price}
            Timestamp: {datetime.now().isoformat()}
            
            {historical_prices}
            
            {indicators}
            """
            
            return summary
        
        except Exception as e:
            logger.error(f"Error preparing market data: {e}")
            return str(data)

class QwenConnector(AIConnector):
    """
    Connector for Qwen AI API
    """
    
    def __init__(self, key_manager=None):
        """
        Initialize the Qwen AI connector
        
        Args:
            key_manager (KeyManager, optional): Key manager instance for API keys
        """
        super().__init__(key_manager)
        self.service_name = "qwen"
        self.model = "qwen-max"
        self.api_url = "https://api.qwen.ai/v1/chat/completions"  # Example URL
    
    def analyze_market(self, data):
        """
        Analyze market data using Qwen AI
        
        Args:
            data (dict): Market data to analyze
            
        Returns:
            dict: Analysis results
        """
        if self._check_circuit_breaker():
            logger.warning("Circuit breaker is open. Skipping Qwen AI analysis.")
            return {"error": "Circuit breaker open", "service": self.service_name}
        
        # Get API key using round-robin
        api_key_data = self.key_manager.get_next_key(self.service_name)
        if not api_key_data:
            logger.error("No Qwen AI API key available")
            return {"error": "No API key available", "service": self.service_name}
        
        api_key = api_key_data["key"]
        
        # Prepare market data for analysis
        market_summary = self._prepare_market_data(data)
        
        # Prepare prompt for Qwen AI
        prompt = f"""
        You are a cryptocurrency trading expert. Analyze the following market data and provide trading advice.
        
        Market Data:
        {market_summary}
        
        Please provide:
        1. Market sentiment (bullish, bearish, or neutral)
        2. Key support and resistance levels
        3. Trading recommendation (buy, sell, or hold)
        4. Confidence level (0-100%)
        5. Brief explanation (max 100 words)
        
        Format your response as a JSON object with the following structure:
        {{
            "sentiment": "bullish|bearish|neutral",
            "support_levels": [level1, level2],
            "resistance_levels": [level1, level2],
            "recommendation": "buy|sell|hold",
            "confidence": 75,
            "explanation": "Your explanation here"
        }}
        """
        
        # Prepare request
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a cryptocurrency trading expert."},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 500
        }
        
        # Make API call with retry logic
        for attempt in range(self.retry_attempts):
            try:
                response = requests.post(self.api_url, headers=headers, json=payload, timeout=30)
                response.raise_for_status()
                
                # Parse response
                response_data = response.json()
                content = response_data["choices"][0]["message"]["content"]
                
                # Extract JSON from content
                try:
                    analysis = json.loads(content)
                    self._handle_success()
                    return {
                        "service": self.service_name,
                        "analysis": analysis,
                        "timestamp": datetime.now().isoformat()
                    }
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON from Qwen AI response: {content}")
                    # Try to extract JSON using regex or other methods
                    import re
                    json_match = re.search(r'({.*})', content.replace('\n', ''), re.DOTALL)
                    if json_match:
                        try:
                            analysis = json.loads(json_match.group(1))
                            self._handle_success()
                            return {
                                "service": self.service_name,
                                "analysis": analysis,
                                "timestamp": datetime.now().isoformat()
                            }
                        except json.JSONDecodeError:
                            pass
                    
                    # If all parsing attempts fail, return error
                    self._handle_failure("Failed to parse JSON from response")
                    return {"error": "Failed to parse response", "service": self.service_name}
            
            except requests.exceptions.RequestException as e:
                self._handle_failure(e)
                if attempt < self.retry_attempts - 1:
                    wait_time = self.retry_delay * (2 ** attempt)
                    logger.warning(f"Retrying Qwen AI API call in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    return {"error": str(e), "service": self.service_name}
        
        return {"error": "Max retry attempts reached", "service": self.service_name}
    
    def _prepare_market_data(self, data):
        """
        Prepare market data for analysis
        
        Args:
            data (dict): Raw market data
            
        Returns:
            str: Formatted market data summary
        """
        try:
            # Extract relevant information from data
            symbol = data.get("symbol", "BTCUSDT")
            current_price = data.get("price", data.get("close", 0))
            
            # Format historical prices if available
            historical_prices = ""
            if "historical" in data and isinstance(data["historical"], list):
                historical = data["historical"][-10:]  # Last 10 data points
                historical_prices = "Historical prices (most recent first):\n"
                for i, point in enumerate(reversed(historical)):
                    price = point.get("close", 0)
                    timestamp = point.get("timestamp", "")
                    historical_prices += f"{i+1}. {timestamp}: ${price}\n"
            
            # Format indicators if available
            indicators = ""
            if "indicators" in data:
                ind = data["indicators"]
                indicators = "Technical Indicators:\n"
                for name, value in ind.items():
                    indicators += f"- {name}: {value}\n"
            
            # Combine all information
            summary = f"""
            Symbol: {symbol}
            Current Price: ${current_price}
            Timestamp: {datetime.now().isoformat()}
            
            {historical_prices}
            
            {indicators}
            """
            
            return summary
        
        except Exception as e:
            logger.error(f"Error preparing market data: {e}")
            return str(data)
