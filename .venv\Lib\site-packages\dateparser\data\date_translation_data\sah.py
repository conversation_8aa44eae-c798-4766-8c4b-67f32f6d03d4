info = {
    "name": "sah",
    "date_order": "<PERSON><PERSON>",
    "january": [
        "тохс",
        "тохсунньу"
    ],
    "february": [
        "олун",
        "олунньу"
    ],
    "march": [
        "клн",
        "кулун тутар"
    ],
    "april": [
        "мсу",
        "муус устар"
    ],
    "may": [
        "ыам",
        "ыам ыйа",
        "ыам ыйын"
    ],
    "june": [
        "бэс",
        "бэс ыйа",
        "бэс ыйын"
    ],
    "july": [
        "от ыйа",
        "от ыйын",
        "отй"
    ],
    "august": [
        "атр",
        "атырдьых ыйа",
        "атырдьых ыйын"
    ],
    "september": [
        "балаҕан ыйа",
        "балаҕан ыйын",
        "блҕ"
    ],
    "october": [
        "алт",
        "алтынньы"
    ],
    "november": [
        "сэт",
        "сэтинньи"
    ],
    "december": [
        "ахс",
        "ахсынньы"
    ],
    "monday": [
        "бн",
        "бэнидиэнньик"
    ],
    "tuesday": [
        "оп",
        "оптуорунньук"
    ],
    "wednesday": [
        "сэ",
        "сэрэдэ"
    ],
    "thursday": [
        "чп",
        "чэппиэр"
    ],
    "friday": [
        "бэ",
        "бээтиҥсэ"
    ],
    "saturday": [
        "сб",
        "субуота"
    ],
    "sunday": [
        "баскыһыанньа",
        "бс"
    ],
    "am": [
        "эи"
    ],
    "pm": [
        "эк"
    ],
    "year": [
        "сыл"
    ],
    "month": [
        "ый"
    ],
    "week": [
        "нэдиэлэ"
    ],
    "day": [
        "күн"
    ],
    "hour": [
        "чаас"
    ],
    "minute": [
        "мүнүүтэ"
    ],
    "second": [
        "сөкүүндэ"
    ],
    "relative-type": {
        "0 day ago": [
            "бүгүн"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "бу ый"
        ],
        "0 second ago": [
            "билигин"
        ],
        "0 week ago": [
            "бу нэдиэлэ"
        ],
        "0 year ago": [
            "быйыл"
        ],
        "1 day ago": [
            "бэҕэһээ"
        ],
        "1 month ago": [
            "ааспыт ый"
        ],
        "1 week ago": [
            "ааспыт нэдиэлэ"
        ],
        "1 year ago": [
            "былырыын"
        ],
        "in 1 day": [
            "сарсын"
        ],
        "in 1 month": [
            "аныгыскы ый"
        ],
        "in 1 week": [
            "кэлэр нэдиэлэ"
        ],
        "in 1 year": [
            "эһиил"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) күн ынараа өттүгэр"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) чаас ынараа өттүгэр"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) мүнүүтэ ынараа өттүгэр"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) ый ынараа өттүгэр"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) сөк анараа өттүгэр",
            "(\\d+[.,]?\\d*) сөкүүндэ ынараа өттүгэр"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) нэдиэлэ анараа өттүгэр"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) сыл ынараа өттүгэр"
        ],
        "in \\1 day": [
            "(\\d+[.,]?\\d*) күнүнэн"
        ],
        "in \\1 hour": [
            "(\\d+[.,]?\\d*) чааһынан"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) мүнүүтэннэн"
        ],
        "in \\1 month": [
            "(\\d+[.,]?\\d*) ыйынан"
        ],
        "in \\1 second": [
            "(\\d+[.,]?\\d*) сөкүүндэннэн"
        ],
        "in \\1 week": [
            "(\\d+[.,]?\\d*) нэдиэлэннэн"
        ],
        "in \\1 year": [
            "(\\d+[.,]?\\d*) сылынан"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
