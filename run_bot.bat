@echo off
echo Starting Trading Bot with Watchdog...
echo.

REM Set environment variables for resource management
set CPU_THRESHOLD=95
set RAM_THRESHOLD=95
set MAX_CONSECUTIVE_ERRORS=5
set ERROR_COOLDOWN=60

REM Create logs directory if it doesn't exist
if not exist logs mkdir logs

REM Start the watchdog process
python watchdog.py --check-interval 30 --max-restarts 5 --cooldown 300 --max-memory 95 --max-cpu 95

echo.
echo Watchdog process has exited.
echo Check logs/watchdog.log for details.
echo.

pause
