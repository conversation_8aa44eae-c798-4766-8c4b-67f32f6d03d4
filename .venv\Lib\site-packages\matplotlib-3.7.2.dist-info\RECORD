__pycache__/pylab.cpython-312.pyc,,
matplotlib-3.7.2-py3.12-nspkg.pth,sha256=8AmPeV-f78aJb0I1LJrRHtvqIMlLBZGCBlG6PXj5EAY,498
matplotlib-3.7.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
matplotlib-3.7.2.dist-info/METADATA,sha256=0y5uqVt_8IYobgd8PouUcSaSrZL4XK0QC0ldFzAT80A,6039
matplotlib-3.7.2.dist-info/RECORD,,
matplotlib-3.7.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib-3.7.2.dist-info/WHEEL,sha256=Jt89WmAJV88pNRHMaNesrLxovMec0KaUDI5oSN8rrpM,101
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE,sha256=WhqB6jAXKMi7opM9qDLAzWIina8giToCSrPVMkRGjbw,4830
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_AMSFONTS,sha256=FVFB1Zh38zj24cCAXem3mWTc5x_l0qVsROOLLA9-Ne4,12675
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_BAKOMA,sha256=k62ytTwCt84Gsmv_QTWMISSPQv7Sh7Yz7EwxA4N3zbs,1440
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_CARLOGO,sha256=YZAtXu803SSHC3KHqWJg0zKCM7lvcgK_cK1uKg2i3j8,4455
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_COLORBREWER,sha256=7FIbyIlwg2PD2R0pDZCClCN3gRfqJZABk-mOKfUiJAg,1968
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_COURIERTEN,sha256=rIH3F6r_lFa4gBD8VTgqjf8ZnkxLzF1h4EDD9wY760M,802
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_JSXTOOLS_RESIZE_OBSERVER,sha256=WXdWrctR8kPvT7OGkgN39h0BKs4JBDZOGo7pquxq_IQ,6799
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_QT4_EDITOR,sha256=srUMqLYXKsojCVrfFduJ03J-nvLW7wF45CcjQBG-080,1230
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_SOLARIZED,sha256=EtUyf7xN-EWoaIPeme1f30GYRF1W26zfX62PDv3JdRM,1121
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_STIX,sha256=TMPvujo6YE62-TchHkbaHiFIgwBWpuCbzBnfQXDSUqQ,3914
matplotlib-3.7.2.dist-info/licenses/LICENSE/LICENSE_YORICK,sha256=yrdT04wJNlHo3rWrtoTj7WgCDg5BgDT5TXnokNx66E0,2313
matplotlib-3.7.2.dist-info/namespace_packages.txt,sha256=A2PHFg9NKYOU4pEQ1h97U0Qd-rB-65W34XqC-56ZN9g,13
matplotlib-3.7.2.dist-info/top_level.txt,sha256=5ZkWSoOE5dc7ykV6m51vToOGk-k46KcCqdiWASRGDPU,31
matplotlib/__init__.py,sha256=AhDki8nFBeUGJXF_OnpTme4vhGiC6DuOGnoRr2-fyk8,53402
matplotlib/__pycache__/__init__.cpython-312.pyc,,
matplotlib/__pycache__/_afm.cpython-312.pyc,,
matplotlib/__pycache__/_animation_data.cpython-312.pyc,,
matplotlib/__pycache__/_blocking_input.cpython-312.pyc,,
matplotlib/__pycache__/_cm.cpython-312.pyc,,
matplotlib/__pycache__/_cm_listed.cpython-312.pyc,,
matplotlib/__pycache__/_color_data.cpython-312.pyc,,
matplotlib/__pycache__/_constrained_layout.cpython-312.pyc,,
matplotlib/__pycache__/_docstring.cpython-312.pyc,,
matplotlib/__pycache__/_enums.cpython-312.pyc,,
matplotlib/__pycache__/_fontconfig_pattern.cpython-312.pyc,,
matplotlib/__pycache__/_internal_utils.cpython-312.pyc,,
matplotlib/__pycache__/_layoutgrid.cpython-312.pyc,,
matplotlib/__pycache__/_mathtext.cpython-312.pyc,,
matplotlib/__pycache__/_mathtext_data.cpython-312.pyc,,
matplotlib/__pycache__/_pylab_helpers.cpython-312.pyc,,
matplotlib/__pycache__/_text_helpers.cpython-312.pyc,,
matplotlib/__pycache__/_tight_bbox.cpython-312.pyc,,
matplotlib/__pycache__/_tight_layout.cpython-312.pyc,,
matplotlib/__pycache__/_type1font.cpython-312.pyc,,
matplotlib/__pycache__/_version.cpython-312.pyc,,
matplotlib/__pycache__/afm.cpython-312.pyc,,
matplotlib/__pycache__/animation.cpython-312.pyc,,
matplotlib/__pycache__/artist.cpython-312.pyc,,
matplotlib/__pycache__/axis.cpython-312.pyc,,
matplotlib/__pycache__/backend_bases.cpython-312.pyc,,
matplotlib/__pycache__/backend_managers.cpython-312.pyc,,
matplotlib/__pycache__/backend_tools.cpython-312.pyc,,
matplotlib/__pycache__/bezier.cpython-312.pyc,,
matplotlib/__pycache__/category.cpython-312.pyc,,
matplotlib/__pycache__/cm.cpython-312.pyc,,
matplotlib/__pycache__/collections.cpython-312.pyc,,
matplotlib/__pycache__/colorbar.cpython-312.pyc,,
matplotlib/__pycache__/colors.cpython-312.pyc,,
matplotlib/__pycache__/container.cpython-312.pyc,,
matplotlib/__pycache__/contour.cpython-312.pyc,,
matplotlib/__pycache__/dates.cpython-312.pyc,,
matplotlib/__pycache__/docstring.cpython-312.pyc,,
matplotlib/__pycache__/dviread.cpython-312.pyc,,
matplotlib/__pycache__/figure.cpython-312.pyc,,
matplotlib/__pycache__/font_manager.cpython-312.pyc,,
matplotlib/__pycache__/fontconfig_pattern.cpython-312.pyc,,
matplotlib/__pycache__/gridspec.cpython-312.pyc,,
matplotlib/__pycache__/hatch.cpython-312.pyc,,
matplotlib/__pycache__/image.cpython-312.pyc,,
matplotlib/__pycache__/layout_engine.cpython-312.pyc,,
matplotlib/__pycache__/legend.cpython-312.pyc,,
matplotlib/__pycache__/legend_handler.cpython-312.pyc,,
matplotlib/__pycache__/lines.cpython-312.pyc,,
matplotlib/__pycache__/markers.cpython-312.pyc,,
matplotlib/__pycache__/mathtext.cpython-312.pyc,,
matplotlib/__pycache__/mlab.cpython-312.pyc,,
matplotlib/__pycache__/offsetbox.cpython-312.pyc,,
matplotlib/__pycache__/patches.cpython-312.pyc,,
matplotlib/__pycache__/path.cpython-312.pyc,,
matplotlib/__pycache__/patheffects.cpython-312.pyc,,
matplotlib/__pycache__/pylab.cpython-312.pyc,,
matplotlib/__pycache__/pyplot.cpython-312.pyc,,
matplotlib/__pycache__/quiver.cpython-312.pyc,,
matplotlib/__pycache__/rcsetup.cpython-312.pyc,,
matplotlib/__pycache__/sankey.cpython-312.pyc,,
matplotlib/__pycache__/scale.cpython-312.pyc,,
matplotlib/__pycache__/spines.cpython-312.pyc,,
matplotlib/__pycache__/stackplot.cpython-312.pyc,,
matplotlib/__pycache__/streamplot.cpython-312.pyc,,
matplotlib/__pycache__/table.cpython-312.pyc,,
matplotlib/__pycache__/texmanager.cpython-312.pyc,,
matplotlib/__pycache__/text.cpython-312.pyc,,
matplotlib/__pycache__/textpath.cpython-312.pyc,,
matplotlib/__pycache__/ticker.cpython-312.pyc,,
matplotlib/__pycache__/tight_bbox.cpython-312.pyc,,
matplotlib/__pycache__/tight_layout.cpython-312.pyc,,
matplotlib/__pycache__/transforms.cpython-312.pyc,,
matplotlib/__pycache__/type1font.cpython-312.pyc,,
matplotlib/__pycache__/units.cpython-312.pyc,,
matplotlib/__pycache__/widgets.cpython-312.pyc,,
matplotlib/_afm.py,sha256=xb5uam-U6bFzhcO07zEN1sbV9mIywMj7-T2d52gnA5s,16693
matplotlib/_animation_data.py,sha256=BBDhy8phg_Vxj-ZzB-tQVmX5QnfjHfemUB66KW9Zikw,7972
matplotlib/_api/__init__.py,sha256=RGXCYmbXaK5Q1lTkEZJwLfRawwuQmGTHirxhaCIblsw,13598
matplotlib/_api/__pycache__/__init__.cpython-312.pyc,,
matplotlib/_api/__pycache__/deprecation.cpython-312.pyc,,
matplotlib/_api/deprecation.py,sha256=mv0HDtr7v3S0G1OMF2sZNiXachgKd-U4yz8WWqlYN8U,19978
matplotlib/_blocking_input.py,sha256=VHNsxvX2mTx_xBknd30MSicVlRXS4dCDe9hDctbV5rk,1224
matplotlib/_c_internal_utils.cp312-win_amd64.pyd,sha256=wQg78M9syK_zcf_j9gpeVtutd_cTknVU4rxTmeCQIZ8,13824
matplotlib/_cm.py,sha256=eE6AzoVf2HxDgaAwjRSkE2N8L0wyiUBjWvFIq5ArJiE,66439
matplotlib/_cm_listed.py,sha256=hpgMx7bjxJx5nl1PbQvaCDUBHQf8njaRrM2iMaBeZOM,109462
matplotlib/_color_data.py,sha256=k-wdTi6ArJxksqBfMT-7Uy2qWz8XX4Th5gsjf32CwmM,34780
matplotlib/_constrained_layout.py,sha256=cnmPXLqCLY0oAa14EKgBssNftT1OiMdah6N2edwbI0k,30477
matplotlib/_docstring.py,sha256=Q4u0zJx5Agrf_Ot4kDQyji9pcVPq8FYPaU9r3_GSGlI,3069
matplotlib/_enums.py,sha256=cq5dtb_qy4g3cHgr1KdVA9qzYalgz7KCtTytyFp3PAs,6474
matplotlib/_fontconfig_pattern.py,sha256=3-BX7OTwQ5HmDJg_THxQDvLAoCQlvcU0XuN3ZluOySo,4746
matplotlib/_image.cp312-win_amd64.pyd,sha256=ByNZiGvJHblko-dpj87UDKvGX5Zp1nRYNKQpcwCPDu0,143872
matplotlib/_internal_utils.py,sha256=nhK6LLWYW93fBcsFiO09JmqFj2rgHEsGYFOeaC7HRKw,2140
matplotlib/_layoutgrid.py,sha256=laNstF8ddikwhSjwLGNVjirsWeq0LhVSHVImWScAEi4,21676
matplotlib/_mathtext.py,sha256=1X5PDzcWjTKCpJp5M4RmLOcAFUQkJTD10UTxjSS7Hsc,94313
matplotlib/_mathtext_data.py,sha256=jDYqTUlGeELaAJrBBVJ0RYDGMja03OhjYcdwpU85Apc,48385
matplotlib/_path.cp312-win_amd64.pyd,sha256=0vHw2gdKHQuaiYkW0SGU1pOc91iaaQFAicwSXWahMQs,142336
matplotlib/_pylab_helpers.py,sha256=lIkvc-NFRMQIsXykuymE1Dcf5A-bMvu4BcnQjUL9vKg,4331
matplotlib/_qhull.cp312-win_amd64.pyd,sha256=gbP4kX9F2Dalnjdw8p5f_mx9g2DTXWudIlrMJChfJcQ,451584
matplotlib/_text_helpers.py,sha256=jvCm4JYRs_YTecSO2gZJyWS9tXjy5BHTJHBh8UZux2Q,2496
matplotlib/_tight_bbox.py,sha256=ddJ5ViXulbPLocHr-RkWK27WJSuV2WXUx74jZyL0NOg,2787
matplotlib/_tight_layout.py,sha256=oWCfzymTLvdmydhrYH2--O5UFMywQkJdXwvEAU3HR7s,12675
matplotlib/_tri.cp312-win_amd64.pyd,sha256=YiFSBN1nap_Q4V4w5at2D7trt2C41nyWwubfY-yRf7M,228864
matplotlib/_ttconv.cp312-win_amd64.pyd,sha256=2nVmlvrYHNmp30UfGO65wOuUBQ410Yti5j-THUdGdH0,47616
matplotlib/_type1font.py,sha256=XhdJ3CpLOyzrzrRRvMLe4lbe32SKk7Vvyk5nfHpj6rk,28357
matplotlib/_version.py,sha256=66ky-UupHQgTmixgv7yE26-ZA_IAw2D10YEFwkP04ZQ,532
matplotlib/afm.py,sha256=IRqq7WPcnsNEHADJtk880YJYci6uwBH23g1VoepVJrk,140
matplotlib/animation.py,sha256=9cLMpLCOLZmF4XZL3KNtFDXzh6gIhVvDzhQnj6qSwDI,70690
matplotlib/artist.py,sha256=g20RdNMqnDn1mBd6nhi2G0VuMsEIKfXko81OdCR2PnQ,63475
matplotlib/axes/__init__.py,sha256=tlVOhR-Y-bkcTI3jm8jbXJXFLqc5yQt8n_0a-wXZa5M,368
matplotlib/axes/__pycache__/__init__.cpython-312.pyc,,
matplotlib/axes/__pycache__/_axes.cpython-312.pyc,,
matplotlib/axes/__pycache__/_base.cpython-312.pyc,,
matplotlib/axes/__pycache__/_secondary_axes.cpython-312.pyc,,
matplotlib/axes/_axes.py,sha256=MkQePj6O0Ir_fhWnh3D90OZfllb50DhzYqg6FYub37E,324545
matplotlib/axes/_base.py,sha256=7B93VOqCmBeoWYFxBRrtYZh9j50s71e3zMIdLlfVTb8,175838
matplotlib/axes/_secondary_axes.py,sha256=JrXK7yLvnF4gT_aMNm87NcNlGXcO6CC9dE0aks-OniQ,10460
matplotlib/axis.py,sha256=MLYeX8pp9c8pLANfCXRCZqny3eh5sVurY0l8qxsv4qQ,99899
matplotlib/backend_bases.py,sha256=jbPYee3NNFJwCLVSPiQSUmq-weUt5AR4ygVUWbv-BrM,131728
matplotlib/backend_managers.py,sha256=FWNHC7iNXw7dNb4bgmm5CIOZKggkqi6FGT0-oXEE2Jg,12024
matplotlib/backend_tools.py,sha256=bIwyAWeSWiH3jQs2nLDZWwgTCl1XwaxWXPwU66ezgNg,32985
matplotlib/backends/__init__.py,sha256=zedu8Er46aBvLouOLhCfcaPPC3QTp-KnhqWB58PK0As,137
matplotlib/backends/__pycache__/__init__.cpython-312.pyc,,
matplotlib/backends/__pycache__/_backend_gtk.cpython-312.pyc,,
matplotlib/backends/__pycache__/_backend_pdf_ps.cpython-312.pyc,,
matplotlib/backends/__pycache__/_backend_tk.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_agg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_cairo.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_gtk3.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_gtk3agg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_gtk3cairo.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_gtk4.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_gtk4agg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_gtk4cairo.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_macosx.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_mixed.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_nbagg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_pdf.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_pgf.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_ps.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_qt.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_qt5.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_qt5agg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_qt5cairo.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_qtagg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_qtcairo.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_svg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_template.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_tkagg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_tkcairo.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_webagg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_webagg_core.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_wx.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_wxagg.cpython-312.pyc,,
matplotlib/backends/__pycache__/backend_wxcairo.cpython-312.pyc,,
matplotlib/backends/__pycache__/qt_compat.cpython-312.pyc,,
matplotlib/backends/_backend_agg.cp312-win_amd64.pyd,sha256=jqMF32lTUj642qNEvozh4_L_xpk_UJVzE2ajBuhwLeo,202752
matplotlib/backends/_backend_gtk.py,sha256=aEVfSQtIHk2p4B1CC3aUznOSD_7odXvGEpcGyfeJRGs,11344
matplotlib/backends/_backend_pdf_ps.py,sha256=JeLQ9ajcL3uWWSMe4DxvnbjJS_rkD7QLaKSrfPtmXj0,4442
matplotlib/backends/_backend_tk.py,sha256=5mDxZAEqk098TXQ_vB7UydeR3YtMnRmxXJJM-PYOpec,42318
matplotlib/backends/_tkagg.cp312-win_amd64.pyd,sha256=mWOah0UBTCeG9D6rl0bKvINxQXsGGs2WS93lteESVgQ,18432
matplotlib/backends/backend_agg.py,sha256=Jar01BhaFQJB1HuMNRVZM1Nnu_a4VeDR7NtVDA3WMd4,20997
matplotlib/backends/backend_cairo.py,sha256=w14w6UnYKOaOGUIqzh4bWgpiJ_DiPem81eUGbgUAeNM,18214
matplotlib/backends/backend_gtk3.py,sha256=8gZZHYjBUfrCOSzk4j9R3E8eaWgGwFjOugP7Lu3tZsA,22849
matplotlib/backends/backend_gtk3agg.py,sha256=_oekE3SS0mtP1ABPUGxL4XZFPMgZNt0zoDhyMa9ECNA,2464
matplotlib/backends/backend_gtk3cairo.py,sha256=XDkZt3c2kS7bDL_8FDuqDoUlbTpkUBO12AoeXnwXk9w,1022
matplotlib/backends/backend_gtk4.py,sha256=R9ViqSS-HoO5npKGP2ZENuvcao-Xk_RaK2BLfE_tdmU,21724
matplotlib/backends/backend_gtk4agg.py,sha256=hhqtnl666mvww48zpf46UBTJE1-0IqiS3bmGJSuJ0Bo,1261
matplotlib/backends/backend_gtk4cairo.py,sha256=3TfNB9qeKUDXOPdIfsgjR4NOARwEqvq_bne75XnJXU4,1053
matplotlib/backends/backend_macosx.py,sha256=RqyW3-IIHnGmcvvNC9N3Z_SFwJCkRZ74Mqpnzsszu8M,6853
matplotlib/backends/backend_mixed.py,sha256=PAYTjNuunpAa4-JkBguvyOjgDlB0eg9ARDAWidKfJpc,4698
matplotlib/backends/backend_nbagg.py,sha256=zT-xyW-6oHSWjGumMlLB06Hm1KNpOH_a1WD2MhvKTb0,8017
matplotlib/backends/backend_pdf.py,sha256=WF25SxMSxiuAj4rMEQ5p2FiQMm2_OZUDSd3RWwl0mwg,106065
matplotlib/backends/backend_pgf.py,sha256=Kj5bEFnxS1ga_Rw0kH4rKxC0lxt_5KJOPI9CuI-iGbY,40728
matplotlib/backends/backend_ps.py,sha256=ubUpE2KltiqFbUjF3B4Ioz_BstJHJvD9UZTR3kUlWHQ,48024
matplotlib/backends/backend_qt.py,sha256=j-X2y7yg3-eFiAaJueDKclrTS-ogrQDAxpri_hVC8ng,39710
matplotlib/backends/backend_qt5.py,sha256=kzfoo2ksEGsiWAa2LGtZYzKvfzqJJWyGOohohcRAu1g,787
matplotlib/backends/backend_qt5agg.py,sha256=Vh7H8kqWH4X8a3VX2XZ2Vze9srwJavkNHAZxdJUz_bk,352
matplotlib/backends/backend_qt5cairo.py,sha256=Go2Y0GVkXh1xh6x4F255_e5Xbwwws-OiD1Fc0805E78,292
matplotlib/backends/backend_qtagg.py,sha256=8AarIRDc7J1PSrOF3-cpdaGKJ2u4hhgkJqJ9Y4ICr_o,3004
matplotlib/backends/backend_qtcairo.py,sha256=TIIF2nANtBzGFtvGDsV9VqT_8xN4-OKmsLtS1MyisLY,1786
matplotlib/backends/backend_svg.py,sha256=wpiJRB2sMB_Sr0mpjeBOBOPKwjikZzApv7hBqMhrIhw,50665
matplotlib/backends/backend_template.py,sha256=Z352VD5tp_xsNcR-DQcqt-LOB8lXoNzkCzFaMZaS0Dg,8010
matplotlib/backends/backend_tkagg.py,sha256=z9gB16fq2d-DUNpbeSDDLWaYmc0Jz3cDqNlBKhnQg0c,592
matplotlib/backends/backend_tkcairo.py,sha256=JaGGXh8Y5FwVZtgryIucN941Olf_Pn6f4Re7Vuxl1-c,845
matplotlib/backends/backend_webagg.py,sha256=svZCanFQm3FpeqPLpCcE20r39jeWNiKhx7ZAbqVFiOk,11367
matplotlib/backends/backend_webagg_core.py,sha256=66auUjzHunanLK7iSblWgdsfKoMa6-ZFJXSAHMDOhLc,18273
matplotlib/backends/backend_wx.py,sha256=QwU6fl0T9m05tgIZE4VS0PwtxniN4DpusPdc0QTO4MQ,51455
matplotlib/backends/backend_wxagg.py,sha256=WyIAlc6p6jZGgCBlcp7iOjRoyJKrjWOdRaTTCu-KeRM,2000
matplotlib/backends/backend_wxcairo.py,sha256=qT52gI0rRwTre6jd5Xk9BcSLiu96R9Qz-E8dc2hrae0,1468
matplotlib/backends/qt_compat.py,sha256=XUxJDPh0jq8QfbnjaSAKMTNxM7973Mpd5C0JdrBnqqY,8809
matplotlib/backends/qt_editor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/qt_editor/__pycache__/__init__.cpython-312.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formlayout.cpython-312.pyc,,
matplotlib/backends/qt_editor/__pycache__/figureoptions.cpython-312.pyc,,
matplotlib/backends/qt_editor/_formlayout.py,sha256=Z7BJNqoP-TZxlU7mhEBfdt0ftM4UnQNLtiHJPwGBpik,21017
matplotlib/backends/qt_editor/figureoptions.py,sha256=xeba5ul8ZqYHgmCq_yk126Mh5lVxjGyrxxIBk1PMvn4,9521
matplotlib/backends/web_backend/.eslintrc.js,sha256=pZxDrJU80urQlDMoO6A3ylTeZ7DgFoexDhi93Yfm6DU,666
matplotlib/backends/web_backend/.prettierignore,sha256=L47QXaDPUyI-rMmNAmn-OQH-5-Gi04-ZGl7QXdjP7h8,97
matplotlib/backends/web_backend/.prettierrc,sha256=OjC7XB1lRdhntVFThQG-J-wRiqwY1fStHF2i0XTOrbk,145
matplotlib/backends/web_backend/all_figures.html,sha256=44Y-GvIJbNlqQaKSW3kwVKpTxBSG1WsdYz3ZmYHlUsA,1753
matplotlib/backends/web_backend/css/boilerplate.css,sha256=qui16QXRnQFNJDbcMasfH6KtN9hLjv8883U9cJmsVCE,2310
matplotlib/backends/web_backend/css/fbm.css,sha256=wa4vNkNv7fQ_TufjJjecFZEzPMR6W8x6uXJga_wQILw,1456
matplotlib/backends/web_backend/css/mpl.css,sha256=ruca_aA5kNnP-MZmLkriu8teVP1nIgwcFEpoB16j8Z4,1611
matplotlib/backends/web_backend/css/page.css,sha256=ca3nO3TaPw7865PN5SlGJBTc2H3rBXQCMaFPywX29y4,1623
matplotlib/backends/web_backend/ipython_inline_figure.html,sha256=wgSxUh3xpPAxOnZgSMnrhDM5hYncOfWRGgaCUezvedY,1311
matplotlib/backends/web_backend/js/mpl.js,sha256=f52UO4xag_YcbRxCd-fq10eP_oy7ihISgcnrXMEDQyo,23951
matplotlib/backends/web_backend/js/mpl_tornado.js,sha256=Zs2Uzs7YUilG765nYvanCo-IK8HkHDtIum1KAq6bQ_w,302
matplotlib/backends/web_backend/js/nbagg_mpl.js,sha256=F-By4ZjOSmwpNAkxUUxUk35qCjGlf0B28Z0aOyfpxDM,9514
matplotlib/backends/web_backend/nbagg_uat.ipynb,sha256=Fwj_C5fIbm9tkoMYfjS6fdWuOPad3l2Fji7m76xTl8k,16427
matplotlib/backends/web_backend/package.json,sha256=vl3nGaoaVKYGnxlhZP7kVyAqoNmbQbKmEBnQwUWDmwE,545
matplotlib/backends/web_backend/single_figure.html,sha256=wEBwF602JLHErBUEXiS6jXqmxYAIzHpa3MMFrnev6rs,1357
matplotlib/bezier.py,sha256=MhvxLpz4yzhRxi_2EyV0Lvd7PNeBPgWH5A8SBytAmko,18679
matplotlib/category.py,sha256=aDGxoBkadhun-NDpc4clsA2oHS7Ehqj2g2ApnUoV3Ig,7316
matplotlib/cbook/__init__.py,sha256=9UrRE9jSOOheUe-p5ahorAHJJQAVxqxgTCEgpjJCp8I,76849
matplotlib/cbook/__pycache__/__init__.cpython-312.pyc,,
matplotlib/cm.py,sha256=3wlrrNI-X6t-FMYTTW0WWvc5DYTHnsZSVSpDsUv8edo,25236
matplotlib/collections.py,sha256=msp1mstR8VlKBOiHJekV_fjp4rKYmkJm-W1MKdwN9_k,78547
matplotlib/colorbar.py,sha256=2BebS0Xk-v9ePM3pOR20v1lKv1NUpRYtwQKReHeXBds,61224
matplotlib/colors.py,sha256=HihSKrVZxJ7tADI3pUHnGa3Px5AjCX-iLRdxIxsxr4U,96880
matplotlib/container.py,sha256=KdU902svRXMLVxQaOs7sjI7ZHHUl5OCkrDprTT2bApg,4598
matplotlib/contour.py,sha256=3hh27ha0MecrdF0370Ns7Y9wzVMCRx6irtbHwqsDTE8,70179
matplotlib/dates.py,sha256=rgFBhIa1gIyI_mWwIx6gy8xP9aBwV69zYlkWytWmYCQ,69632
matplotlib/docstring.py,sha256=us6poHFe0HcOh1Ws1dGq5zNE6BmJxypD4zaxYIgor54,156
matplotlib/dviread.py,sha256=SIFG8D8nqEJAVCX7JudtKIjuWM7w-fHHRolnXIW8T78,42436
matplotlib/figure.py,sha256=9mq-EtKC3Ny-GmuNu0Q2T4pfZkKDNfRVOYzHrH-7dSo,136091
matplotlib/font_manager.py,sha256=1DnzUP5A1X5IlmxiSKVxUO3UeeMsj8DkG9iD5BC0lOk,54142
matplotlib/fontconfig_pattern.py,sha256=MSREpPxV4XJriSnNuTheakT3UahwB6KJyNUmCR15gPo,680
matplotlib/ft2font.cp312-win_amd64.pyd,sha256=2KlJLrf0u9ggmVyF19-QWyNVPH-ZZRoGdFhyaGg54s8,607232
matplotlib/gridspec.py,sha256=kBkSN5RN1JfXAUSQaC8dZC9-5CkxLhbowCD9scGsxvc,27863
matplotlib/hatch.py,sha256=iuEJDM2QQWBnF2MlJfkg-NhHjdKW4r2ondKCQ-C-Ii0,7449
matplotlib/image.py,sha256=ZZ_qvqYJKOuxPkLS-0IgqzZMuVC1t_l7UwGbjeb6NZ8,71668
matplotlib/layout_engine.py,sha256=jPd3a7DYdMVcOPht2mc2Zwbk7sAKLL9g5jtw1aIlloo,11335
matplotlib/legend.py,sha256=4mZvIVz6sV73KbvONUbMo2iHM_WfFM-i7F9bnYgyY-o,54082
matplotlib/legend_handler.py,sha256=C6Ejb-lkcpPGnLm23aIVJR7N-8Fpa6pYJTXSOdsJ8pE,30146
matplotlib/lines.py,sha256=3-G7iFmTWtZtiPaPwKqYszc1fyTdSdu-VEOLTBy7zXw,54538
matplotlib/markers.py,sha256=OPw95McLSaurhfdf8k662uZ-7ZB_S0vWML14qh69I1E,35135
matplotlib/mathtext.py,sha256=V7SdRMQWbNDJEsvempcxJhePn-DBjdkzvbvKOad2SNA,9093
matplotlib/mlab.py,sha256=ovhN2myxrvDiY5oSRCdBTr9OKb34dD0zIP5Dj9GnUVM,32636
matplotlib/mpl-data/fonts/afm/cmex10.afm,sha256=blR3ERmrVBV5XKkAnDCj4NMeYVgzH7cXtJ3u59u9GuE,12070
matplotlib/mpl-data/fonts/afm/cmmi10.afm,sha256=5qwEOpedEo76bDUahyuuF1q0cD84tRrX-VQ4p3MlfBo,10416
matplotlib/mpl-data/fonts/afm/cmr10.afm,sha256=WDvgC_D3UkGJg9u-J0U6RaT02lF4oz3lQxHtg1r3lYw,10101
matplotlib/mpl-data/fonts/afm/cmsy10.afm,sha256=AbmzvCVWBceHRfmRfeJ9E6xzOQTFLk0U1zDfpf3_MaM,8295
matplotlib/mpl-data/fonts/afm/cmtt10.afm,sha256=4ji7_mTpeWMa93o_UHBWPKCnqsBfhJJNllat1lJArP4,6501
matplotlib/mpl-data/fonts/afm/pagd8a.afm,sha256=jjFrigwkTpYLqa26cpzZvKQNBo-PuF4bmDVqaM4pMWw,17183
matplotlib/mpl-data/fonts/afm/pagdo8a.afm,sha256=sgNQdeYyx8J-itGw9h31y95aMBiTCRvmNSPTXwwS7xg,17255
matplotlib/mpl-data/fonts/afm/pagk8a.afm,sha256=ZUtfHPloNqcvGMHMxaKDSlshhOcjwheUx143RwpGdIU,17241
matplotlib/mpl-data/fonts/afm/pagko8a.afm,sha256=Yj1wBg6Jsqqz1KBfhRoJ3ACR-CMQol8Fj_ZM5NZ1gDk,17346
matplotlib/mpl-data/fonts/afm/pbkd8a.afm,sha256=Zl5o6J_di9Y5j2EpHtjew-_sfg7-WoeVmO9PzOYSTUc,15157
matplotlib/mpl-data/fonts/afm/pbkdi8a.afm,sha256=JAOno930iTyfZILMf11vWtiaTgrJcPpP6FRTRhEMMD4,15278
matplotlib/mpl-data/fonts/afm/pbkl8a.afm,sha256=UJqJjOJ6xQDgDBLX157mKpohIJFVmHM-N6x2-DiGv14,15000
matplotlib/mpl-data/fonts/afm/pbkli8a.afm,sha256=AWislZ2hDbs0ox_qOWREugsbS8_8lpL48LPMR40qpi0,15181
matplotlib/mpl-data/fonts/afm/pcrb8a.afm,sha256=6j1TS2Uc7DWSc-8l42TGDc1u0Fg8JspeWfxFayjUwi8,15352
matplotlib/mpl-data/fonts/afm/pcrbo8a.afm,sha256=smg3mjl9QaBDtQIt06ko5GvaxLsO9QtTvYANuE5hfG0,15422
matplotlib/mpl-data/fonts/afm/pcrr8a.afm,sha256=7nxFr0Ehz4E5KG_zSE5SZOhxRH8MyfnCbw-7x5wu7tw,15339
matplotlib/mpl-data/fonts/afm/pcrro8a.afm,sha256=NKEz7XtdFkh9cA8MvY-S3UOZlV2Y_J3tMEWFFxj7QSg,15443
matplotlib/mpl-data/fonts/afm/phvb8a.afm,sha256=NAx4M4HjL7vANCJbc-tk04Vkol-T0oaXeQ3T2h-XUvM,17155
matplotlib/mpl-data/fonts/afm/phvb8an.afm,sha256=8e_myD-AQkNF7q9XNLb2m76_lX2TUr3a5wog_LIE1sk,17086
matplotlib/mpl-data/fonts/afm/phvbo8a.afm,sha256=8fkBRmJ-SWY2YrBg8fFyjJyrJp8daQ6JPO6LvhM8xPI,17230
matplotlib/mpl-data/fonts/afm/phvbo8an.afm,sha256=aeVRvV4r15BBvxuRJ0MG8ZHuH2HViuIiCYkvuapmkmM,17195
matplotlib/mpl-data/fonts/afm/phvl8a.afm,sha256=IyMYM-bgl-gI6rG0EuZZ2OLzlxJfGeSh8xqsh0t-eJQ,15627
matplotlib/mpl-data/fonts/afm/phvlo8a.afm,sha256=s12C-eNnIDHJ_UVbuiprjxBjCiHIbS3Y8ORTC-qTpuI,15729
matplotlib/mpl-data/fonts/afm/phvr8a.afm,sha256=Kt8KaRidts89EBIK29X2JomDUEDxvroeaJz_RNTi6r4,17839
matplotlib/mpl-data/fonts/afm/phvr8an.afm,sha256=lL5fAHTRwODl-sB5mH7IfsD1tnnea4yRUK-_Ca2bQHM,17781
matplotlib/mpl-data/fonts/afm/phvro8a.afm,sha256=3KqK3eejiR4hIFBUynuSX_4lMdE2V2T58xOF8lX-fwc,17919
matplotlib/mpl-data/fonts/afm/phvro8an.afm,sha256=Vx9rRf3YfasMY7tz-njSxz67xHKk-fNkN7yBi0X2IP0,17877
matplotlib/mpl-data/fonts/afm/pncb8a.afm,sha256=aoXepTcDQtQa_mspflMJkEFKefzXHoyjz6ioJVI0YNc,16028
matplotlib/mpl-data/fonts/afm/pncbi8a.afm,sha256=pCWW1MYgy0EmvwaYsaYJaAI_LfrsKmDANHu7Pk0RaiU,17496
matplotlib/mpl-data/fonts/afm/pncr8a.afm,sha256=0CIB2BLe9r-6_Wl5ObRTTf98UOrezmGQ8ZOuBX5kLks,16665
matplotlib/mpl-data/fonts/afm/pncri8a.afm,sha256=5R-pLZOnaHNG8pjV6MP3Ai-d2OTQYR_cYCb5zQhzfSU,16920
matplotlib/mpl-data/fonts/afm/pplb8a.afm,sha256=3EzUbNnXr5Ft5eFLY00W9oWu59rHORgDXUuJaOoKN58,15662
matplotlib/mpl-data/fonts/afm/pplbi8a.afm,sha256=X_9tVspvrcMer3OS8qvdwjFFqpAXYZneyCL2NHA902g,15810
matplotlib/mpl-data/fonts/afm/pplr8a.afm,sha256=ijMb497FDJ9nVdVMb21F7W3-cu9sb_9nF0oriFpSn8k,15752
matplotlib/mpl-data/fonts/afm/pplri8a.afm,sha256=8KITbarcUUMi_hdoRLLmNHtlqs0TtOSKqtPFft7X5nY,15733
matplotlib/mpl-data/fonts/afm/psyr.afm,sha256=Iyt8ajE4B2Tm34oBj2pKtctIf9kPfq05suQefq8p3Ro,9644
matplotlib/mpl-data/fonts/afm/ptmb8a.afm,sha256=bL1fA1NC4_nW14Zrnxz4nHlXJb4dzELJPvodqKnYeMg,17983
matplotlib/mpl-data/fonts/afm/ptmbi8a.afm,sha256=-_Ui6XlKaFTHEnkoS_-1GtIr5VtGa3gFQ2ezLOYHs08,18070
matplotlib/mpl-data/fonts/afm/ptmr8a.afm,sha256=IEcsWcmzJyjCwkgsw4o6hIMmzlyXUglJat9s1PZNnEU,17942
matplotlib/mpl-data/fonts/afm/ptmri8a.afm,sha256=49fQMg5fIGguZ7rgc_2styMK55Pv5bPTs7wCzqpcGpk,18068
matplotlib/mpl-data/fonts/afm/putb8a.afm,sha256=qMaHTdpkrNL-m4DWhjpxJCSmgYkCv1qIzLlFfM0rl40,21532
matplotlib/mpl-data/fonts/afm/putbi8a.afm,sha256=g7AVJyiTxeMpNk_1cSfmYgM09uNUfPlZyWGv3D1vcAk,21931
matplotlib/mpl-data/fonts/afm/putr8a.afm,sha256=XYmNC5GQgSVAZKTIYdYeNksE6znNm9GF_0SmQlriqx0,22148
matplotlib/mpl-data/fonts/afm/putri8a.afm,sha256=i7fVe-iLyLtQxCfAa4IxdxH-ufcHmMk7hbCGG5TxAY4,21891
matplotlib/mpl-data/fonts/afm/pzcmi8a.afm,sha256=wyuoIWEZOcoXrSl1tPzLkEahik7kGi91JJj-tkFRG4A,16250
matplotlib/mpl-data/fonts/afm/pzdr.afm,sha256=MyjLAnzKYRdQBfof1W3k_hf30MvqOkqL__G22mQ5xww,9467
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm,sha256=sIDDI-B82VZ3C0mI_mHFITCZ7PVn37AIYMv1CrHX4sE,15333
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm,sha256=zg61QobD3YU9UBfCXmvmhBNaFKno-xj8sY0b2RpgfLw,15399
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm,sha256=vRQm5j1sTUN4hicT1PcVZ9P9DTTUHhEzfPXqUUzVZhE,15441
matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm,sha256=Mdcq2teZEBJrIqVXnsnhee7oZnTs6-P8_292kWGTrw4,15335
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm,sha256=i2l4gcjuYXoXf28uK7yIVwuf0rnw6J7PwPVQeHj5iPw,69269
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm,sha256=Um5O6qK11DXLt8uj_0IoWkc84TKqHK3bObSKUswQqvY,69365
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm,sha256=hVYDg2b52kqtbVeCzmiv25bW1yYdpkZS-LXlGREN2Rs,74392
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm,sha256=23cvKDD7bQAJB3kdjSahJSTZaUOppznlIO6FXGslyW8,74292
matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm,sha256=P5UaoXr4y0qh4SiMa5uqijDT6ZDr2-jPmj1ayry593E,9740
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm,sha256=cQTmr2LFPwKQE_sGQageMcmFicjye16mKJslsJLHQyE,64251
matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm,sha256=pzWOdycm6RqocBWgAVY5Jq0z3Fp7LuqWgLNMx4q6OFw,59642
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm,sha256=bK5puSMpGT_YUILwyJrXoxjfj7XJOdfv5TQ_iKsJRzw,66328
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm,sha256=hhNrUnpazuDDKD1WpraPxqPWCYLrO7D7bMVOg-zI13o,60460
matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm,sha256=ZuOmt9GcKofjdOq8kqhPhtAIhOwkL2rTJTmZxAjFakA,9527
matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt,sha256=MRv8ppSITYYAb7lt5EOw9DWWNZIblfxsFhu5TQE7cpI,828
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf,sha256=sYS4njwQdfIva3FXW2_CDUlys8_TsjMiym_Vltyu8Wc,704128
matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf,sha256=bt8CgxYBhq9FHL7nHnuEXy5Mq_Jku5ks5mjIPCVGXm8,641720
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf,sha256=zN90s1DxH9PdV3TeUOXmNGoaXaH1t9X7g1kGZel6UhM,633840
matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf,sha256=P99pyr8GBJ6nCgC1kZNA4s4ebQKwzDxLRPtoAb0eDSI,756072
matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf,sha256=ggmdz7paqGjN_CdFGYlSX-MpL3N_s8ngMozpzvWWUvY,25712
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf,sha256=uq2ppRcv4giGJRr_BDP8OEYZEtXa8HKH577lZiCo2pY,331536
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf,sha256=ppCBwVx2yCfgonpaf1x0thNchDSZlVSV_6jCDTqYKIs,253116
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf,sha256=KAUoE_enCfyJ9S0ZLcmV708P3Fw9e3OknWhJsZFtDNA,251472
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf,sha256=YC7Ia4lIz82VZIL-ZPlMNshndwFJ7y95HUYT9EO87LM,340240
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf,sha256=w3U_Lta8Zz8VhG3EWt2-s7nIcvMvsY_VOiHxvvHtdnY,355692
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf,sha256=2T7-x6nS6CZ2jRou6VuVhw4V4pWZqE80hK8d4c7C4YE,347064
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf,sha256=PnmU-8VPoQzjNSpC1Uj63X2crbacsRCbydlg9trFfwQ,345612
matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf,sha256=EHJElW6ZYrnpb6zNxVGCXgrgiYrhNzcTPhuSGi_TX_o,379740
matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf,sha256=KRTzLkfHd8J75Wd6-ufbTeefnkXeb8kJfZlJwjwU99U,14300
matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU,sha256=11k43sCY8G8Kw8AIUwZdlPAgvhw8Yu8dwpdboVtNmw4,4816
matplotlib/mpl-data/fonts/ttf/LICENSE_STIX,sha256=urPTHf7wf0g2JPL2XycR52BluOcnMnixwHHt4QQcmVk,5476
matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf,sha256=FnN4Ax4t3cYhbWeBnJJg6aBv_ExHjk4jy5im_USxg8I,448228
matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf,sha256=6FM9xwg_o0a9oZM9YOpKg7Z9CUW86vGzVB-CtKDixqA,237360
matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf,sha256=mHiP1LpI37sr0CbA4gokeosGxzcoeWKLemuw1bsJc2w,181152
matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf,sha256=bPyzM9IrfDxiO9_UAXTxTIXD1nMcphZsHtyAFA6uhSc,175040
matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf,sha256=Ulb34CEzWsSFTRgPDovxmJZOwvyCAXYnbhaqvGU3u1c,59108
matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf,sha256=XRBqW3jR_8MBdFU0ObhiV7-kXwiBIMs7QVClHcT5tgs,30512
matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf,sha256=pb22DnbDf2yQqizotc3wBDqFGC_g27YcCGJivH9-Le8,41272
matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf,sha256=BMr9pWiBv2YIZdq04X4c3CgL6NPLUPrl64aV1N4w9Ug,46752
matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf,sha256=wYuH1gYUpCuusqItRH5kf9p_s6mUD-9X3L5RvRtKSxs,13656
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf,sha256=yNdvjUoSmsZCULmD7SVq9HabndG9P4dPhboL1JpAf0s,12228
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf,sha256=-9xVMYL4_1rcO8FiCKrCfR4PaSmKtA42ddLGqwtei1w,15972
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf,sha256=cYexyo8rZcdqMlpa9fNF5a2IoXLUTZuIvh0JD1Qp0i4,12556
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf,sha256=0lbHzpndzJmO8S42mlkhsz5NbvJLQCaH5Mcc7QZRDzc,19760
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf,sha256=3eBc-VtYbhQU3BnxiypfO6eAzEu8BdDvtIJSFbkS2oY,12192
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf,sha256=XFSKCptbESM8uxHtUFSAV2cybwxhSjd8dWVByq6f3w0,15836
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf,sha256=MUCYHrA0ZqFiSE_PjIGlJZgMuv79aUgQqE7Dtu3kuo0,12116
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf,sha256=_sdxDuEwBDtADpu9CyIXQxV7sIqA2TZVBCUiUjq5UCk,15704
matplotlib/mpl-data/fonts/ttf/cmb10.ttf,sha256=B0SXtQxD6ldZcYFZH5iT04_BKofpUQT1ZX_CSB9hojo,25680
matplotlib/mpl-data/fonts/ttf/cmex10.ttf,sha256=ryjwwXByOsd2pxv6WVrKCemNFa5cPVTOGa_VYZyWqQU,21092
matplotlib/mpl-data/fonts/ttf/cmmi10.ttf,sha256=MJKWW4gR_WpnZXmWZIRRgfwd0TMLk3-RWAjEhdMWI00,32560
matplotlib/mpl-data/fonts/ttf/cmr10.ttf,sha256=Tdl2GwWMAJ25shRfVe5mF9CTwnPdPWxbPkP_YRD6m_Y,26348
matplotlib/mpl-data/fonts/ttf/cmss10.ttf,sha256=ffkag9BbLkcexjjLC0NaNgo8eSsJ_EKn2mfpHy55EVo,20376
matplotlib/mpl-data/fonts/ttf/cmsy10.ttf,sha256=uyJu2TLz8QDNDlL15JEu5VO0G2nnv9uNOFTbDrZgUjI,29396
matplotlib/mpl-data/fonts/ttf/cmtt10.ttf,sha256=YhHwmuk1mZka_alwwkZp2tGnfiU9kVYk-_IS9wLwcdc,28136
matplotlib/mpl-data/images/back-symbolic.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back.pdf,sha256=ZR7CJo_dAeCM-KlaGvskgtHQyRtrPIolc8REOmcoqJk,1623
matplotlib/mpl-data/images/back.png,sha256=E4dGf4Gnz1xJ1v2tMygHV0YNQgShreDeVApaMb-74mU,380
matplotlib/mpl-data/images/back.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back_large.png,sha256=9A6hUSQeszhYONE4ZuH3kvOItM0JfDVu6tkfromCbsQ,620
matplotlib/mpl-data/images/filesave-symbolic.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave.pdf,sha256=P1EPPV2g50WTt8UaX-6kFoTZM1xVqo6S2H6FJ6Zd1ec,1734
matplotlib/mpl-data/images/filesave.png,sha256=b7ctucrM_F2mG-DycTedG_a_y4pHkx3F-zM7l18GLhk,458
matplotlib/mpl-data/images/filesave.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave_large.png,sha256=LNbRD5KZ3Kf7nbp-stx_a1_6XfGBSWUfDdpgmnzoRvk,720
matplotlib/mpl-data/images/forward-symbolic.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward.pdf,sha256=KIqIL4YId43LkcOxV_TT5uvz1SP8k5iUNUeJmAElMV8,1630
matplotlib/mpl-data/images/forward.png,sha256=pKbLepgGiGeyY2TCBl8svjvm7Z4CS3iysFxcq4GR-wk,357
matplotlib/mpl-data/images/forward.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward_large.png,sha256=36h7m7DZDHql6kkdpNPckyi2LKCe_xhhyavWARz_2kQ,593
matplotlib/mpl-data/images/hand.pdf,sha256=hspwkNY915KPD7AMWnVQs7LFPOtlcj0VUiLu76dMabQ,4172
matplotlib/mpl-data/images/hand.png,sha256=2cchRETGKa0hYNKUxnJABwkyYXEBPqJy_VqSPlT0W2Q,979
matplotlib/mpl-data/images/hand.svg,sha256=tsVIES_nINrAbH4FqdsCGOx0SVE37vcofSYBhnnaOP0,4888
matplotlib/mpl-data/images/help-symbolic.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help.pdf,sha256=CeE978IMi0YWznWKjIT1R8IrP4KhZ0S7usPUvreSgcA,1813
matplotlib/mpl-data/images/help.png,sha256=s4pQrqaQ0py8I7vc9hv3BI3DO_tky-7YBMpaHuBDCBY,472
matplotlib/mpl-data/images/help.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help_large.png,sha256=1IwEyWfGRgnoCWM-r9CJHEogTJVD5n1c8LXTK4AJ4RE,747
matplotlib/mpl-data/images/home-symbolic.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home.pdf,sha256=e0e0pI-XRtPmvUCW2VTKL1DeYu1pvPmUUeRSgEbWmik,1737
matplotlib/mpl-data/images/home.png,sha256=IcFdAAUa6_A0qt8IO3I8p4rpXpQgAlJ8ndBECCh7C1w,468
matplotlib/mpl-data/images/home.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home_large.png,sha256=uxS2O3tWOHh1iau7CaVV4ermIJaZ007ibm5Z3i8kXYg,790
matplotlib/mpl-data/images/matplotlib.pdf,sha256=BkSUf-2xoij-eXfpV2t7y1JFKG1zD1gtV6aAg3Xi_wE,22852
matplotlib/mpl-data/images/matplotlib.png,sha256=w8KLRYVa-voUZXa41hgJauQuoois23f3NFfdc72pUYY,1283
matplotlib/mpl-data/images/matplotlib.svg,sha256=QiTIcqlQwGaVPtHsEk-vtmJk1wxwZSvijhqBe_b9VCI,62087
matplotlib/mpl-data/images/matplotlib_large.png,sha256=ElRoue9grUqkZXJngk-nvh4GKfpvJ4gE69WryjCbX5U,3088
matplotlib/mpl-data/images/move-symbolic.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move.pdf,sha256=CXk3PGK9WL5t-5J-G2X5Tl-nb6lcErTBS5oUj2St6aU,1867
matplotlib/mpl-data/images/move.png,sha256=TmjR41IzSzxGbhiUcV64X0zx2BjrxbWH3cSKvnG2vzc,481
matplotlib/mpl-data/images/move.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move_large.png,sha256=Skjz2nW_RTA5s_0g88gdq2hrVbm6DOcfYW4Fu42Fn9U,767
matplotlib/mpl-data/images/qt4_editor_options.pdf,sha256=2qu6GVyBrJvVHxychQoJUiXPYxBylbH2j90QnytXs_w,1568
matplotlib/mpl-data/images/qt4_editor_options.png,sha256=EryQjQ5hh2dwmIxtzCFiMN1U6Tnd11p1CDfgH5ZHjNM,380
matplotlib/mpl-data/images/qt4_editor_options.svg,sha256=E00YoX7u4NrxMHm_L1TM8PDJ88bX5qRdCrO-Uj59CEA,1244
matplotlib/mpl-data/images/qt4_editor_options_large.png,sha256=-Pd-9Vh5aIr3PZa8O6Ge_BLo41kiEnpmkdDj8a11JkY,619
matplotlib/mpl-data/images/subplots-symbolic.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots.pdf,sha256=Q0syPMI5EvtgM-CE-YXKOkL9eFUAZnj_X2Ihoj6R4p4,1714
matplotlib/mpl-data/images/subplots.png,sha256=MUfCItq3_yzb9yRieGOglpn0Y74h8IA7m5i70B63iRc,445
matplotlib/mpl-data/images/subplots.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots_large.png,sha256=Edu9SwVMQEXJZ5ogU5cyW7VLcwXJdhdf-EtxxmxdkIs,662
matplotlib/mpl-data/images/zoom_to_rect-symbolic.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect.pdf,sha256=SEvPc24gfZRpl-dHv7nx8KkxPyU66Kq4zgQTvGFm9KA,1609
matplotlib/mpl-data/images/zoom_to_rect.png,sha256=aNz3QZBrIgxu9E-fFfaQweCVNitGuDUFoC27e5NU2L4,530
matplotlib/mpl-data/images/zoom_to_rect.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect_large.png,sha256=V6pkxmm6VwFExdg_PEJWdK37HB7k3cE_corLa7RbUMk,1016
matplotlib/mpl-data/kpsewhich.lua,sha256=RdyYaBnBLy3NsB5c2R5FGrKu-V-WBcZim24NWilsTfw,139
matplotlib/mpl-data/matplotlibrc,sha256=qGoAlhPZJGAC6a0MlMKiTItSSIc8F5-2EJzpvhxAjZs,42626
matplotlib/mpl-data/plot_directive/plot_directive.css,sha256=utSJ1oETz0UG6AC9hU134J_JY78ENijqMZXN0JMBUfk,318
matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png,sha256=XnKGiCanpDKalQ5anvo5NZSAeDP7fyflzQAaivuc0IE,13634
matplotlib/mpl-data/sample_data/README.txt,sha256=ABz19VBKfGewdY39QInG9Qccgn1MTYV3bT5Ph7TCy2Y,128
matplotlib/mpl-data/sample_data/Stocks.csv,sha256=72878aZNXGxd5wLvFUw_rnj-nfg4gqtrucZji-w830c,67924
matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy,sha256=DpWZ9udAh6ospYqneEa27D6EkRgORFwHosacZXVu98U,1880
matplotlib/mpl-data/sample_data/data_x_x2_x3.csv,sha256=A0SU3buOUGhT-NI_6LQ6p70fFSIU3iLFdgzvzrKR6SE,132
matplotlib/mpl-data/sample_data/eeg.dat,sha256=KGVjFt8ABKz7p6XZirNfcxSTOpGGNuyA8JYErRKLRBc,25600
matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc,sha256=cUqVw5vDHNSZoaO4J0ebZUf5SrJP36775abs7R9Bclg,2186
matplotlib/mpl-data/sample_data/goog.npz,sha256=QAkXzzDmtmT3sNqT18dFhg06qQCNqLfxYNLdEuajGLE,22845
matplotlib/mpl-data/sample_data/grace_hopper.jpg,sha256=qMptc0dlcDsJcoq0f-WfRz2Trjln_CTHwCiMPHrbcTA,61306
matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz,sha256=1JP1CjPoKkQgSUxU0fyhU50Xe9wnqxkLxf5ukvYvtjc,174061
matplotlib/mpl-data/sample_data/logo2.png,sha256=ITxkJUsan2oqXgJDy6DJvwJ4aHviKeWGnxPkTjXUt7A,33541
matplotlib/mpl-data/sample_data/membrane.dat,sha256=q3lbQpIBpbtXXGNw1eFwkN_PwxdDGqk4L46IE2b0M1c,48000
matplotlib/mpl-data/sample_data/msft.csv,sha256=GArKb0O3DgKZRsKdJf6lX3rMSf-PCekIiBoLNdgF7Mk,3211
matplotlib/mpl-data/sample_data/percent_bachelors_degrees_women_usa.csv,sha256=TzoqamsV_N3d3lW7SKmj14zZVX4FOOg9jJcsC5U9pbA,5681
matplotlib/mpl-data/sample_data/s1045.ima.gz,sha256=MrQk1k9it-ccsk0p_VOTitVmTWCAVaZ6srKvQ2n4uJ4,33229
matplotlib/mpl-data/sample_data/topobathy.npz,sha256=AkTgMpFwLfRQJNy1ysvE89TLMNct-n_TccSsYcQrT78,45224
matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle,sha256=aytOm4eT_SPvs7HC28ZY4GukeN44q-SE0JEMCR8kVOk,1257
matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle,sha256=iopHpMaM3im_AK2aiHGuM2DKM5i9Kc84v6NQEoSb10Q,167
matplotlib/mpl-data/stylelib/_mpl-gallery-nogrid.mplstyle,sha256=1VOL3USqD6iuGQaSynNg1QhyUwvKLnkLyUKdbBMnnqg,489
matplotlib/mpl-data/stylelib/_mpl-gallery.mplstyle,sha256=MN-q59CiDqHXB8xFKXxzCbJJbJmNDhBe9lDJJAoMTPA,504
matplotlib/mpl-data/stylelib/bmh.mplstyle,sha256=-KbhaI859BITHIoyUZIfpQDjfckgLAlDAS_ydKsm6mc,712
matplotlib/mpl-data/stylelib/classic.mplstyle,sha256=kvFmnvSBXWdUHTsPl5t3Dg8wC1ANybioJRX09AyWGvc,24599
matplotlib/mpl-data/stylelib/dark_background.mplstyle,sha256=Ht6phZUy3zNRdcfHKcSb1uh3O8DunSPX8HPt9xTyzuo,658
matplotlib/mpl-data/stylelib/fast.mplstyle,sha256=yTa2YEIIP9xi5V_G0p2vSlxghuhNwjRi9gPECMxyRiM,288
matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle,sha256=WNUmAFuBPcqQPVgt6AS1ldy8Be2XO01N-1YQL__Q6ZY,832
matplotlib/mpl-data/stylelib/ggplot.mplstyle,sha256=u2oPHMLWFtZcpIjHk2swi2Nrt4NgnEtof5lxcwM0RD0,956
matplotlib/mpl-data/stylelib/grayscale.mplstyle,sha256=KCLg-pXpns9cnKDXKN2WH6mV41OH-6cbT-5zKQotSdw,526
matplotlib/mpl-data/stylelib/seaborn-v0_8-bright.mplstyle,sha256=pDqn3-NUyVLvlfkYs8n8HzNZvmslVMChkeH-HtZuJIc,144
matplotlib/mpl-data/stylelib/seaborn-v0_8-colorblind.mplstyle,sha256=eCSzFj5_2vR6n5qu1rHE46wvSVGZcdVqz85ov40ZsH8,148
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark-palette.mplstyle,sha256=p5ABKNQHRG7bk4HXqMQrRBjDlxGAo3RCXHdQmP7g-Ng,142
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark.mplstyle,sha256=I4xQ75vE5_9X4k0cNDiqhhnF3OcrZ2xlPX8Ll7OCkoE,667
matplotlib/mpl-data/stylelib/seaborn-v0_8-darkgrid.mplstyle,sha256=2bXOSzS5gmPzRBrRmzVWyhg_7ZaBRQ6t_-O-cRuyZoA,670
matplotlib/mpl-data/stylelib/seaborn-v0_8-deep.mplstyle,sha256=44dLcXjjRgR-6yaopgGRInaVgz3jk8VJVQTbBIcxRB0,142
matplotlib/mpl-data/stylelib/seaborn-v0_8-muted.mplstyle,sha256=T4o3jvqKD_ImXDkp66XFOV_xrBVFUolJU34JDFk1Xkk,143
matplotlib/mpl-data/stylelib/seaborn-v0_8-notebook.mplstyle,sha256=PcvZQbYrDdducrNlavBPmQ1g2minio_9GkUUFRdgtoM,382
matplotlib/mpl-data/stylelib/seaborn-v0_8-paper.mplstyle,sha256=n0mboUp2C4Usq2j6tNWcu4TZ_YT4-kKgrYO0t-rz1yw,393
matplotlib/mpl-data/stylelib/seaborn-v0_8-pastel.mplstyle,sha256=8nV8qRpbUrnFZeyE6VcQ1oRuZPLil2W74M2U37DNMOE,144
matplotlib/mpl-data/stylelib/seaborn-v0_8-poster.mplstyle,sha256=dUaKqTE4MRfUq2rWVXbbou7kzD7Z9PE9Ko8aXLza8JA,403
matplotlib/mpl-data/stylelib/seaborn-v0_8-talk.mplstyle,sha256=7FnBaBEdWBbncTm6_ER-EQVa_bZgU7dncgez-ez8R74,403
matplotlib/mpl-data/stylelib/seaborn-v0_8-ticks.mplstyle,sha256=CITZmZFUFp40MK2Oz8tI8a7WRoCizQU9Z4J172YWfWw,665
matplotlib/mpl-data/stylelib/seaborn-v0_8-white.mplstyle,sha256=WjJ6LEU6rlCwUugToawciAbKP9oERFHr9rfFlUrdTx0,665
matplotlib/mpl-data/stylelib/seaborn-v0_8-whitegrid.mplstyle,sha256=ec4BjsNzmOvHptcJ3mdPxULF3S1_U1EUocuqfIpw-Nk,664
matplotlib/mpl-data/stylelib/seaborn-v0_8.mplstyle,sha256=_Xu6qXKzi4b3GymCOB1b1-ykKTQ8xhDliZ8ezHGTiAs,1130
matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle,sha256=BsirZVd1LmPWT4tBIz6loZPjZcInoQrIGfC7rvzqmJw,190
matplotlib/offsetbox.py,sha256=tkzgoBk0vFihjgOfvJHFNTD_-9-Ig3WTXmtjx0raTYg,54719
matplotlib/patches.py,sha256=t6yggzyJsN_xV0prRBllQmH-_y264LVrb-3C5bShRW8,159284
matplotlib/path.py,sha256=Bwsk7Y7b3urZXPMl9SsSLZGJTSUGC6h4sgvMEZxNjSI,41305
matplotlib/patheffects.py,sha256=5zlC-wRF2hHAgK6roz_ywty9PorX8VUG_w0aC94fIgk,18595
matplotlib/projections/__init__.py,sha256=TaQxkt9vITCr_NLC17n7hm5jvoBaxSph7daVvWFqIUc,3998
matplotlib/projections/__pycache__/__init__.cpython-312.pyc,,
matplotlib/projections/__pycache__/geo.cpython-312.pyc,,
matplotlib/projections/__pycache__/polar.cpython-312.pyc,,
matplotlib/projections/geo.py,sha256=AWRunVIOTh82dnp_ZPamBtMY5piuH0303Tt9yNW3o60,17330
matplotlib/projections/polar.py,sha256=39L-NDdoVrbZpP-q142k5NVXuH8N3Rvl1lWerpX67i8,55375
matplotlib/pylab.py,sha256=30kKK6tYXaEQ7vL2CkJcAs_JqWaqi37dGdjj_XdJDgA,1798
matplotlib/pyplot.py,sha256=B89e3t1zk5YjTYs-n233lrqULWp7GLAtBWqD3clAoq8,115971
matplotlib/quiver.py,sha256=jU7n4CGywtPgIGXGxIzPy1_ud4_CQiCAuGffiPHTq6I,46184
matplotlib/rcsetup.py,sha256=NlrjOxHd2Ku92p5HvfG19TFk6nkd72B4ZHE_RIlFDPg,48578
matplotlib/sankey.py,sha256=AtUWRfI0LQZqqTtc0-1VgZ3W3Ptc5Lu1Ig9RKglM45k,36697
matplotlib/scale.py,sha256=xDY1p-b72j7niZcmU_XscwZ2ismU6Xt4U22mqeulOAY,25490
matplotlib/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/sphinxext/__pycache__/__init__.cpython-312.pyc,,
matplotlib/sphinxext/__pycache__/mathmpl.cpython-312.pyc,,
matplotlib/sphinxext/__pycache__/plot_directive.cpython-312.pyc,,
matplotlib/sphinxext/mathmpl.py,sha256=bihUf5HE7mYQPs2_2lxI82Qp9SHFP5Da8UwC-KesXZM,7460
matplotlib/sphinxext/plot_directive.py,sha256=mJFn3TBhf39U7Qm1cgejGEbcDB5tGGtcmCtdUStNih0,28803
matplotlib/spines.py,sha256=QrQxfh1P_hs__3epdCght_LA8k4VkBjdJ2Lcn60EewE,21321
matplotlib/stackplot.py,sha256=KSDJZVINpNcEkvgCIxkIbNAvd2s3yJjbCwVb0Zspmaw,4189
matplotlib/streamplot.py,sha256=bmYtfXm14H1rOJM0B4RTRYY8ybl12HrGhwfgI9iFXhU,23757
matplotlib/style/__init__.py,sha256=fraQtyBC3TY2ZTsLdxL7zNs9tJYZtje7tbiqEf3M56M,140
matplotlib/style/__pycache__/__init__.cpython-312.pyc,,
matplotlib/style/__pycache__/core.cpython-312.pyc,,
matplotlib/style/core.py,sha256=mUHGdpDTTpFaKKH9RMqZWAOg9mY2M2-TQbvdO8Y7aRI,9905
matplotlib/table.py,sha256=Vl6IQa-qxgGlqb2422Dorjryg1Z7-fFyL1MR2diC3kg,26968
matplotlib/testing/__init__.py,sha256=pounzyRu_AdVFB58_agoq4N6gGtzpSf0YKoqI7Py3bw,3232
matplotlib/testing/__pycache__/__init__.cpython-312.pyc,,
matplotlib/testing/__pycache__/_markers.cpython-312.pyc,,
matplotlib/testing/__pycache__/compare.cpython-312.pyc,,
matplotlib/testing/__pycache__/conftest.cpython-312.pyc,,
matplotlib/testing/__pycache__/decorators.cpython-312.pyc,,
matplotlib/testing/__pycache__/exceptions.cpython-312.pyc,,
matplotlib/testing/__pycache__/widgets.cpython-312.pyc,,
matplotlib/testing/_markers.py,sha256=8PeZ8wo52FNCR_knzbcha2EvytGJZ76BelhaBl5v9xk,1411
matplotlib/testing/compare.py,sha256=WYEFgK8qadm_PxZof2Fv2s7xPqFj9mf4vjMks7u2GPA,19108
matplotlib/testing/conftest.py,sha256=mLDGehFQhOOGZ3BwuAzw-at4Mkd2gIer2j8u7fQaaPw,3604
matplotlib/testing/decorators.py,sha256=RS0wzw5Epscqd-o1NfwQxIjQ5gRdO-h_h6788Ydk0G4,20299
matplotlib/testing/exceptions.py,sha256=72QmjiHG7DwxSvlJf8mei-hRit5AH3NKh0-osBo4YbY,138
matplotlib/testing/jpl_units/Duration.py,sha256=Wy88eqhOZUHBIFaH9u3oQ5-v_63U3tYHKMvJ71FMnUk,3974
matplotlib/testing/jpl_units/Epoch.py,sha256=5FWT1zM9plNJTG9h5TbE2NrXdjkyh314ghHtH4FaalQ,6104
matplotlib/testing/jpl_units/EpochConverter.py,sha256=moULv8w8ArfiKqqQOZvC6zhrwGLZApAryB9L4nKSCqA,3058
matplotlib/testing/jpl_units/StrConverter.py,sha256=codGw9b_Zc-MG_YK4CiyMrnMR8ahR9hw836O2SsV8QI,2865
matplotlib/testing/jpl_units/UnitDbl.py,sha256=NnFvZZBff0vZhnFrwdTXIkgFF9XHeoOU6xQ_7lZkdog,5890
matplotlib/testing/jpl_units/UnitDblConverter.py,sha256=B8DssrQVyC4mwvSFP78cGL0vCnZgVhDaAbZE-jsXLUg,2828
matplotlib/testing/jpl_units/UnitDblFormatter.py,sha256=CRcbPtE3K0FlFJ4hkhi-SgQl1MUV-VlmIeOPIEPNwuI,681
matplotlib/testing/jpl_units/__init__.py,sha256=p__9RUwrt2LJ2eoT2JPM-42XLxSJrfA4az3rN5uP6d4,2684
matplotlib/testing/jpl_units/__pycache__/Duration.cpython-312.pyc,,
matplotlib/testing/jpl_units/__pycache__/Epoch.cpython-312.pyc,,
matplotlib/testing/jpl_units/__pycache__/EpochConverter.cpython-312.pyc,,
matplotlib/testing/jpl_units/__pycache__/StrConverter.cpython-312.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDbl.cpython-312.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblConverter.cpython-312.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblFormatter.cpython-312.pyc,,
matplotlib/testing/jpl_units/__pycache__/__init__.cpython-312.pyc,,
matplotlib/testing/widgets.py,sha256=wqpvu8v3YzlKjbY3ubgE7r8eLLPsPeAeXRUUgznmJq4,3469
matplotlib/tests/__init__.py,sha256=ns6SIKdszYNXD5h5PqKRCR06Z45H-sXrUX2VwujSRIM,366
matplotlib/tests/__pycache__/__init__.cpython-312.pyc,,
matplotlib/tests/__pycache__/conftest.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_afm.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_agg.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_agg_filter.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_animation.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_api.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_arrow_patches.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_artist.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_axes.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_bases.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_cairo.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_gtk3.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_macosx.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_nbagg.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_pdf.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_pgf.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_ps.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_qt.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_svg.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_template.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_tk.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_tools.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backend_webagg.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_backends_interactive.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_basic.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_bbox_tight.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_category.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_cbook.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_collections.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_colorbar.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_colors.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_compare_images.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_constrainedlayout.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_container.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_contour.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_cycles.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_dates.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_determinism.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_doc.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_dviread.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_figure.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_font_manager.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_fontconfig_pattern.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_ft2font.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_getattr.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_gridspec.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_image.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_legend.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_lines.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_marker.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_mathtext.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_matplotlib.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_mlab.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_offsetbox.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_patches.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_path.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_patheffects.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_pickle.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_png.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_polar.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_preprocess_data.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_pyplot.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_quiver.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_rcparams.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_sankey.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_scale.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_simplification.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_skew.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_sphinxext.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_spines.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_streamplot.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_style.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_subplots.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_table.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_testing.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_texmanager.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_text.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_textpath.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_ticker.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_tightlayout.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_transforms.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_triangulation.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_ttconv.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_type1font.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_units.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_usetex.cpython-312.pyc,,
matplotlib/tests/__pycache__/test_widgets.cpython-312.pyc,,
matplotlib/tests/conftest.py,sha256=HNR8xF4YiUoKY03WKoeABGwSRcKFMwI9cNFXZJDzz-A,119
matplotlib/tests/test_afm.py,sha256=A7jm2o-QaQH9SSpiFxGtZkbVU0LJZE69jfPv7RczOD4,3701
matplotlib/tests/test_agg.py,sha256=1SkR6e6ZZ8z6ENxIMJpviR3OnVciM4Yd3uwkZSnqHpQ,10742
matplotlib/tests/test_agg_filter.py,sha256=3c_Smtb4OHEOfdMFCOb2qKhzMXSbNoaUtsJ0pW47Q44,1067
matplotlib/tests/test_animation.py,sha256=NbVt4NCGGiaQ0Ub841VyohpomLnBxOIqqUyfQjKf4HA,16599
matplotlib/tests/test_api.py,sha256=7Lxp9bBs6de5zenFzoHeRUMgl3uNK3tojwT9yOttgXE,2835
matplotlib/tests/test_arrow_patches.py,sha256=mo3V1gorHQiIU4Xglw_wUeyraLjXoA-HfHpH_srxtpI,6434
matplotlib/tests/test_artist.py,sha256=8wXtMuBgzFzfCX_tJgKbdJXAyYwGAa43MsQggKIcO7Q,17441
matplotlib/tests/test_axes.py,sha256=hhjwG_7c8770hoK9--s5I109PW-bu9FQ6koPHysqLnc,284687
matplotlib/tests/test_backend_bases.py,sha256=c6WwkR9P7NTFvnrZ6NriYDscAhctTpwHYuXiSURgFmM,15743
matplotlib/tests/test_backend_cairo.py,sha256=O2LTYjsfPn__bKtTz4MGGBodpSshoPkzu0INsc18xmI,1821
matplotlib/tests/test_backend_gtk3.py,sha256=uGElP1YwTTbgvxk-gJZRFglfyC9z7ghBOBgVWILxOgs,1784
matplotlib/tests/test_backend_macosx.py,sha256=EOWBfQMJxYlo0bHmXdY-AcBZRbC23dZYghxjRJskEXM,1541
matplotlib/tests/test_backend_nbagg.py,sha256=3IfwLcdSPRupcUMWvadOWEAJG5kLywphVH5EO-ZcMx0,973
matplotlib/tests/test_backend_pdf.py,sha256=G01_KnZ_c0I0d3b9VnZjhKpjVcaJA3NcVX7dvgcp418,14469
matplotlib/tests/test_backend_pgf.py,sha256=Ra7S69IapjkdUQ8NoWLOhT4Ip7t-dd4RRyVW7_6Vx6Q,11930
matplotlib/tests/test_backend_ps.py,sha256=eK35y67rMHXaS01fTdeLPzsI1LWlZUK64x5NvcipmjM,10739
matplotlib/tests/test_backend_qt.py,sha256=fF7C4OY3FkYRXUQdUz4uEsjdd46tFGALvRhY6pLofI8,21252
matplotlib/tests/test_backend_svg.py,sha256=zLLX7xNEVQWAfxPzjki3mg_45y4DYVNxMyvY32afP4I,21062
matplotlib/tests/test_backend_template.py,sha256=uEubnnxnQdoODjRDuFb6wigOb43WEqhNWUI9Oq7_tr8,1781
matplotlib/tests/test_backend_tk.py,sha256=WZHjzNZOkCL984PBRKy3dIDuDYpXpVpHo99MLgGdqzA,8324
matplotlib/tests/test_backend_tools.py,sha256=C-B7NCkyWsQ5KzQEnI5Be16DsAHHZJU9P5v9--wsF-o,501
matplotlib/tests/test_backend_webagg.py,sha256=dsqcJWj3vg6gYsjQHR8sACUt4iPFH5aMpjULEwh0GJ8,893
matplotlib/tests/test_backends_interactive.py,sha256=krm1V4Il8_sbphnoZghDFjbMOq5RAKfPQShq2bsO2T4,23171
matplotlib/tests/test_basic.py,sha256=gJ95mHI-DcIeOB6ZsHnYjMX8oJCFE05h4VbBnTImyc0,1051
matplotlib/tests/test_bbox_tight.py,sha256=SD4xdNfLGWyV0BqhkUbAvct24At32UrEXglUDK5iAzo,5585
matplotlib/tests/test_category.py,sha256=qX_bGO31cmcj0F852I0xchTAoR6Mz9eHx7pBVnKi10w,11303
matplotlib/tests/test_cbook.py,sha256=umMZIkPh5bQk8VzAEGMyws7Shpdyx-wySE7WX_-rJTE,29950
matplotlib/tests/test_collections.py,sha256=Wp1NARnP_eAmf4VjIrW3KheGUwxzblup_KI-clWAKpI,41669
matplotlib/tests/test_colorbar.py,sha256=t3tfaSr9pxuQMDYypLcwQbmyJyjhiP9bhZuEW89bBmk,45563
matplotlib/tests/test_colors.py,sha256=1vH_oBK0UdOfQecOOJnGT6An132AQkWzx0bL5f-FasU,56032
matplotlib/tests/test_compare_images.py,sha256=NcBoT8HAxtzoR1zZBu0V3MFpGWtdFaDblQ8o3OTO6zM,3260
matplotlib/tests/test_constrainedlayout.py,sha256=W01FtR-0bSyhzd_-B6h3Uv4Kb63-KOyu87T79mkakYg,22659
matplotlib/tests/test_container.py,sha256=ijZ2QDT_GLdMwKW393K4WU6R7A5jJNaH1uhLRDwoXXI,550
matplotlib/tests/test_contour.py,sha256=--xbzrrJd4RfCO4HL0yG8UGbSP42smJAPyhrQmoHq8c,25672
matplotlib/tests/test_cycles.py,sha256=_R4kd5eVok6QimW8NTtk65oNG9mNHCOIz1AFSFBSgx4,5660
matplotlib/tests/test_dates.py,sha256=HJWLLkjHSvu5iBHSDnBgZkOe3NsomNyrZPpuTHSwqVY,57161
matplotlib/tests/test_determinism.py,sha256=T6lmhemq2ROWsQn32qDkIqZMGOfmeG1_4Ak7QT4T0AA,4464
matplotlib/tests/test_doc.py,sha256=U56V3No8ZUcVmOrKy1NeatGmLdwOLKz9gwTC5dWmtU4,972
matplotlib/tests/test_dviread.py,sha256=5agsd1oKQJvKjS8_JvtaPzZRVpOsJrch1yEiyEFJJPw,2763
matplotlib/tests/test_figure.py,sha256=Z7bGNxD684sMbwTlYl8TRNllltdhJbv8QWB1gVF7OsY,52096
matplotlib/tests/test_font_manager.py,sha256=eAs__KcWVttgROMdsn_2nhRVNAwDLFDd71T_Et0pELs,11707
matplotlib/tests/test_fontconfig_pattern.py,sha256=nkXV7NOYShEnD0eUnnt1MJrLRBKU2DCNOLjyb8cgXP8,2151
matplotlib/tests/test_ft2font.py,sha256=K1XGJTjqqqEGY7ZYJ170PA_cth9WT2qkYZTuY9jVgU8,3730
matplotlib/tests/test_getattr.py,sha256=tu6dAWCyJ7hEVlQ_J5TOvvcurhSvJEpWM2yN156K2j4,1028
matplotlib/tests/test_gridspec.py,sha256=OOCSE5R6xq3XI8Bstm0xMR1hv4guHFBCAwfCXrk_Td0,960
matplotlib/tests/test_image.py,sha256=euJ21fn2tz9Yh1AP0XqmDtTabrG6EXpOgcw70Lp9IDc,47947
matplotlib/tests/test_legend.py,sha256=wnD9Ybt0-AwhELXCsw95xTH6lPLqfhKVucBmvq0PLoE,44831
matplotlib/tests/test_lines.py,sha256=vGKsIiTA9fx1MLHMUthjWvYEm5OMmENkkBKE8drdizQ,13426
matplotlib/tests/test_marker.py,sha256=vEqEAx66_YYUADewbPo0On4gFgQOxly8V6MTDbgw9RQ,11297
matplotlib/tests/test_mathtext.py,sha256=FU9tYMaGvAewfjwH6oWebj_4e5zO_JdGzbVna0TRyz4,21990
matplotlib/tests/test_matplotlib.py,sha256=AswWqgJb_Iob7VenZX8HLRQ_1Frr6NhunsLOlAiT5HI,2628
matplotlib/tests/test_mlab.py,sha256=tYHWS_ewbB2BEm5zk-3tX3N1JgZ3KEm2pcMRG4H42LA,45122
matplotlib/tests/test_offsetbox.py,sha256=N3IrW_IMM7SidnHPZtTMK40cNlm9CdliTlhKvfE8eYM,13854
matplotlib/tests/test_patches.py,sha256=SVCYjv0KcvGOAfiq1XdNzuZCQ3DCh2KNzoHF_Grp-14,29563
matplotlib/tests/test_path.py,sha256=UWQ3t7bkk4ZatQRuBtBTBA8r9z6AveHWvoQxXwfo3uE,17814
matplotlib/tests/test_patheffects.py,sha256=GfAt8RaVEaCzmVpfJQ1lfxQmu6N69hZlerR5tnja-Qk,7587
matplotlib/tests/test_pickle.py,sha256=XTd3vWui-s8E1ojMDqnq-0Pyq98nQLwm7Sps568Bl-c,8235
matplotlib/tests/test_png.py,sha256=Bq1QLBoFH3PZCNzsBvmicWAmkLoVw1__yZse7ysYiAI,1240
matplotlib/tests/test_polar.py,sha256=OKmlBe0Lb4F3Wks9eAJEGm38U5eMeESeA0OG6uOZf84,15285
matplotlib/tests/test_preprocess_data.py,sha256=N3WEupEGxaX-ogRs7WW5DJghKW8eZGvH7H9g41UPz1w,11370
matplotlib/tests/test_pyplot.py,sha256=6DlqnmTOQYe6jg3A0yAKjLJVVe9dt3wP_AVPyEi3E0E,13034
matplotlib/tests/test_quiver.py,sha256=XSbuKXwixVChM-VqRkfNJCTQTrufswXiAKL0XigQHVI,8531
matplotlib/tests/test_rcparams.py,sha256=3z0Jy8oBzvm-ekf7VDAWUyXeJX0Q2IFWDLkRwK2HcMI,23527
matplotlib/tests/test_sankey.py,sha256=yg4i-qHT5MljFzFGViOnHbMkumM8bhPwRgoQ5M6CUEs,3900
matplotlib/tests/test_scale.py,sha256=sWcLzfSv5IOWIJbbEuOzwDVPdWDT28oW-BY45fD5YJQ,8411
matplotlib/tests/test_simplification.py,sha256=ucPf9b5yLajXiP3FklEmwW2ys3-K1q2JpsgaHB1B2Hc,18784
matplotlib/tests/test_skew.py,sha256=gs1r2YOF1BpOIBGO_CrcD-8Adufk7BKLyodU7ZUO-Vg,6267
matplotlib/tests/test_sphinxext.py,sha256=y_NH1cxqOXM4t43VSlCBuir6HdJndtMNsJitOPjA0qk,8000
matplotlib/tests/test_spines.py,sha256=QyV1ffk5x6iVqVouE-65O1n3SUyRPgLqLx7Qgnz9b10,4727
matplotlib/tests/test_streamplot.py,sha256=QJwU1t8bUtunwPN22TJcHRj--ILPq_V2iwHrvbnmzCY,5723
matplotlib/tests/test_style.py,sha256=LqcDlPikuNH7P92_PrZD6pGnA5_4cMbEv6o9Nth8GdE,6981
matplotlib/tests/test_subplots.py,sha256=cRrF-65JtRqjZEpX_3nmVerwoMbx3_FZkWdPXO1LXyc,9698
matplotlib/tests/test_table.py,sha256=qoHQxZsrJ7Z6i_GyUGK5CZ4pIW3aHKxCX9AWITIM2Jg,6549
matplotlib/tests/test_testing.py,sha256=eh-1r4PIXcM7hfSKCNTU899QxRYWhZBg6W7x0TDVUxo,1057
matplotlib/tests/test_texmanager.py,sha256=isMI3BUIoN90zCsTHsEspp4ARAwxEuyYOnna1Zc9dH8,2595
matplotlib/tests/test_text.py,sha256=OLKuDV_gDBCZmxyI6kx-Y7sb3kKCj4Q4ZGYdu18_kv4,28589
matplotlib/tests/test_textpath.py,sha256=WLXvT5OzE6Tew4pr87LH-cgioCzM7srgMNRemiMEC5o,271
matplotlib/tests/test_ticker.py,sha256=R_iSbq-wzdFWrIFTNULommoYSmNIl7PQv71Jd0RWV5Y,58526
matplotlib/tests/test_tightlayout.py,sha256=hcG-BqPGQZydJ9STyDkAcsSIuBMTtjvJG_BanPPYj_I,12704
matplotlib/tests/test_transforms.py,sha256=oO3A6Liy6gngqEkxUAO-NAKcsRGt8LDpFfxEotvXBog,29767
matplotlib/tests/test_triangulation.py,sha256=NnhZZf830EeF_7fiMrJAZogRX6kAFy74Rsf1uAbUkoM,52456
matplotlib/tests/test_ttconv.py,sha256=yW3YkGWTh-h9KXcyy70dEH4ytVuhMlzl-HMsNbK_gX8,540
matplotlib/tests/test_type1font.py,sha256=7TUXy8ehA3el-zvd4U9bnocYbelssDhUeph5cOFoaE4,6369
matplotlib/tests/test_units.py,sha256=Gihd679M0rN8JkvcBq3Azr9zk2or4dqVpJRo7aPD-fM,9306
matplotlib/tests/test_usetex.py,sha256=MhNqh5uGf2Z9wFxpPmL7UK65uU00cMpI78xxjm6GeH4,5086
matplotlib/tests/test_widgets.py,sha256=RzfPYr-fqho86nL8UZVqIqmcYLu5aIujWMiMgJezsZU,65551
matplotlib/texmanager.py,sha256=3KC-WJSQd-E00ywQcllyY36vIatKXVw9mbmXDlZSGzo,15405
matplotlib/text.py,sha256=DNRB8ryopr-WPCQSaK7aUzVaPWg0MM5eYxpcuI81AIs,70934
matplotlib/textpath.py,sha256=EXWfayoiIFRHkB5W4DyZVyYXdmdFHAmfHonoUF3XcaA,13550
matplotlib/ticker.py,sha256=EjjCo7KmZfAsc6GIzecmAaoBaZYyDl5uXbMbBmkdRHk,104653
matplotlib/tight_bbox.py,sha256=iCclrZbQwZUhmDWnPEMUJtaM9Fx319UohlEmlDmca1o,147
matplotlib/tight_layout.py,sha256=2okOqVULNDptm4w3_oBHjnItvL2D3eqzumOpnn9yLoc,448
matplotlib/transforms.py,sha256=7XcQu9O6HyGO-QuVeiIsLDL0oQSdFMv6XZid52Flpjg,99585
matplotlib/tri/__init__.py,sha256=asnfefKRpJv7sGbfddCMybnJInVDPwgph7g0mpoh2u4,820
matplotlib/tri/__pycache__/__init__.cpython-312.pyc,,
matplotlib/tri/__pycache__/_triangulation.cpython-312.pyc,,
matplotlib/tri/__pycache__/_tricontour.cpython-312.pyc,,
matplotlib/tri/__pycache__/_trifinder.cpython-312.pyc,,
matplotlib/tri/__pycache__/_triinterpolate.cpython-312.pyc,,
matplotlib/tri/__pycache__/_tripcolor.cpython-312.pyc,,
matplotlib/tri/__pycache__/_triplot.cpython-312.pyc,,
matplotlib/tri/__pycache__/_trirefine.cpython-312.pyc,,
matplotlib/tri/__pycache__/_tritools.cpython-312.pyc,,
matplotlib/tri/__pycache__/triangulation.cpython-312.pyc,,
matplotlib/tri/__pycache__/tricontour.cpython-312.pyc,,
matplotlib/tri/__pycache__/trifinder.cpython-312.pyc,,
matplotlib/tri/__pycache__/triinterpolate.cpython-312.pyc,,
matplotlib/tri/__pycache__/tripcolor.cpython-312.pyc,,
matplotlib/tri/__pycache__/triplot.cpython-312.pyc,,
matplotlib/tri/__pycache__/trirefine.cpython-312.pyc,,
matplotlib/tri/__pycache__/tritools.cpython-312.pyc,,
matplotlib/tri/_triangulation.py,sha256=T8BJEjLSrUBnEMJ5ovZHlz6-SrO6lxHZFuKgK3J0JI8,9776
matplotlib/tri/_tricontour.py,sha256=R5vUYYHwVEXwatHp3bqYSU6YctDJpihaBaI6EPbCOQ4,10318
matplotlib/tri/_trifinder.py,sha256=sp0jIE5MyMyK3No8zfxJwE2WfajP68rISG-uj5ycgGc,3457
matplotlib/tri/_triinterpolate.py,sha256=n7bu27vISqK0ycBBVx6fT5kwW11xUCnsLI_Yv43sQoE,62493
matplotlib/tri/_tripcolor.py,sha256=AWOdj9AvYziaonOzkTg2ufREI_zPv6xK6_pvvd8KUJM,6539
matplotlib/tri/_triplot.py,sha256=jlHSz36Z5S18zBKc639PlSqdhfl7jHol8ExlddJuDI4,3102
matplotlib/tri/_trirefine.py,sha256=7fhCBtFs6ITMYdQn3yNO6DhOPTbRYl89fmoFyXdchIc,13192
matplotlib/tri/_tritools.py,sha256=wC9KVE6UqkWVHpyW9FU4hQdqRVRVmJlhaBF1EXsaD8U,10575
matplotlib/tri/triangulation.py,sha256=UPkIhytAtpBUXXz2-CmHcB_oYwca91xTwu5yBPzz34o,332
matplotlib/tri/tricontour.py,sha256=FewF6-fHvOZAptUW3z1CZvNgKbmJIQ2BE_KBWBz2KO0,329
matplotlib/tri/trifinder.py,sha256=zq8aU90q5fO3peb8s58g-DFUy4qtaD2QrfR6NkpEzPI,328
matplotlib/tri/triinterpolate.py,sha256=BxDRUN6JsFh7ZJV0hvHKZrBkhztBhuseoRZ1mz-wZXA,333
matplotlib/tri/tripcolor.py,sha256=I4wz5Bt1vmB_QznwrkUewicYN7KUDig8iT3lHRppexs,328
matplotlib/tri/triplot.py,sha256=s4M-7KIJTy9ETLSTjl9KicK8IMojE40YtzLQU9eIGqg,326
matplotlib/tri/trirefine.py,sha256=T9Oe3-WPlqM-psE7lFKH1o53bXzXE2L8mnPjuJVYD-8,328
matplotlib/tri/tritools.py,sha256=f7qZzFSh6-IKWDlIMogjlFRKabwpNJK_V-W7BQmIm1g,327
matplotlib/type1font.py,sha256=Q9gGTKva3USbM9GDUqqsY3WRelgm8RWgXvqwNIRYihE,146
matplotlib/units.py,sha256=7O-llc8k3GpdotUs2tWcEGgoUHHX-Y7o0R7f-1Jve3k,6429
matplotlib/widgets.py,sha256=zXlt4rOFSiyySrflvvGYsNugUiolKejUr4YDbpNWkHw,156130
mpl_toolkits/axes_grid1/__init__.py,sha256=wiuUCQo1g20SW5T3mFOmI9dGCJY6aDmglpQw5DfszEU,371
mpl_toolkits/axes_grid1/__pycache__/__init__.cpython-312.pyc,,
mpl_toolkits/axes_grid1/__pycache__/anchored_artists.cpython-312.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_divider.cpython-312.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_grid.cpython-312.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_rgb.cpython-312.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_size.cpython-312.pyc,,
mpl_toolkits/axes_grid1/__pycache__/inset_locator.cpython-312.pyc,,
mpl_toolkits/axes_grid1/__pycache__/mpl_axes.cpython-312.pyc,,
mpl_toolkits/axes_grid1/__pycache__/parasite_axes.cpython-312.pyc,,
mpl_toolkits/axes_grid1/anchored_artists.py,sha256=xXoa97WV31zxcXsKYBAutj8OFbTcq5fzzA4rGZc1Ym0,19181
mpl_toolkits/axes_grid1/axes_divider.py,sha256=u-ljEElYLXuBA3HYeqUaGWpwq_LE1OjAXpNDybitx3Y,22729
mpl_toolkits/axes_grid1/axes_grid.py,sha256=gwV9n5n_p9BQ5Ln9MuF4GYTsooiXmSF5po8r0QOUqC4,22161
mpl_toolkits/axes_grid1/axes_rgb.py,sha256=ZemF2Mg8MCrTl9fid7okXjufXsHCMS7LFALcRW6TTiE,4718
mpl_toolkits/axes_grid1/axes_size.py,sha256=WZwxgQVa-nq9srLKfWYVyKZmcxcvKuiBKj5fDnt0UqM,8701
mpl_toolkits/axes_grid1/inset_locator.py,sha256=gGA1HQsxK8SUNdSJW2fsXh-0yHvjxoL81wNQ6U3KaWQ,21684
mpl_toolkits/axes_grid1/mpl_axes.py,sha256=vFCttnj9JIgY3Mt2eOi-O_FVvdZ6SW_sBtIBFib6bz4,4251
mpl_toolkits/axes_grid1/parasite_axes.py,sha256=olzEC-7Bbc6F41Jmpwolhb3_Bxf_258rd4i1ryEQPGc,9358
mpl_toolkits/axes_grid1/tests/__init__.py,sha256=Ox41zElZt1Po-41lx14-gMFr9R1DEK6Amt64Hn5d6sY,365
mpl_toolkits/axes_grid1/tests/__pycache__/__init__.cpython-312.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/conftest.cpython-312.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/test_axes_grid1.cpython-312.pyc,,
mpl_toolkits/axes_grid1/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/axes_grid1/tests/test_axes_grid1.py,sha256=GqHS6qwmAZ2NVONPMZex7MAnEAZXuzigEyaj4LPRP-A,28618
mpl_toolkits/axisartist/__init__.py,sha256=w0sQlZrE1LIRd3dqCp9y42111ESJONGjZSwnhBzh8dQ,553
mpl_toolkits/axisartist/__pycache__/__init__.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/angle_helper.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_divider.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_grid.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_rgb.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/axis_artist.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/axisline_style.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/axislines.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/floating_axes.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_finder.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_helper_curvelinear.cpython-312.pyc,,
mpl_toolkits/axisartist/__pycache__/parasite_axes.cpython-312.pyc,,
mpl_toolkits/axisartist/angle_helper.py,sha256=-mjKpaR1pLMJuoc0sx0_V3bv0iRPMrpS7r_WI0UYrCc,12952
mpl_toolkits/axisartist/axes_divider.py,sha256=ltdw9uabafN8MXqTcmtpA2mVFKmDqswCDzPZA6Qp2oo,135
mpl_toolkits/axisartist/axes_grid.py,sha256=ksppAL2XN59YUGbFD1ZFotCNToBPROM7TOU8KOXvTsM,248
mpl_toolkits/axisartist/axes_rgb.py,sha256=RmtYWWk9G770cD5_ovVvgRSOX_GbRqfmED_UudRryGs,295
mpl_toolkits/axisartist/axis_artist.py,sha256=TlvqI-03ZtkxL5kDXknzue3AUsLceg62D1oKtDJpmzM,38157
mpl_toolkits/axisartist/axisline_style.py,sha256=weljspcXdP0y4ccjXp4NxX1A20J6clKfNS4VathUnyU,6712
mpl_toolkits/axisartist/axislines.py,sha256=HOKtOL38Pdzxsn25-G6-B30vG5UJr4yvWy2RzacBCt8,19751
mpl_toolkits/axisartist/floating_axes.py,sha256=ufHFB6LdIV_JAhols2I9yExw7Z9ExJQKU4CV7UM1FFE,10784
mpl_toolkits/axisartist/grid_finder.py,sha256=XLJnrBEWunlIECzov5qnIE7ib8fNXJAa9p3xirh9Hbw,12273
mpl_toolkits/axisartist/grid_helper_curvelinear.py,sha256=ymI19qTMOgdBcjRMS3GNrAu0MGrE9Jb9KorCqwhyXJU,12726
mpl_toolkits/axisartist/parasite_axes.py,sha256=Ydi4-0Lbczr6K7Sz1-fRwK4Tm8KlHrOIumx67Xbo_9c,244
mpl_toolkits/axisartist/tests/__init__.py,sha256=Ox41zElZt1Po-41lx14-gMFr9R1DEK6Amt64Hn5d6sY,365
mpl_toolkits/axisartist/tests/__pycache__/__init__.cpython-312.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/conftest.cpython-312.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_angle_helper.cpython-312.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axis_artist.cpython-312.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axislines.cpython-312.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_floating_axes.cpython-312.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_finder.cpython-312.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_helper_curvelinear.cpython-312.pyc,,
mpl_toolkits/axisartist/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/axisartist/tests/test_angle_helper.py,sha256=PwhJwBm2kk4uMyhdO5arQs8IlqSX2vN0hvUzI7YHqrw,5670
mpl_toolkits/axisartist/tests/test_axis_artist.py,sha256=uuwuc-3ajXvG8lBZN8JNO5fO2w5sX2Ynbux9VHpO8kc,2982
mpl_toolkits/axisartist/tests/test_axislines.py,sha256=r3CciE-PogcVtm1bR3HDHEg15iTZAHau2C0JrnWEVUI,4466
mpl_toolkits/axisartist/tests/test_floating_axes.py,sha256=MOlxCmU11s6W3fI2aCoLx2csTbLr9Hig5r4wzO25Vbo,3948
mpl_toolkits/axisartist/tests/test_grid_finder.py,sha256=cwQLDOdcJbAY2E7dr8595yzuNh1_Yh80r_O8WGT2hMY,1156
mpl_toolkits/axisartist/tests/test_grid_helper_curvelinear.py,sha256=F3tcHrFJS7EIlWEMeiPk6KFLn46IB5LpddnNSNkPtZQ,7270
mpl_toolkits/mplot3d/__init__.py,sha256=fH9HdMfFMvjbIWqy2gjQnm2m3ae1CvLiuH6LwKHo0kI,49
mpl_toolkits/mplot3d/__pycache__/__init__.cpython-312.pyc,,
mpl_toolkits/mplot3d/__pycache__/art3d.cpython-312.pyc,,
mpl_toolkits/mplot3d/__pycache__/axes3d.cpython-312.pyc,,
mpl_toolkits/mplot3d/__pycache__/axis3d.cpython-312.pyc,,
mpl_toolkits/mplot3d/__pycache__/proj3d.cpython-312.pyc,,
mpl_toolkits/mplot3d/art3d.py,sha256=AIFmIxwvkret13XzZs3Q799FxZpJYOyhVIxjs4A5r4A,41520
mpl_toolkits/mplot3d/axes3d.py,sha256=dj_oPq9jpKwInsfdNIPEIPgkL_nemnuM7oyG2QxihfI,125831
mpl_toolkits/mplot3d/axis3d.py,sha256=j7ZboQbaCexgvzGy0yOZ95jJ9tP1CobisCJhsNhMo8E,23680
mpl_toolkits/mplot3d/proj3d.py,sha256=cXiP_dp51U4YbEPt1LW-8rHXoaJ-MhAYF7bmdQtLTl0,6932
mpl_toolkits/mplot3d/tests/__init__.py,sha256=Ox41zElZt1Po-41lx14-gMFr9R1DEK6Amt64Hn5d6sY,365
mpl_toolkits/mplot3d/tests/__pycache__/__init__.cpython-312.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/conftest.cpython-312.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_art3d.cpython-312.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_axes3d.cpython-312.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_legend3d.cpython-312.pyc,,
mpl_toolkits/mplot3d/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/mplot3d/tests/test_art3d.py,sha256=0zhJvLAqg6RwB3fq77fUc30f1h5Wjw3nh1P0cOFM4R0,1214
mpl_toolkits/mplot3d/tests/test_axes3d.py,sha256=5daT9vrSthLqXdHjlYmoO2oyFtdjgpo54Q0h8ca34Mw,72550
mpl_toolkits/mplot3d/tests/test_legend3d.py,sha256=YFJtEkNJKdLQu5BBJIG03ufvPLBrtUPwwZCZvnMQeAw,4194
pylab.py,sha256=u_By3CHla-rBMg57egFXIxZ3P_J6zEkSu_dNpBcH5pw,90
