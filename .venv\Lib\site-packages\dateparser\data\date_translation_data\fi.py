info = {
    "name": "fi",
    "date_order": "DMY",
    "january": [
        "tammi",
        "tammik",
        "tammikuu",
        "tammikuuta",
        "tammikuussa"
    ],
    "february": [
        "helmi",
        "helmik",
        "helmikuu",
        "helmikuuta",
        "helmikuussa"
    ],
    "march": [
        "maalis",
        "maalisk",
        "maaliskuu",
        "maaliskuuta",
        "maaliskuussa"
    ],
    "april": [
        "huhti",
        "huhtik",
        "huhtikuu",
        "huhtikuuta",
        "huhtikuussa"
    ],
    "may": [
        "touko",
        "toukok",
        "toukokuu",
        "toukokuuta",
        "toukokuussa"
    ],
    "june": [
        "kesä",
        "kesäk",
        "kesäkuu",
        "kesäkuuta",
        "kesäkuussa"
    ],
    "july": [
        "hein<PERSON>",
        "heinäk",
        "hein<PERSON>ku<PERSON>",
        "hein<PERSON><PERSON><PERSON>",
        "hein<PERSON><PERSON><PERSON><PERSON>"
    ],
    "august": [
        "elo",
        "elok",
        "elokuu",
        "elokuuta",
        "elokuussa"
    ],
    "september": [
        "syys",
        "syysk",
        "syyskuu",
        "syyskuuta",
        "Syyskuussa"
    ],
    "october": [
        "loka",
        "lokak",
        "lokakuu",
        "lokakuuta",
        "Lokakuussa"
    ],
    "november": [
        "marras",
        "marrask",
        "marraskuu",
        "marraskuuta",
        "Marraskuussa"
    ],
    "december": [
        "joulu",
        "jouluk",
        "joulukuu",
        "joulukuuta",
        "Joulukuussa"
    ],
    "monday": [
        "ma",
        "maanantai",
        "maanantaina"
    ],
    "tuesday": [
        "ti",
        "tiistai",
        "tiistaina"
    ],
    "wednesday": [
        "ke",
        "keskiviikko",
        "keskiviikkona"
    ],
    "thursday": [
        "to",
        "torstai",
        "torstaina"
    ],
    "friday": [
        "pe",
        "perjantai",
        "perjantaina"
    ],
    "saturday": [
        "la",
        "lauantai",
        "lauantaina"
    ],
    "sunday": [
        "su",
        "sunnuntai",
        "sunnuntaina"
    ],
    "am": [
        "ap"
    ],
    "pm": [
        "ip"
    ],
    "year": [
        "v",
        "vuosi",
        "vuotta",
        "vv",
        "vuonna",
        "vuoden"
    ],
    "month": [
        "kk",
        "kuukausi",
        "kuukautta",
        "kuukauden"
    ],
    "week": [
        "viikko",
        "vk",
        "viikkoa",
        "vko",
        "viikon"
    ],
    "day": [
        "pv",
        "päivä",
        "päivää",
        "p",
        "pvä",
        "pvää",
        "päivän"
    ],
    "hour": [
        "t",
        "tunti",
        "tuntia",
        "tunnin"
    ],
    "minute": [
        "min",
        "minuutti",
        "minuuttia",
        "minuutin"
    ],
    "second": [
        "s",
        "sekunti",
        "sekuntia",
        "sekuntti",
        "sekunttia",
        "sekuntin",
        "sekunnin"
    ],
    "relative-type": {
        "0 day ago": [
            "tänään"
        ],
        "0 hour ago": [
            "tunnin sisällä",
            "tämän tunnin aikana"
        ],
        "0 minute ago": [
            "minuutin sisällä",
            "tämän minuutin aikana"
        ],
        "0 month ago": [
            "tässä kk",
            "tässä kuussa"
        ],
        "0 second ago": [
            "nyt"
        ],
        "0 week ago": [
            "tällä viikolla",
            "tällä vk"
        ],
        "0 year ago": [
            "tänä v",
            "tänä vuonna"
        ],
        "1 day ago": [
            "eilen"
        ],
        "1 month ago": [
            "viime kk",
            "viime kuussa"
        ],
        "1 week ago": [
            "viime viikolla",
            "viime vk"
        ],
        "1 year ago": [
            "viime v",
            "viime vuonna"
        ],
        "in 1 day": [
            "huom",
            "huomenna"
        ],
        "in 1 month": [
            "ensi kk",
            "ensi kuussa"
        ],
        "in 1 week": [
            "ensi viikolla",
            "ensi vk"
        ],
        "in 1 year": [
            "ensi v",
            "ensi vuonna"
        ],
        "2 year ago": [
            "toissa vuonna"
        ],
        "2 month ago": [
            "toissa kuussa"
        ],
        "2 week ago": [
            "toissa viikolla"
        ],
        "2 day ago": [
            "toissa päivänä"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) pv sitten",
            "(\\d+[.,]?\\d*) päivä sitten",
            "(\\d+[.,]?\\d*) päivää sitten"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) t sitten",
            "(\\d+[.,]?\\d*) tunti sitten",
            "(\\d+[.,]?\\d*) tuntia sitten"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) min sitten",
            "(\\d+[.,]?\\d*) minuutti sitten",
            "(\\d+[.,]?\\d*) minuuttia sitten"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) kk sitten",
            "(\\d+[.,]?\\d*) kuukausi sitten",
            "(\\d+[.,]?\\d*) kuukautta sitten"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) s sitten",
            "(\\d+[.,]?\\d*) sekunti sitten",
            "(\\d+[.,]?\\d*) sekuntia sitten"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) viikko sitten",
            "(\\d+[.,]?\\d*) viikkoa sitten",
            "(\\d+[.,]?\\d*) vk sitten"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) v sitten",
            "(\\d+[.,]?\\d*) vuosi sitten",
            "(\\d+[.,]?\\d*) vuotta sitten"
        ],
        "in \\1 day": [
            "(\\d+[.,]?\\d*) pv päästä",
            "(\\d+[.,]?\\d*) päivän päästä"
        ],
        "in \\1 hour": [
            "(\\d+[.,]?\\d*) t päästä",
            "(\\d+[.,]?\\d*) tunnin päästä"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) min päästä",
            "(\\d+[.,]?\\d*) minuutin päästä"
        ],
        "in \\1 month": [
            "(\\d+[.,]?\\d*) kk päästä",
            "(\\d+[.,]?\\d*) kuukauden päästä"
        ],
        "in \\1 second": [
            "(\\d+[.,]?\\d*) s päästä",
            "(\\d+[.,]?\\d*) sekunnin päästä"
        ],
        "in \\1 week": [
            "(\\d+[.,]?\\d*) viikon päästä",
            "(\\d+[.,]?\\d*) vk päästä"
        ],
        "in \\1 year": [
            "(\\d+[.,]?\\d*) v päästä",
            "(\\d+[.,]?\\d*) vuoden päästä"
        ]
    },
    "locale_specific": {},
    "skip": [
        ":n",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "sitten"
    ],
    "in": [
        "kuluttua",
        "päästä"
    ],
    "simplifications": [
        {
            "(\\d+[.,]?\\d*) (sekunnin|sekuntin|minuutin|tunnin|päivän|viikon|kuukauden|vuoden) (päästä|kuluttua)": "\\3 \\1 \\2"
        }
    ]
}
