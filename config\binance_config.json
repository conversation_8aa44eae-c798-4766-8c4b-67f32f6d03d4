{"binance_accounts": {"live_trading": {"account_id": "binance_live_main", "api_key": "BINANCE_API_KEY", "secret_key": "BINANCE_SECRET", "base_url": "https://api.binance.com", "testnet": false, "permissions": {"spot_trading": true, "margin_trading": true, "futures_trading": false, "withdrawal": false, "deposit": false}, "rate_limits": {"requests_per_minute": 1200, "orders_per_second": 10, "orders_per_day": 200000}, "active": true, "priority": 1}, "testnet": {"account_id": "binance_testnet", "api_key": "BINANCE_TESTNET_API_KEY", "secret_key": "BINANCE_TESTNET_SECRET", "base_url": "https://testnet.binance.vision", "testnet": true, "permissions": {"spot_trading": true, "margin_trading": true, "futures_trading": true, "withdrawal": false, "deposit": false}, "rate_limits": {"requests_per_minute": 1200, "orders_per_second": 10, "orders_per_day": 200000}, "active": true, "priority": 2}}, "trading_settings": {"default_account": "live_trading", "fallback_account": "testnet", "auto_fallback": true, "connection_timeout": 10, "read_timeout": 30, "retry_attempts": 3, "retry_delay": 1}, "security_settings": {"encrypt_keys": true, "timestamp_sync": true, "ntp_servers": ["pool.ntp.org", "time.google.com", "time.cloudflare.com"], "max_time_drift": 5000, "signature_validation": true, "ip_whitelist_enabled": false}, "risk_management": {"max_position_size": 0.05, "max_daily_trades": 50, "max_daily_loss": 0.1, "emergency_stop_loss": 0.15, "position_size_limits": {"BTCUSDT": 0.1, "ETHUSDT": 1.0, "ADAUSDT": 1000, "default": 0.01}}, "monitoring": {"health_check_interval": 60, "balance_check_interval": 300, "position_check_interval": 30, "error_notification": true, "performance_logging": true}}