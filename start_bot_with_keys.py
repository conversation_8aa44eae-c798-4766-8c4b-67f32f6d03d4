#!/usr/bin/env python3
"""
SP.Bot Enhanced v2.0.0 - Startup Script with API Key Configuration

This script initializes the bot with all provided API keys and starts trading.
"""

import os
import sys
import logging
import json
from pathlib import Path
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/startup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_environment():
    """Load environment variables from .env file"""
    try:
        load_dotenv()
        logger.info("✅ Environment variables loaded successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to load environment variables: {e}")
        return False

def verify_api_keys():
    """Verify all API keys are properly configured"""
    logger.info("🔍 Verifying API key configuration...")
    
    # Check Binance keys
    binance_live_key = os.getenv('BINANCE_API_KEY')
    binance_live_secret = os.getenv('BINANCE_SECRET')
    binance_test_key = os.getenv('BINANCE_TESTNET_API_KEY')
    binance_test_secret = os.getenv('BINANCE_TESTNET_SECRET')
    
    if binance_live_key and binance_live_secret:
        logger.info("✅ Binance LIVE API keys configured")
    else:
        logger.warning("⚠️ Binance LIVE API keys missing")
    
    if binance_test_key and binance_test_secret:
        logger.info("✅ Binance TESTNET API keys configured")
    else:
        logger.warning("⚠️ Binance TESTNET API keys missing")
    
    # Check OpenAI keys
    openai_keys = []
    for i in range(1, 4):
        key = os.getenv(f'OPENAI_API_KEY_{i}')
        if key:
            openai_keys.append(key)
    
    logger.info(f"✅ OpenAI API keys configured: {len(openai_keys)}/3")
    
    # Check DeepSeek keys
    deepseek_keys = []
    for i in range(1, 4):
        key = os.getenv(f'DEEPSEEK_API_KEY_{i}')
        if key:
            deepseek_keys.append(key)
    
    logger.info(f"✅ DeepSeek API keys configured: {len(deepseek_keys)}/3")
    
    # Check Qwen keys
    qwen_keys = []
    for i in range(1, 4):
        key = os.getenv(f'QWEN_API_KEY_{i}')
        if key:
            qwen_keys.append(key)
    
    logger.info(f"✅ Qwen AI API keys configured: {len(qwen_keys)}/3")
    
    total_ai_keys = len(openai_keys) + len(deepseek_keys) + len(qwen_keys)
    logger.info(f"📊 Total AI API keys available: {total_ai_keys}/9")
    
    return total_ai_keys >= 6  # At least 2 keys per service

def initialize_security():
    """Initialize security systems"""
    logger.info("🔐 Initializing security systems...")
    
    try:
        # Import security modules
        from security.key_manager import KeyManager
        from security.memory_guard import MemoryGuard
        
        # Initialize key manager
        key_manager = KeyManager()
        logger.info("✅ Key manager initialized")
        
        # Initialize memory guard
        memory_guard = MemoryGuard()
        memory_guard.start_monitoring()
        logger.info("✅ Memory guard started")
        
        return key_manager, memory_guard
        
    except ImportError as e:
        logger.error(f"❌ Failed to import security modules: {e}")
        return None, None
    except Exception as e:
        logger.error(f"❌ Failed to initialize security: {e}")
        return None, None

def test_api_connections():
    """Test API connections before starting trading"""
    logger.info("🧪 Testing API connections...")
    
    try:
        # Test AI connections
        from ai_services.ai_connector import MultiAIConnector
        
        ai_connector = MultiAIConnector()
        
        # Test market data
        test_data = {
            'symbol': 'BTCUSDT',
            'price': 50000,
            'volume': 1000000,
            'atr': 0.02,
            'rsi': 65,
            'market_regime': 'stable'
        }
        
        logger.info("Testing AI services...")
        signals = ai_connector.analyze_market(test_data)
        
        if signals:
            logger.info("✅ AI services responding correctly")
            return True
        else:
            logger.warning("⚠️ AI services not responding as expected")
            return False
            
    except Exception as e:
        logger.error(f"❌ API connection test failed: {e}")
        return False

def start_trading_bot(mode="paper"):
    """Start the trading bot"""
    logger.info(f"🚀 Starting SP.Bot Enhanced v2.0.0 in {mode.upper()} mode...")
    
    try:
        if mode == "paper":
            # Start paper trading
            from simulation.paper_trading import PaperTradingBot
            bot = PaperTradingBot()
            logger.info("✅ Paper trading bot initialized")
        else:
            # Start live trading
            from enhanced_main import EnhancedTradingBot
            bot = EnhancedTradingBot()
            logger.info("✅ Live trading bot initialized")
        
        # Start the bot
        bot.start()
        logger.info("🎯 Trading bot started successfully!")
        
        return bot
        
    except Exception as e:
        logger.error(f"❌ Failed to start trading bot: {e}")
        return None

def main():
    """Main startup function"""
    logger.info("=" * 70)
    logger.info("🤖 SP.Bot Enhanced v2.0.0 - STARTUP WITH API KEYS")
    logger.info("=" * 70)
    
    # Step 1: Load environment
    if not load_environment():
        logger.error("❌ Failed to load environment. Exiting.")
        sys.exit(1)
    
    # Step 2: Verify API keys
    if not verify_api_keys():
        logger.error("❌ Insufficient API keys configured. Need at least 6 AI keys.")
        sys.exit(1)
    
    # Step 3: Initialize security
    key_manager, memory_guard = initialize_security()
    if not key_manager:
        logger.error("❌ Failed to initialize security. Exiting.")
        sys.exit(1)
    
    # Step 4: Test API connections
    if not test_api_connections():
        logger.warning("⚠️ API connection tests failed. Proceeding with caution.")
    
    # Step 5: Determine trading mode
    trading_mode = os.getenv('TRADING_MODE', 'paper').lower()
    logger.info(f"📊 Trading mode: {trading_mode.upper()}")
    
    # Step 6: Start trading bot
    bot = start_trading_bot(trading_mode)
    
    if bot:
        logger.info("🎉 SP.Bot Enhanced v2.0.0 started successfully!")
        logger.info("📈 Ready for profitable trading!")
        
        # Keep the bot running
        try:
            bot.run()
        except KeyboardInterrupt:
            logger.info("🛑 Bot stopped by user")
        except Exception as e:
            logger.error(f"❌ Bot crashed: {e}")
        finally:
            if memory_guard:
                memory_guard.stop_monitoring()
            logger.info("🔒 Security cleanup completed")
    else:
        logger.error("❌ Failed to start bot. Exiting.")
        sys.exit(1)

if __name__ == "__main__":
    main()
