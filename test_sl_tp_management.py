import os
import sys
import time
import json
import logging
import datetime
import random
import numpy as np

# Import the SimpleAITradingBot class
from simple_ai_bot import SimpleAITradingBot

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("test_sl_tp_management")

def test_sl_tp_management():
    """
    Test the Stop Loss and Take Profit Management feature
    """
    # Create a bot instance
    bot = SimpleAITradingBot(symbol="BTC/USDT", test_mode=True)

    # Create a position
    entry_price = 60000.0

    # Calculate stop loss (0.5% below recent low)
    recent_low = 58000.0
    stop_loss_price = recent_low * 0.995  # 0.5% below recent low

    # Calculate risk (distance from entry to stop loss)
    risk_distance = entry_price - stop_loss_price

    # Calculate take profit targets
    tp1_price = entry_price + risk_distance * 1.0  # 1R
    tp2_price = entry_price + risk_distance * 2.0  # 2R
    tp3_price = entry_price + risk_distance * 3.0  # 3R

    # Create a position
    position = {
        "symbol": "BTC/USDT",
        "side": "buy",
        "entry_price": entry_price,
        "quantity": 0.1,
        "timestamp": datetime.datetime.now().isoformat(),
        "order_id": f"test_{random.randint(1000, 9999)}",
        "signal": {"recommendation": "buy", "confidence": 0.85},
        "initial_stop_loss_price": stop_loss_price,
        "stop_loss_price": stop_loss_price,
        "stop_loss_pct": (entry_price - stop_loss_price) / entry_price,
        "risk_value": (entry_price - stop_loss_price) * 0.1,
        "tp1_price": tp1_price,
        "tp2_price": tp2_price,
        "tp3_price": tp3_price,
        "breakeven_price": entry_price,
        "trailing_stop_price": stop_loss_price,
        "trailing_stop_activated": False,
        "reached_tp1": False,
        "reached_tp2": False,
        "reached_breakeven": False
    }

    # Set current position
    bot.current_position = position

    # Add to open positions list
    bot.open_positions.append(position)

    # Add symbol to the set of position symbols
    bot.position_symbols.add("BTC/USDT")

    logger.info(f"Created position: {position['side']} {position['quantity']} {position['symbol']} at ${position['entry_price']:.2f}")
    logger.info(f"Stop loss: ${position['stop_loss_price']:.2f} (0.5% below recent low of ${recent_low:.2f})")
    logger.info(f"Take profit targets: TP1: ${position['tp1_price']:.2f} (1R), TP2: ${position['tp2_price']:.2f} (2R), TP3: ${position['tp3_price']:.2f} (3R)")

    # Test scenarios
    test_scenarios = [
        # Scenario 1: Price moves up but doesn't reach TP1
        {"price": 61000.0, "description": "Price moves up but doesn't reach TP1"},

        # Scenario 2: Price reaches TP1, stop loss should move to breakeven
        {"price": tp1_price + 100, "description": "Price reaches TP1, stop loss should move to breakeven"},

        # Scenario 3: Price moves up further, trailing stop should be activated
        {"price": tp1_price + 500, "description": "Price moves up further, trailing stop should be activated"},

        # Scenario 4: Price moves up even further, trailing stop should be updated
        {"price": tp1_price + 1000, "description": "Price moves up even further, trailing stop should be updated"},

        # Scenario 5: Price reaches TP2
        {"price": tp2_price + 100, "description": "Price reaches TP2"},

        # Scenario 6: Price reaches TP3
        {"price": tp3_price + 100, "description": "Price reaches TP3"},

        # Scenario 7: Price drops to trailing stop, position should be closed
        {"price": bot.current_position["trailing_stop_price"] - 100, "description": "Price drops to trailing stop, position should be closed"}
    ]

    # Run test scenarios
    for i, scenario in enumerate(test_scenarios):
        logger.info(f"\nScenario {i+1}: {scenario['description']}")

        # Override the check_price method to return the scenario price
        bot.check_price = lambda: scenario["price"]

        # Update the last high/low price for trailing stop calculation
        if scenario["price"] > bot.last_high_price:
            bot.last_high_price = scenario["price"]

        # Manage positions
        bot.manage_positions(scenario["price"])

        # Wait a bit
        time.sleep(1)

    logger.info("\nTest completed!")

if __name__ == "__main__":
    test_sl_tp_management()
