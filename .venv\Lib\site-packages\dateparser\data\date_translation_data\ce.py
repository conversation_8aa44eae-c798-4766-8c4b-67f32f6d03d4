info = {
    "name": "ce",
    "date_order": "<PERSON><PERSON>",
    "january": [
        "янв",
        "январь"
    ],
    "february": [
        "фев",
        "февраль"
    ],
    "march": [
        "мар",
        "март"
    ],
    "april": [
        "апр",
        "апрель"
    ],
    "may": [
        "май"
    ],
    "june": [
        "июн",
        "июнь"
    ],
    "july": [
        "июл",
        "июль"
    ],
    "august": [
        "авг",
        "август"
    ],
    "september": [
        "сен",
        "сентябрь"
    ],
    "october": [
        "окт",
        "октябрь"
    ],
    "november": [
        "ноя",
        "ноябрь"
    ],
    "december": [
        "дек",
        "декабрь"
    ],
    "monday": [
        "оршотан де"
    ],
    "tuesday": [
        "шинарин де"
    ],
    "wednesday": [
        "кхаарин де"
    ],
    "thursday": [
        "еарин де"
    ],
    "friday": [
        "пӏераскан де"
    ],
    "saturday": [
        "шот де"
    ],
    "sunday": [
        "кӏиранан де"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "ш",
        "шо"
    ],
    "month": [
        "бут",
        "бутт"
    ],
    "week": [
        "кӏир",
        "кӏира"
    ],
    "day": [
        "де"
    ],
    "hour": [
        "сахь",
        "сахьт"
    ],
    "minute": [
        "мин",
        "минот"
    ],
    "second": [
        "сек",
        "секунд"
    ],
    "relative-type": {
        "0 day ago": [
            "тахана"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "карарчу баттахь"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "карарчу кӏирнахь"
        ],
        "0 year ago": [
            "карарчу шарахь"
        ],
        "1 day ago": [
            "селхана"
        ],
        "1 month ago": [
            "баханчу баттахь"
        ],
        "1 week ago": [
            "даханчу кӏирнахь"
        ],
        "1 year ago": [
            "даханчу шарахь"
        ],
        "in 1 day": [
            "кхана"
        ],
        "in 1 month": [
            "рогӏерчу баттахь"
        ],
        "in 1 week": [
            "рогӏерчу кӏирнахь"
        ],
        "in 1 year": [
            "рогӏерчу шарахь"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) д хьалха",
            "(\\d+[.,]?\\d*) де хьалха"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) сахь хьалха",
            "(\\d+[.,]?\\d*) сахьт хьалха"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) мин хьалха",
            "(\\d+[.,]?\\d*) минот хьалха"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) б хьалха",
            "(\\d+[.,]?\\d*) бутт хьалха"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) сек хьалха",
            "(\\d+[.,]?\\d*) секунд хьалха"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) кӏир хьалха",
            "(\\d+[.,]?\\d*) кӏира хьалха"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) ш хьалха",
            "(\\d+[.,]?\\d*) шо хьалха"
        ],
        "in \\1 day": [
            "(\\d+[.,]?\\d*) д даьлча",
            "(\\d+[.,]?\\d*) де даьлча"
        ],
        "in \\1 hour": [
            "(\\d+[.,]?\\d*) сахь даьлча",
            "(\\d+[.,]?\\d*) сахьт даьлча"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) мин яьлча",
            "(\\d+[.,]?\\d*) минот яьлча"
        ],
        "in \\1 month": [
            "(\\d+[.,]?\\d*) б баьлча",
            "(\\d+[.,]?\\d*) бутт баьлча"
        ],
        "in \\1 second": [
            "(\\d+[.,]?\\d*) сек яьлча",
            "(\\d+[.,]?\\d*) секунд яьлча"
        ],
        "in \\1 week": [
            "(\\d+[.,]?\\d*) кӏир даьлча",
            "(\\d+[.,]?\\d*) кӏира даьлча"
        ],
        "in \\1 year": [
            "(\\d+[.,]?\\d*) ш даьлча",
            "(\\d+[.,]?\\d*) шо даьлча"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
