"""
Signal Aggregator Module
Combines signals from multiple sources and generates a final trading signal
"""

import os
import json
import logging
import datetime
from pathlib import Path
from analysis.ai_connector import OpenAIConnector, DeepSeekConnector, QwenConnector
from analysis.news_connector import GoogleNewsConnector, GoogleTrendsConnector

# Set up logging
logger = logging.getLogger("signal_aggregator")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/analysis.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class SignalAggregator:
    """
    Aggregates signals from multiple sources and generates a final trading signal
    """

    def __init__(self, key_manager=None):
        """
        Initialize the signal aggregator

        Args:
            key_manager (KeyManager, optional): Key manager instance for API keys
        """
        # Initialize connectors
        self.openai_connector = OpenAIConnector(key_manager)
        self.deepseek_connector = DeepSeekConnector(key_manager)
        self.qwen_connector = QwenConnector(key_manager)
        self.news_connector = GoogleNewsConnector(key_manager)
        self.trends_connector = GoogleTrendsConnector()

        # Initialize model evaluator
        from ai_services.model_evaluator import AIModelEvaluator
        self.model_evaluator = AIModelEvaluator(base_weights={
            "openai": 0.30,
            "deepseek": 0.35,
            "qwen": 0.35
        })

        # Get weights from model evaluator for AI models
        ai_weights = self.model_evaluator.get_current_weights()

        # Configure weights for different sources
        self.weights = {
            "openai": ai_weights.get("openai", 0.30),
            "deepseek": ai_weights.get("deepseek", 0.35),
            "qwen": ai_weights.get("qwen", 0.35),
            "news": 0.15,
            "trends": 0.15
        }

        # Configure confidence thresholds - LOWERED for more trading opportunities
        self.confidence_threshold = 0.55  # 55% confidence required for a signal
        self.min_sources = 2  # At least 2 sources must agree
        self.volume_confluence_weight = 0.15  # Weight for volume confirmation
        self.market_regime_weight = 0.10  # Weight for market regime consideration

    def analyze_market(self, market_data):
        """
        Analyze market data using multiple sources and generate a final signal

        Args:
            market_data (dict): Market data to analyze

        Returns:
            dict: Aggregated analysis and final signal
        """
        logger.info("Starting market analysis with multiple sources")

        # Collect signals from all sources
        signals = {}

        # Get OpenAI analysis
        openai_result = self.openai_connector.analyze_market(market_data)
        if "error" not in openai_result:
            signals["openai"] = openai_result
            logger.info(f"OpenAI analysis: {openai_result['analysis']['recommendation']} with {openai_result['analysis']['confidence']}% confidence")
        else:
            logger.warning(f"OpenAI analysis failed: {openai_result.get('error')}")

        # Get DeepSeek analysis
        deepseek_result = self.deepseek_connector.analyze_market(market_data)
        if "error" not in deepseek_result:
            signals["deepseek"] = deepseek_result
            logger.info(f"DeepSeek analysis: {deepseek_result['analysis']['recommendation']} with {deepseek_result['analysis']['confidence']}% confidence")
        else:
            logger.warning(f"DeepSeek analysis failed: {deepseek_result.get('error')}")

        # Get Qwen analysis
        qwen_result = self.qwen_connector.analyze_market(market_data)
        if "error" not in qwen_result:
            signals["qwen"] = qwen_result
            logger.info(f"Qwen analysis: {qwen_result['analysis']['recommendation']} with {qwen_result['analysis']['confidence']}% confidence")
        else:
            logger.warning(f"Qwen analysis failed: {qwen_result.get('error')}")

        # Get news analysis
        news_result = self.news_connector.get_bitcoin_news()
        if "error" not in news_result:
            signals["news"] = news_result
            logger.info(f"News analysis: {news_result['news']['overall_sentiment']}")
        else:
            logger.warning(f"News analysis failed: {news_result.get('error')}")

        # Get trends analysis
        trends_result = self.trends_connector.get_bitcoin_interest()
        if "error" not in trends_result:
            signals["trends"] = trends_result
            logger.info(f"Trends analysis: {trends_result['trends']['trend_direction']}")
        else:
            logger.warning(f"Trends analysis failed: {trends_result.get('error')}")

        # Aggregate signals
        aggregated_signal = self._aggregate_signals(signals)

        # Save analysis to file
        self._save_analysis(signals, aggregated_signal)

        return aggregated_signal

    def _aggregate_signals(self, signals):
        """
        Aggregate signals from multiple sources

        Args:
            signals (dict): Signals from multiple sources

        Returns:
            dict: Aggregated signal
        """
        # Extract market data from signals
        market_data = {}
        for source in signals:
            if "market_data" in signals[source]:
                market_data = signals[source]["market_data"]
                break
        # Check if AI models disagree
        ai_recommendations = set()
        for source in ["openai", "deepseek", "qwen"]:
            if source in signals and "analysis" in signals[source]:
                recommendation = signals[source]["analysis"].get("recommendation", "hold").lower()
                ai_recommendations.add(recommendation)

        # If AI models disagree, use debate mode weights
        if len(ai_recommendations) > 1:
            logger.info("AI models disagree. Using debate mode weights.")
            debate_weights = self.model_evaluator.get_debate_weights(signals)

            # Update AI weights
            for source in ["openai", "deepseek", "qwen"]:
                if source in debate_weights:
                    self.weights[source] = debate_weights[source]

            logger.info(f"Updated AI weights for debate mode: {self.weights}")
        else:
            # Use current weights from model evaluator for AI models
            ai_weights = self.model_evaluator.get_current_weights()
            for source in ["openai", "deepseek", "qwen"]:
                if source in ai_weights:
                    self.weights[source] = ai_weights[source]

        # Count recommendations
        recommendations = {"buy": 0, "sell": 0, "hold": 0}
        confidence_sum = 0
        source_count = 0

        # Process AI signals
        for source in ["openai", "deepseek", "qwen"]:
            if source in signals and "analysis" in signals[source]:
                analysis = signals[source]["analysis"]
                recommendation = analysis.get("recommendation", "hold").lower()
                confidence = analysis.get("confidence", 50) / 100.0  # Convert to 0-1 scale

                # Add weighted recommendation
                recommendations[recommendation] += self.weights[source] * confidence
                confidence_sum += self.weights[source] * confidence
                source_count += 1

                # Record prediction for evaluation
                self.model_evaluator.record_prediction(
                    source,
                    recommendation,
                    confidence
                )

        # Process news signal
        if "news" in signals and "news" in signals["news"]:
            news = signals["news"]["news"]
            sentiment = news.get("overall_sentiment", "neutral")

            # Convert sentiment to recommendation
            if sentiment == "bullish":
                recommendation = "buy"
            elif sentiment == "bearish":
                recommendation = "sell"
            else:
                recommendation = "hold"

            # Add weighted recommendation
            recommendations[recommendation] += self.weights["news"]
            confidence_sum += self.weights["news"]
            source_count += 1

        # Process trends signal
        if "trends" in signals and "trends" in signals["trends"]:
            trends = signals["trends"]["trends"]
            trend_direction = trends.get("trend_direction", "neutral")

            # Convert trend direction to recommendation
            if trend_direction == "bullish":
                recommendation = "buy"
            elif trend_direction == "bearish":
                recommendation = "sell"
            else:
                recommendation = "hold"

            # Add weighted recommendation
            recommendations[recommendation] += self.weights["trends"]
            confidence_sum += self.weights["trends"]
            source_count += 1

        # Normalize recommendations
        if confidence_sum > 0:
            for rec in recommendations:
                recommendations[rec] /= confidence_sum

        # Determine final recommendation
        final_recommendation = max(recommendations, key=recommendations.get)
        final_confidence = recommendations[final_recommendation]

        # Apply volume confluence and market regime adjustments
        volume_adjustment = self._calculate_volume_confluence_adjustment(market_data)
        regime_adjustment = self._calculate_market_regime_adjustment(market_data)

        # Adjust final confidence with volume and regime factors
        adjusted_confidence = final_confidence + (volume_adjustment * self.volume_confluence_weight) + (regime_adjustment * self.market_regime_weight)
        adjusted_confidence = min(1.0, max(0.0, adjusted_confidence))  # Clamp between 0 and 1

        # Check if confidence threshold is met
        if adjusted_confidence < self.confidence_threshold or source_count < self.min_sources:
            final_recommendation = "hold"
            reason = f"Insufficient confidence ({adjusted_confidence:.2f}) or sources ({source_count})"
        else:
            reason = f"Aggregated from {source_count} sources with {adjusted_confidence:.2f} confidence (volume: {volume_adjustment:+.2f}, regime: {regime_adjustment:+.2f})"

        # Update final confidence to adjusted value
        final_confidence = adjusted_confidence

        # Verify predictions when we have a final recommendation
        if final_recommendation != "hold":
            self.model_evaluator.verify_latest_predictions(final_recommendation)

        # Evaluate model performance daily
        self._evaluate_model_performance()

        # Create final signal
        symbol = market_data.get("symbol", "BTCUSDT")
        current_price = market_data.get("price", market_data.get("close", 0))

        aggregated_signal = {
            "symbol": symbol,
            "recommendation": final_recommendation,
            "confidence": final_confidence,
            "price": current_price,
            "timestamp": datetime.datetime.now().isoformat(),
            "reason": reason,
            "source_count": source_count,
            "recommendations": recommendations
        }

        logger.info(f"Aggregated signal: {final_recommendation} with {final_confidence:.2f} confidence")
        return aggregated_signal

    def _save_analysis(self, signals, aggregated_signal):
        """
        Save analysis to file

        Args:
            signals (dict): Signals from multiple sources
            aggregated_signal (dict): Aggregated signal
        """
        try:
            # Create analysis data
            analysis_data = {
                "timestamp": datetime.datetime.now().isoformat(),
                "signals": signals,
                "aggregated_signal": aggregated_signal
            }

            # Save to file
            analysis_file = f"logs/analysis_{datetime.datetime.now().strftime('%Y%m%d')}.txt"

            with open(analysis_file, 'a') as f:
                f.write(f"\n\n--- Analysis at {datetime.datetime.now().isoformat()} ---\n")
                f.write(f"Aggregated Signal: {aggregated_signal['recommendation']} with {aggregated_signal['confidence']:.2f} confidence\n")
                f.write(f"Reason: {aggregated_signal['reason']}\n\n")

                # Write individual signals
                f.write("Individual Signals:\n")
                for source, signal in signals.items():
                    if source in ["openai", "deepseek", "qwen"] and "analysis" in signal:
                        analysis = signal["analysis"]
                        f.write(f"- {source.upper()}: {analysis.get('recommendation', 'N/A')} with {analysis.get('confidence', 'N/A')}% confidence\n")
                        f.write(f"  Explanation: {analysis.get('explanation', 'N/A')}\n")
                    elif source == "news" and "news" in signal:
                        news = signal["news"]
                        f.write(f"- NEWS: {news.get('overall_sentiment', 'N/A')} based on {news.get('article_count', 0)} articles\n")
                    elif source == "trends" and "trends" in signal:
                        trends = signal["trends"]
                        f.write(f"- TRENDS: {trends.get('trend_direction', 'N/A')}\n")

            logger.info(f"Analysis saved to {analysis_file}")

        except Exception as e:
            logger.error(f"Error saving analysis: {e}")

    def _evaluate_model_performance(self):
        """
        Evaluate model performance and adjust weights
        """
        try:
            # Check if it's time to evaluate (once per day)
            now = datetime.datetime.now()
            last_eval_file = Path("data/last_model_evaluation.txt")

            if last_eval_file.exists():
                with open(last_eval_file, 'r') as f:
                    last_eval_str = f.read().strip()
                    last_eval = datetime.datetime.fromisoformat(last_eval_str)

                    # If less than 24 hours have passed, skip evaluation
                    if (now - last_eval).total_seconds() < 86400:  # 24 hours
                        return

            # Evaluate model performance
            logger.info("Evaluating AI model performance...")
            updated_weights = self.model_evaluator.evaluate_performance()

            # Update weights
            for source in ["openai", "deepseek", "qwen"]:
                if source in updated_weights:
                    self.weights[source] = updated_weights[source]

            logger.info(f"Updated AI model weights: {self.weights}")

            # Generate daily report
            report = self.model_evaluator.generate_daily_report()
            logger.info(f"Generated daily AI performance report: {report}")

            # Update last evaluation timestamp
            os.makedirs("data", exist_ok=True)
            with open(last_eval_file, 'w') as f:
                f.write(now.isoformat())

        except Exception as e:
            logger.error(f"Error evaluating model performance: {e}")

    def generate_discussion(self, signals):
        """
        Generate a discussion between AI models about the market

        Args:
            signals (dict): Signals from multiple sources

        Returns:
            str: Discussion text
        """
        try:
            # Check if we have at least two AI signals
            ai_signals = [s for s in ["openai", "deepseek", "qwen"] if s in signals and "analysis" in signals[s]]

            if len(ai_signals) < 2:
                logger.warning("Not enough AI signals for discussion")
                return "Insufficient AI signals for a meaningful discussion."

            # Generate discussion
            discussion = "AI Models Discussion:\n\n"

            # Add introduction
            discussion += "The following is a discussion between AI models about the current market conditions:\n\n"

            # Get model weights for display
            weights = self.weights

            # Add each AI's analysis
            for source in ai_signals:
                analysis = signals[source]["analysis"]
                weight_pct = int(weights.get(source, 0) * 100)
                model_name = f"{source.upper()} (Weight: {weight_pct}%)"
                recommendation = analysis.get("recommendation", "hold").upper()
                confidence = analysis.get("confidence", 50)
                explanation = analysis.get("explanation", "No explanation provided.")

                discussion += f"{model_name}: I recommend {recommendation} with {confidence}% confidence. {explanation}\n\n"

            # Enhanced debate mode if models disagree
            recommendations = [signals[s]["analysis"].get("recommendation", "hold") for s in ai_signals]
            if len(set(recommendations)) > 1:
                discussion += "\n=== AI DEBATE MODE ACTIVATED ===\n"
                discussion += "Models disagree on the recommendation. Initiating detailed analysis debate.\n\n"

                # Get historical accuracy for each model
                performance_history = self.model_evaluator.get_performance_history()
                recent_days = sorted(performance_history.keys())[-5:] if performance_history else []

                if recent_days:
                    discussion += "Recent accuracy of each model:\n"
                    for source in ai_signals:
                        accuracies = []
                        for day in recent_days:
                            if day in performance_history and "accuracy" in performance_history[day]:
                                if source in performance_history[day]["accuracy"]:
                                    accuracies.append(performance_history[day]["accuracy"][source])

                        if accuracies:
                            avg_accuracy = sum(accuracies) / len(accuracies)
                            discussion += f"- {source.upper()}: {avg_accuracy:.2%} accuracy over last {len(accuracies)} days\n"

                    discussion += "\n"

                # Add detailed arguments from each model
                discussion += "Detailed arguments:\n\n"

                for source in ai_signals:
                    analysis = signals[source]["analysis"]
                    recommendation = analysis.get("recommendation", "hold").upper()
                    explanation = analysis.get("explanation", "No explanation provided.")

                    discussion += f"{source.upper()} argues for {recommendation}:\n"
                    discussion += f"- {explanation}\n"
                    discussion += f"- Based on {source.upper()}'s analysis of market conditions and technical indicators.\n\n"

                # Add cross-examination between models
                discussion += "Cross-examination:\n\n"

                for i, source1 in enumerate(ai_signals):
                    for source2 in ai_signals[i+1:]:
                        rec1 = signals[source1]["analysis"].get("recommendation", "hold").upper()
                        rec2 = signals[source2]["analysis"].get("recommendation", "hold").upper()

                        if rec1 != rec2:
                            discussion += f"{source1.upper()} vs {source2.upper()}:\n"
                            discussion += f"{source1.upper()}: I recommend {rec1} because {signals[source1]['analysis'].get('explanation', 'no explanation')}.\n"
                            discussion += f"{source2.upper()}: I recommend {rec2} because {signals[source2]['analysis'].get('explanation', 'no explanation')}.\n\n"

                # Add debate conclusion with weighted decision
                discussion += "Debate conclusion:\n"

                # Calculate weighted recommendation
                weighted_rec = {"buy": 0, "sell": 0, "hold": 0}
                for source in ai_signals:
                    rec = signals[source]["analysis"].get("recommendation", "hold").lower()
                    conf = signals[source]["analysis"].get("confidence", 50) / 100.0
                    weight = weights.get(source, 0)
                    weighted_rec[rec] += weight * conf

                # Normalize and find highest
                total_weight = sum(weighted_rec.values())
                if total_weight > 0:
                    for rec in weighted_rec:
                        weighted_rec[rec] /= total_weight

                    final_rec = max(weighted_rec, key=weighted_rec.get)
                    final_conf = weighted_rec[final_rec] * 100

                    discussion += f"After weighing all arguments, the final recommendation is {final_rec.upper()} with {final_conf:.1f}% confidence.\n"
                    discussion += f"This decision gives more weight to models with better historical performance.\n"
                else:
                    discussion += "Unable to reach a conclusion due to insufficient data.\n"

                discussion += "\n=== END OF DEBATE ===\n"

            # Add conclusion
            discussion += "\nFinal Conclusion:\n"

            # Calculate weighted recommendation including news and trends
            weighted_rec = {"buy": 0, "sell": 0, "hold": 0}

            # Add AI signals
            for source in ai_signals:
                rec = signals[source]["analysis"].get("recommendation", "hold").lower()
                conf = signals[source]["analysis"].get("confidence", 50) / 100.0
                weight = weights.get(source, 0)
                weighted_rec[rec] += weight * conf

            # Add news signal
            if "news" in signals and "news" in signals["news"]:
                news = signals["news"]["news"]
                sentiment = news.get("overall_sentiment", "neutral")

                # Convert sentiment to recommendation
                if sentiment == "bullish":
                    rec = "buy"
                elif sentiment == "bearish":
                    rec = "sell"
                else:
                    rec = "hold"

                weighted_rec[rec] += weights.get("news", 0.15)

            # Add trends signal
            if "trends" in signals and "trends" in signals["trends"]:
                trends = signals["trends"]["trends"]
                trend_direction = trends.get("trend_direction", "neutral")

                # Convert trend direction to recommendation
                if trend_direction == "bullish":
                    rec = "buy"
                elif trend_direction == "bearish":
                    rec = "sell"
                else:
                    rec = "hold"

                weighted_rec[rec] += weights.get("trends", 0.15)

            # Normalize and find highest
            total_weight = sum(weighted_rec.values())
            if total_weight > 0:
                for rec in weighted_rec:
                    weighted_rec[rec] /= total_weight

                final_rec = max(weighted_rec, key=weighted_rec.get)
                final_conf = weighted_rec[final_rec] * 100

                discussion += f"Based on weighted analysis of all signals (AI, news, and trends), the final recommendation is {final_rec.upper()} with {final_conf:.1f}% confidence.\n"
            else:
                discussion += "No recommendations available.\n"

            return discussion

        except Exception as e:
            logger.error(f"Error generating discussion: {e}")
            return f"Error generating discussion: {e}"

    def _calculate_volume_confluence_adjustment(self, market_data):
        """
        Calculate volume confluence adjustment for signal confidence

        Args:
            market_data (dict): Market data containing volume information

        Returns:
            float: Volume adjustment factor (-0.2 to +0.2)
        """
        try:
            # Get volume data
            current_volume = market_data.get('volume', 0)
            volume_history = market_data.get('volume_history', [])

            if not volume_history or len(volume_history) < 10:
                return 0.0

            # Calculate average volume
            avg_volume = sum(volume_history[-20:]) / min(20, len(volume_history))

            # Volume ratio
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0

            # Volume confluence scoring
            if volume_ratio >= 2.0:
                # Very high volume - strong confirmation
                adjustment = 0.15
            elif volume_ratio >= 1.5:
                # High volume - good confirmation
                adjustment = 0.10
            elif volume_ratio >= 1.2:
                # Above average volume - moderate confirmation
                adjustment = 0.05
            elif volume_ratio >= 0.8:
                # Normal volume - neutral
                adjustment = 0.0
            elif volume_ratio >= 0.5:
                # Low volume - slight negative
                adjustment = -0.05
            else:
                # Very low volume - negative confirmation
                adjustment = -0.10

            logger.debug(f"Volume confluence adjustment: {adjustment:.3f} (ratio: {volume_ratio:.2f})")
            return adjustment

        except Exception as e:
            logger.error(f"Error calculating volume confluence: {e}")
            return 0.0

    def _calculate_market_regime_adjustment(self, market_data):
        """
        Calculate market regime adjustment for signal confidence

        Args:
            market_data (dict): Market data containing regime information

        Returns:
            float: Market regime adjustment factor (-0.15 to +0.15)
        """
        try:
            # Get market regime data
            market_regime = market_data.get('market_regime', 'unclear')
            atr = market_data.get('atr', 0)
            volatility = market_data.get('volatility', 0)

            # Market regime scoring
            if market_regime == 'stable':
                # Stable market - good for trend following
                adjustment = 0.10
            elif market_regime == 'volatile':
                # Volatile market - reduce confidence
                adjustment = -0.05
            elif market_regime == 'unclear':
                # Unclear market - neutral to slightly negative
                adjustment = -0.02
            else:
                adjustment = 0.0

            # Additional volatility adjustment
            if atr > 0:
                # High ATR reduces confidence
                if atr > 0.03:  # 3% ATR
                    adjustment -= 0.05
                elif atr > 0.02:  # 2% ATR
                    adjustment -= 0.02
                elif atr < 0.01:  # 1% ATR
                    adjustment += 0.02

            # Clamp adjustment
            adjustment = max(-0.15, min(0.15, adjustment))

            logger.debug(f"Market regime adjustment: {adjustment:.3f} (regime: {market_regime}, atr: {atr:.4f})")
            return adjustment

        except Exception as e:
            logger.error(f"Error calculating market regime adjustment: {e}")
            return 0.0
