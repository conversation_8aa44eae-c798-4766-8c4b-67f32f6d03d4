info = {
    "name": "ee",
    "date_order": "MD<PERSON>",
    "january": [
        "dzove",
        "dzv"
    ],
    "february": [
        "dzd",
        "dzodze"
    ],
    "march": [
        "ted",
        "tedoxe"
    ],
    "april": [
        "afɔ",
        "afɔfĩe"
    ],
    "may": [
        "dam",
        "dama"
    ],
    "june": [
        "mas",
        "masa"
    ],
    "july": [
        "sia",
        "siamlɔm"
    ],
    "august": [
        "dea",
        "deasiamime"
    ],
    "september": [
        "any",
        "anyɔnyɔ"
    ],
    "october": [
        "kel",
        "kele"
    ],
    "november": [
        "ade",
        "adeɛmekpɔxe"
    ],
    "december": [
        "dzm",
        "dzome"
    ],
    "monday": [
        "dzo",
        "dzoɖa"
    ],
    "tuesday": [
        "bla",
        "blaɖa"
    ],
    "wednesday": [
        "kuɖ",
        "kuɖa"
    ],
    "thursday": [
        "yaw",
        "yawoɖa"
    ],
    "friday": [
        "fiɖ",
        "fiɖa"
    ],
    "saturday": [
        "mem",
        "memleɖa"
    ],
    "sunday": [
        "kɔs",
        "kɔsiɖa"
    ],
    "am": [
        "ŋdi"
    ],
    "pm": [
        "ɣetrɔ"
    ],
    "year": [
        "ƒe"
    ],
    "month": [
        "ɣleti"
    ],
    "week": [
        "kɔsiɖa ɖeka"
    ],
    "day": [
        "ŋkeke"
    ],
    "hour": [
        "gaƒoƒo"
    ],
    "minute": [
        "aɖabaƒoƒo"
    ],
    "second": [
        "sekend"
    ],
    "relative-type": {
        "0 day ago": [
            "egbe"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "ɣleti sia"
        ],
        "0 second ago": [
            "fifi"
        ],
        "0 week ago": [
            "kɔsiɖa sia"
        ],
        "0 year ago": [
            "ƒe sia"
        ],
        "1 day ago": [
            "etsɔ si va yi"
        ],
        "1 month ago": [
            "ɣleti si va yi"
        ],
        "1 week ago": [
            "kɔsiɖa si va yi"
        ],
        "1 year ago": [
            "ƒe si va yi"
        ],
        "in 1 day": [
            "etsɔ si gbɔna"
        ],
        "in 1 month": [
            "ɣleti si gbɔ na"
        ],
        "in 1 week": [
            "kɔsiɖa si gbɔ na"
        ],
        "in 1 year": [
            "ƒe si gbɔ na"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "ŋkeke (\\d+[.,]?\\d*) si va yi",
            "ŋkeke (\\d+[.,]?\\d*) si wo va yi"
        ],
        "\\1 hour ago": [
            "gaƒoƒo (\\d+[.,]?\\d*) si va yi",
            "gaƒoƒo (\\d+[.,]?\\d*) si wo va yi"
        ],
        "\\1 minute ago": [
            "aɖabaƒoƒo (\\d+[.,]?\\d*) si va yi",
            "aɖabaƒoƒo (\\d+[.,]?\\d*) si wo va yi"
        ],
        "\\1 month ago": [
            "ɣleti (\\d+[.,]?\\d*) si va yi",
            "ɣleti (\\d+[.,]?\\d*) si wo va yi"
        ],
        "\\1 second ago": [
            "sekend (\\d+[.,]?\\d*) si va yi",
            "sekend (\\d+[.,]?\\d*) si wo va yi"
        ],
        "\\1 week ago": [
            "kɔsiɖa (\\d+[.,]?\\d*) si va yi",
            "kɔsiɖa (\\d+[.,]?\\d*) si wo va yi"
        ],
        "\\1 year ago": [
            "le ƒe (\\d+[.,]?\\d*) si va yi me",
            "ƒe (\\d+[.,]?\\d*) si va yi",
            "ƒe (\\d+[.,]?\\d*) si va yi me",
            "ƒe (\\d+[.,]?\\d*) si wo va yi"
        ],
        "in \\1 day": [
            "le ŋkeke (\\d+[.,]?\\d*) me",
            "le ŋkeke (\\d+[.,]?\\d*) wo me"
        ],
        "in \\1 hour": [
            "le gaƒoƒo (\\d+[.,]?\\d*) me",
            "le gaƒoƒo (\\d+[.,]?\\d*) wo me"
        ],
        "in \\1 minute": [
            "le aɖabaƒoƒo (\\d+[.,]?\\d*) me",
            "le aɖabaƒoƒo (\\d+[.,]?\\d*) wo me"
        ],
        "in \\1 month": [
            "le ɣleti (\\d+[.,]?\\d*) me",
            "le ɣleti (\\d+[.,]?\\d*) wo me"
        ],
        "in \\1 second": [
            "le sekend (\\d+[.,]?\\d*) me",
            "le sekend (\\d+[.,]?\\d*) wo me"
        ],
        "in \\1 week": [
            "le kɔsiɖa (\\d+[.,]?\\d*) me",
            "le kɔsiɖa (\\d+[.,]?\\d*) wo me"
        ],
        "in \\1 year": [
            "le ƒe (\\d+[.,]?\\d*) me",
            "le ƒe (\\d+[.,]?\\d*) si gbɔna me"
        ]
    },
    "locale_specific": {
        "ee-TG": {
            "name": "ee-TG"
        }
    },
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
