websockets-12.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
websockets-12.0.dist-info/LICENSE,sha256=D0RRSZisognTSC0QIEqK3yqkKW_xV6NqXAki8igGMtM,1538
websockets-12.0.dist-info/METADATA,sha256=kVwktx8yYQI2ok3ME-gGr1Yz1wpeA_r85ZFvCyY7fPg,6786
websockets-12.0.dist-info/RECORD,,
websockets-12.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets-12.0.dist-info/WHEEL,sha256=aDrgWfEd5Ac7WJzHsr90rcMGiH4MHbAXoCWpyP5CEBc,102
websockets-12.0.dist-info/top_level.txt,sha256=CMpdKklxKsvZgCgyltxUWOHibZXZ1uYIVpca9xsQ8Hk,11
websockets/__init__.py,sha256=rvb0DEVwY-aXwf3GNmqrTmwku4vcKGgbkhn3tKv1OWE,5848
websockets/__main__.py,sha256=BMtbQ-dwHfmXmz2STx4nAcdliEKuj2wQXyxe23Vp1S0,4903
websockets/__pycache__/__init__.cpython-312.pyc,,
websockets/__pycache__/__main__.cpython-312.pyc,,
websockets/__pycache__/auth.cpython-312.pyc,,
websockets/__pycache__/client.cpython-312.pyc,,
websockets/__pycache__/connection.cpython-312.pyc,,
websockets/__pycache__/datastructures.cpython-312.pyc,,
websockets/__pycache__/exceptions.cpython-312.pyc,,
websockets/__pycache__/frames.cpython-312.pyc,,
websockets/__pycache__/headers.cpython-312.pyc,,
websockets/__pycache__/http.cpython-312.pyc,,
websockets/__pycache__/http11.cpython-312.pyc,,
websockets/__pycache__/imports.cpython-312.pyc,,
websockets/__pycache__/protocol.cpython-312.pyc,,
websockets/__pycache__/server.cpython-312.pyc,,
websockets/__pycache__/streams.cpython-312.pyc,,
websockets/__pycache__/typing.cpython-312.pyc,,
websockets/__pycache__/uri.cpython-312.pyc,,
websockets/__pycache__/utils.cpython-312.pyc,,
websockets/__pycache__/version.cpython-312.pyc,,
websockets/auth.py,sha256=EhKQwqisAZ4JyYsatBo6vlqzL7qR_PR_S2XDMsgNTi8,268
websockets/client.py,sha256=bVrHTaSULxE4aBENMBx04qQlKIKl9z9SRQP9DsfE0zc,12922
websockets/connection.py,sha256=dnjnwNSlmNt86T_QKEU6kXhfGA_nHUPgoSn2_iS67tc,346
websockets/datastructures.py,sha256=dxbwpJY874wR7GFPNwDAxXeS_JiupEHkPoymasv94Ao,5776
websockets/exceptions.py,sha256=O1JPwDx1Wersg-zW9fa7ns0vxF6ifYGQfK0kKQCEDjM,10701
websockets/extensions/__init__.py,sha256=HdQaQhOVkCR5RJVRKXG5Xmb6cMpAhLaKgRikYRzO64E,102
websockets/extensions/__pycache__/__init__.cpython-312.pyc,,
websockets/extensions/__pycache__/base.cpython-312.pyc,,
websockets/extensions/__pycache__/permessage_deflate.cpython-312.pyc,,
websockets/extensions/base.py,sha256=Udkz03dw1Jb85u0JAD7tWflarEX_yLqumgjY9DFXvaw,3404
websockets/extensions/permessage_deflate.py,sha256=BBQSAfHHtVBoOlfyT9kc-sD6AnBay1cr5xeMquK2fuo,25442
websockets/frames.py,sha256=8JA0-88TH4kI5uv_nOzY1gPnClZVWtr38k6tUPt7ZeU,14170
websockets/headers.py,sha256=ZSdzxVAcJ_O4fEg-PK3v7uuMWoOOR936TOdmt2ceg4U,16707
websockets/http.py,sha256=57gD-RsaSdgJzp47bk2uoV_oKIdDTWSgXptIgXBAPyE,932
websockets/http11.py,sha256=GDvgVqCxLsX5RXF6v-2OZvsluFUy0J0PsqT_kFuNPXw,12929
websockets/imports.py,sha256=9BnKfdMY-4RW_uRv_DkOhDs1i6ZhrWIp245QWZagajc,2889
websockets/legacy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/legacy/__pycache__/__init__.cpython-312.pyc,,
websockets/legacy/__pycache__/async_timeout.cpython-312.pyc,,
websockets/legacy/__pycache__/auth.cpython-312.pyc,,
websockets/legacy/__pycache__/client.cpython-312.pyc,,
websockets/legacy/__pycache__/compatibility.cpython-312.pyc,,
websockets/legacy/__pycache__/framing.cpython-312.pyc,,
websockets/legacy/__pycache__/handshake.cpython-312.pyc,,
websockets/legacy/__pycache__/http.cpython-312.pyc,,
websockets/legacy/__pycache__/protocol.cpython-312.pyc,,
websockets/legacy/__pycache__/server.cpython-312.pyc,,
websockets/legacy/async_timeout.py,sha256=udgTU_qsiWiwPp8b-NSyqvKtksunihXlMxOX91ISKkE,8805
websockets/legacy/auth.py,sha256=z7LHgWN2rd2ugDNCt2Ie17ocHT-t8a8bWmm72fFqUow,6471
websockets/legacy/client.py,sha256=m-gXDMYwyLTvjMfKOku5A7N7x1VxafdilCCvgqur-6I,26828
websockets/legacy/compatibility.py,sha256=V00zWzLIe1dpP3AYXkQfKJ4go-A5CDl1OdhjfapPQDE,272
websockets/legacy/framing.py,sha256=4AljNO-aVi0pDIt-7mKp_srw5w-HfrTarBgnJHo5a5I,5174
websockets/legacy/handshake.py,sha256=Lk_xqIE1kaU9MHip-hmAXcaWc-pCYynG_JtSukQlImI,5644
websockets/legacy/http.py,sha256=ZdG1e9R0MjO-_9FAyRCPZLJMH4Zqb5U4dSO9RL4QG2A,7139
websockets/legacy/protocol.py,sha256=AM32mnx3mr8tPvueAgwVUuh_f1qh7W0utZ4XzOiV5Jo,64988
websockets/legacy/server.py,sha256=5PiLIB41RBRcjTx8WdPkdVcAHkVwrVjMlK1owN2p4aU,45922
websockets/protocol.py,sha256=bNHydFQ87oH1q8AdQA1rDNdbnJuYxBncf_yhDTae1iM,24710
websockets/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/server.py,sha256=prrGz-U0qvCeNNybof0y_MBzpcqZGWhkrLf2JfhcIKs,21716
websockets/speedups.c,sha256=Ihn5ANJ_X-CkK83BSige-5yXWP5_2Ob90BAzx_7vJpo,6057
websockets/speedups.cp312-win_amd64.pyd,sha256=UaU21KxiaCaxU2vC9SLQQQgprNR6AoS6vISdUBolozA,11776
websockets/streams.py,sha256=PbVlfaUjsRo70fO6zOnvae-9M3Bnh2PUu43f27WHknc,4189
websockets/sync/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/sync/__pycache__/__init__.cpython-312.pyc,,
websockets/sync/__pycache__/client.cpython-312.pyc,,
websockets/sync/__pycache__/connection.cpython-312.pyc,,
websockets/sync/__pycache__/messages.cpython-312.pyc,,
websockets/sync/__pycache__/server.cpython-312.pyc,,
websockets/sync/__pycache__/utils.cpython-312.pyc,,
websockets/sync/client.py,sha256=WH3thAuQFsZZqBxhe1he8ds7C9sMqvghTwk34BNzT5Q,11593
websockets/sync/connection.py,sha256=jIyeJfApzWZapD9aY31T5APB1JEzCzfunxj8qg00I4k,30351
websockets/sync/messages.py,sha256=j7QdUVmQadUgygy9bwgdwYodJyyOAjFnyKfOLvV_4tc,9765
websockets/sync/server.py,sha256=cKj2Na1LrMnhZsj5HWSGZH5FzAgy1PZEHq0cV0u4oXo,19204
websockets/sync/utils.py,sha256=Pth5iuZHcoNphKnda238_YbES1W9Mnam8KTJYRhPphs,1197
websockets/typing.py,sha256=VDO_n0LxgD9qFEmBlY6GQBhV6z3m0cjJ_LpcyJJs7-U,1594
websockets/uri.py,sha256=dppB507un7zZrxwvrVLScSFWcqqzdwV05WOxnagsYaA,3323
websockets/utils.py,sha256=nz3tb3CXB6xtssMM2fUFDXPqHPq1XeNRFog9tAzJpPk,1201
websockets/version.py,sha256=47RtwhtsZk4CWIntVrkpvI3gVoAOOab9jozBPNBTEcQ,2829
